/**
 * 颜色工具函数
 * 用于处理颜色相关的计算和转换
 */

/**
 * 判断颜色是否为浅色（用于调整图标颜色和选中边框颜色）
 * @param color 十六进制颜色值，如 "#3B82F6"
 * @returns 是否为浅色
 */
export function isLightColor(color: string): boolean {
  // 移除 # 符号
  const hex = color.replace('#', '');
  
  // 转换为 RGB
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);
  
  // 计算亮度 (使用相对亮度公式)
  const brightness = (r * 299 + g * 587 + b * 114) / 1000;
  
  // 亮度大于 128 认为是浅色
  return brightness > 128;
}

/**
 * 根据背景色获取最佳对比度的边框颜色
 * @param backgroundColor 背景颜色
 * @returns 边框颜色
 */
export function getContrastBorderColor(backgroundColor: string): string {
  return isLightColor(backgroundColor) ? '#1f2937' : '#ffffff';
}

/**
 * 获取现代简约2.0选中边框样式
 * @param isSelected 是否选中
 * @param isEditable 是否可编辑
 * @param activityColor 活动颜色（可选，现代简约2.0中不再使用）
 * @returns 边框样式对象
 */
export function getSmartSelectionBorder(
  isSelected: boolean,
  isEditable: boolean,
  activityColor?: string
): {
  borderColor: string;
  borderWidth: string;
  boxShadow: string;
} {
  if (!isSelected || !isEditable) {
    return {
      borderColor: 'transparent',
      borderWidth: '2px',
      boxShadow: 'none'
    };
  }

  // 现代简约2.0：统一使用主题色边框，无外阴影
  // 无论是否有活动颜色，都使用统一的简约样式
  return {
    borderColor: 'var(--primary-500)',
    borderWidth: '2px',
    boxShadow: 'none' // 移除所有阴影效果，追求极简美学
  };
}
