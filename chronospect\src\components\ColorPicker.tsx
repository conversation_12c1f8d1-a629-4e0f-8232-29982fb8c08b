'use client';

import React, { useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Check, Palette } from 'lucide-react';
import { DEFAULT_COLORS } from '@/types';
import { isLightColor } from '@/utils/colorUtils';

interface ColorPickerProps {
  selectedColor: string;
  onColorSelect: (color: string) => void;
  onClose: () => void;
}

// 扩展的颜色调色板，包含更多颜色选择
const EXTENDED_COLORS = [
  // 基础调色板（来自DEFAULT_COLORS）
  ...DEFAULT_COLORS,
  
  // 扩展颜色 - 更多蓝色系
  '#1E40AF', '#2563EB', '#60A5FA', '#93C5FD',
  
  // 扩展颜色 - 更多绿色系
  '#059669', '#34D399', '#6EE7B7', '#A7F3D0',
  
  // 扩展颜色 - 更多红色系
  '#DC2626', '#F87171', '#FCA5A5', '#FECACA',
  
  // 扩展颜色 - 更多紫色系
  '#7C3AED', '#A855F7', '#C084FC', '#DDD6FE',
  
  // 扩展颜色 - 更多黄色系
  '#D97706', '#FBBF24', '#FCD34D', '#FDE68A',
  
  // 扩展颜色 - 中性色系
  '#374151', '#4B5563', '#9CA3AF', '#D1D5DB',
  
  // 扩展颜色 - 特殊色彩
  '#BE185D', '#E11D48', '#F43F5E', '#FB7185', // 玫瑰色系
  '#0891B2', '#0E7490', '#155E75', '#164E63', // 青色系
];

// 颜色分组
const COLOR_GROUPS = [
  {
    name: '推荐颜色',
    colors: DEFAULT_COLORS
  },
  {
    name: '蓝色系',
    colors: ['#1E40AF', '#2563EB', '#3B82F6', '#60A5FA', '#93C5FD']
  },
  {
    name: '绿色系',
    colors: ['#059669', '#10B981', '#34D399', '#6EE7B7', '#A7F3D0']
  },
  {
    name: '红色系',
    colors: ['#DC2626', '#EF4444', '#F87171', '#FCA5A5', '#FECACA']
  },
  {
    name: '紫色系',
    colors: ['#7C3AED', '#8B5CF6', '#A855F7', '#C084FC', '#DDD6FE']
  },
  {
    name: '黄色系',
    colors: ['#D97706', '#F59E0B', '#FBBF24', '#FCD34D', '#FDE68A']
  },
  {
    name: '中性色',
    colors: ['#374151', '#4B5563', '#6B7280', '#9CA3AF', '#D1D5DB']
  }
];

export function ColorPicker({ selectedColor, onColorSelect, onClose }: ColorPickerProps) {
  const pickerRef = useRef<HTMLDivElement>(null);

  // 点击外部关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (pickerRef.current && !pickerRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  // 处理颜色选择
  const handleColorClick = (color: string) => {
    onColorSelect(color);
  };

  return (
    <div ref={pickerRef} className="w-full max-h-80 overflow-y-auto">
        {/* 头部 */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Palette className="w-4 h-4 text-gray-600" />
            <h3 className="text-sm font-medium text-gray-900">选择颜色</h3>
          </div>
          <div className="text-xs text-gray-500">
            当前: {selectedColor}
          </div>
        </div>

        {/* 颜色组 */}
        <div className="space-y-4">
          {COLOR_GROUPS.map((group, groupIndex) => (
            <div key={group.name} className="space-y-2">
              <h4 className="text-xs font-medium text-gray-700 uppercase tracking-wide">
                {group.name}
              </h4>
              <div className="grid grid-cols-5 gap-2">
                {group.colors.map((color, colorIndex) => (
                  <ColorSwatch
                    key={`${groupIndex}-${colorIndex}`}
                    color={color}
                    isSelected={selectedColor === color}
                    onClick={() => handleColorClick(color)}
                  />
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* 自定义颜色输入 */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <label className="block text-xs font-medium text-gray-700 mb-2">
            自定义颜色
          </label>
          <div className="flex items-center space-x-2">
            <input
              type="color"
              value={selectedColor}
              onChange={(e) => handleColorClick(e.target.value)}
              className="w-8 h-8 rounded border border-gray-300 cursor-pointer"
            />
            <input
              type="text"
              value={selectedColor}
              onChange={(e) => {
                const value = e.target.value;
                // 简单的颜色格式验证
                if (/^#[0-9A-Fa-f]{6}$/.test(value)) {
                  handleColorClick(value);
                }
              }}
              className="flex-1 px-2 py-1 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              placeholder="#3B82F6"
              maxLength={7}
            />
          </div>
        </div>
    </div>
  );
}

// 颜色色块组件
interface ColorSwatchProps {
  color: string;
  isSelected: boolean;
  onClick: () => void;
}

function ColorSwatch({ color, isSelected, onClick }: ColorSwatchProps) {
  return (
    <motion.button
      onClick={onClick}
      className={`relative w-10 h-10 rounded-lg border-2 transition-all duration-200 ${
        isSelected 
          ? 'border-gray-900 shadow-md' 
          : 'border-gray-200 hover:border-gray-300 hover:shadow-sm'
      }`}
      style={{ backgroundColor: color }}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      title={color}
    >
      {isSelected && (
        <motion.div
          className="absolute inset-0 flex items-center justify-center"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ type: "spring", stiffness: 400, damping: 25 }}
        >
          <Check 
            className="w-4 h-4 text-white drop-shadow-sm" 
            style={{
              // 根据背景颜色调整图标颜色
              color: isLightColor(color) ? '#000000' : '#ffffff'
            }}
          />
        </motion.div>
      )}
    </motion.button>
  );
}

// isLightColor函数已移动到 @/utils/colorUtils 中统一管理
