"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/TimeBlock.tsx":
/*!**************************************!*\
  !*** ./src/components/TimeBlock.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TimeBlock: () => (/* binding */ TimeBlock)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _stores_useAppStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/stores/useAppStore */ \"(app-pages-browser)/./src/stores/useAppStore.ts\");\n/* harmony import */ var _utils_timeUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/timeUtils */ \"(app-pages-browser)/./src/utils/timeUtils.ts\");\n/* harmony import */ var _utils_colorUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/colorUtils */ \"(app-pages-browser)/./src/utils/colorUtils.ts\");\n/* harmony import */ var _hooks_useIsClient__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useIsClient */ \"(app-pages-browser)/./src/hooks/useIsClient.ts\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* __next_internal_client_entry_do_not_use__ TimeBlock auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction TimeBlock(param) {\n    let { block, isSelected, onClick, onMouseDown, onMouseEnter } = param;\n    _s();\n    const { getActivityById, currentTimeSlot } = (0,_stores_useAppStore__WEBPACK_IMPORTED_MODULE_2__.useAppStore)();\n    const [showTooltip, setShowTooltip] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isClient = (0,_hooks_useIsClient__WEBPACK_IMPORTED_MODULE_5__.useIsClient)();\n    // 记忆化计算是否可编辑（响应式，依赖currentTimeSlot状态）\n    const isEditable = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"TimeBlock.useMemo[isEditable]\": ()=>{\n            return (0,_utils_timeUtils__WEBPACK_IMPORTED_MODULE_3__.isTimeSlotEditable)(block.date, block.timeSlot);\n        }\n    }[\"TimeBlock.useMemo[isEditable]\"], [\n        block.date,\n        block.timeSlot,\n        currentTimeSlot\n    ]);\n    // 只有可编辑的时间槽才显示活动，未来时间的活动数据不应该显示\n    const activity = block.activityId && isEditable ? getActivityById(block.activityId) : null;\n    // 获取图标组件\n    const getIconComponent = (iconName)=>{\n        if (!iconName) return null;\n        const IconComponent = lucide_react__WEBPACK_IMPORTED_MODULE_6__[iconName];\n        return IconComponent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n            size: 16\n        }, void 0, false, {\n            fileName: \"D:\\\\git_resp\\\\Unfinished\\\\time_blocks\\\\chronospect\\\\src\\\\components\\\\TimeBlock.tsx\",\n            lineNumber: 44,\n            columnNumber: 28\n        }, this) : null;\n    };\n    // 记忆化计算样式 - 现代简约2.0版本\n    const blockStyle = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"TimeBlock.useMemo[blockStyle]\": ()=>{\n            const baseStyle = {\n                opacity: isEditable ? 1 : 0.4,\n                cursor: isEditable ? 'pointer' : 'not-allowed'\n            };\n            // 获取现代简约选中边框样式\n            const selectionBorder = (0,_utils_colorUtils__WEBPACK_IMPORTED_MODULE_4__.getSmartSelectionBorder)(isSelected, isEditable, activity === null || activity === void 0 ? void 0 : activity.color);\n            // 现代简约2.0：选中时统一使用柔和中性色背景\n            if (isSelected && isEditable) {\n                return {\n                    ...baseStyle,\n                    backgroundColor: 'var(--neutral-100)',\n                    borderColor: selectionBorder.borderColor,\n                    borderWidth: selectionBorder.borderWidth,\n                    boxShadow: selectionBorder.boxShadow\n                };\n            }\n            // 已填充活动的时间块（未选中时）\n            if (activity) {\n                return {\n                    ...baseStyle,\n                    backgroundColor: activity.color,\n                    borderColor: activity.color,\n                    borderWidth: '2px',\n                    boxShadow: isEditable ? 'var(--shadow-sm)' : 'none'\n                };\n            }\n            // 空白时间块（未选中时）\n            return {\n                ...baseStyle,\n                backgroundColor: isEditable ? 'var(--neutral-50)' : 'var(--neutral-100)',\n                borderColor: isEditable ? 'var(--neutral-200)' : 'var(--neutral-300)',\n                borderWidth: '2px',\n                boxShadow: isEditable ? 'var(--shadow-sm)' : 'none'\n            };\n        }\n    }[\"TimeBlock.useMemo[blockStyle]\"], [\n        isEditable,\n        activity,\n        isSelected\n    ]);\n    const isActive = !!activity;\n    // 现代简约2.0：统一白色图标文字美学\n    const textColor = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"TimeBlock.useMemo[textColor]\": ()=>{\n            if (isSelected && isEditable) {\n                // 选中时使用深色文字，确保在浅色背景(--neutral-100)上的可读性\n                return 'var(--neutral-700)';\n            }\n            if (isActive) {\n                // 活动时间块统一使用白色图标和文字，创造一致的视觉体验\n                return 'white';\n            }\n            // 空白时间块使用中性色文字\n            return 'var(--neutral-400)';\n        }\n    }[\"TimeBlock.useMemo[textColor]\"], [\n        isSelected,\n        isEditable,\n        isActive\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n        className: \"relative flex items-center justify-center\",\n        style: {\n            height: '60px',\n            border: \"\".concat(blockStyle.borderWidth || '2px', \" solid\"),\n            borderRadius: 'var(--radius-lg)',\n            transition: 'all var(--duration-fast) var(--ease-out)',\n            ...blockStyle\n        },\n        whileHover: isEditable ? {\n            scale: 1.02,\n            y: -1,\n            boxShadow: 'var(--shadow-md)'\n        } : {},\n        whileTap: isEditable ? {\n            scale: 0.98\n        } : {},\n        tabIndex: isClient && isEditable ? 0 : undefined,\n        onClick: isEditable ? onClick : undefined,\n        onMouseDown: isEditable ? onMouseDown : undefined,\n        onMouseEnter: ()=>{\n            if (isEditable) {\n                onMouseEnter();\n            } else {\n                setShowTooltip(true);\n            }\n        },\n        onMouseLeave: ()=>{\n            setShowTooltip(false);\n        },\n        initial: {\n            opacity: 0,\n            scale: 0.9\n        },\n        animate: {\n            scale: 1,\n            // 添加平滑的可编辑状态过渡\n            ...blockStyle,\n            opacity: 1\n        },\n        transition: {\n            duration: 0.15,\n            ease: [\n                0.4,\n                0,\n                0.2,\n                1\n            ],\n            // 为透明度和颜色变化添加更长的过渡时间\n            opacity: {\n                duration: 0.3\n            },\n            backgroundColor: {\n                duration: 0.3\n            },\n            borderColor: {\n                duration: 0.3\n            }\n        },\n        children: [\n            activity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                className: \"flex flex-col items-center justify-center text-center\",\n                style: {\n                    padding: '0 var(--spacing-1)'\n                },\n                initial: {\n                    opacity: 0,\n                    scale: 0.8\n                },\n                animate: {\n                    opacity: 1,\n                    scale: 1\n                },\n                transition: {\n                    delay: 0.1,\n                    duration: 0.2\n                },\n                children: [\n                    activity.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: textColor,\n                            marginBottom: 'var(--spacing-1)'\n                        },\n                        children: getIconComponent(activity.icon)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\git_resp\\\\Unfinished\\\\time_blocks\\\\chronospect\\\\src\\\\components\\\\TimeBlock.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: 'var(--font-size-xs)',\n                            fontWeight: 'var(--font-weight-medium)',\n                            color: textColor,\n                            overflow: 'hidden',\n                            textOverflow: 'ellipsis',\n                            whiteSpace: 'nowrap',\n                            maxWidth: '100%'\n                        },\n                        children: activity.name\n                    }, void 0, false, {\n                        fileName: \"D:\\\\git_resp\\\\Unfinished\\\\time_blocks\\\\chronospect\\\\src\\\\components\\\\TimeBlock.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\git_resp\\\\Unfinished\\\\time_blocks\\\\chronospect\\\\src\\\\components\\\\TimeBlock.tsx\",\n                lineNumber: 153,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute pointer-events-none\",\n                style: {\n                    bottom: '-24px',\n                    left: '50%',\n                    transform: 'translateX(-50%)',\n                    opacity: 0,\n                    transition: 'opacity var(--duration-fast) var(--ease-out)',\n                    zIndex: 10\n                },\n                onMouseEnter: (e)=>{\n                    e.currentTarget.style.opacity = '1';\n                },\n                onMouseLeave: (e)=>{\n                    e.currentTarget.style.opacity = '0';\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: 'var(--neutral-800)',\n                        color: 'white',\n                        fontSize: 'var(--font-size-xs)',\n                        padding: 'var(--spacing-1) var(--spacing-2)',\n                        borderRadius: 'var(--radius-md)',\n                        boxShadow: 'var(--shadow-lg)',\n                        whiteSpace: 'nowrap'\n                    },\n                    children: [\n                        block.startTime,\n                        \" - \",\n                        block.endTime\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\git_resp\\\\Unfinished\\\\time_blocks\\\\chronospect\\\\src\\\\components\\\\TimeBlock.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\git_resp\\\\Unfinished\\\\time_blocks\\\\chronospect\\\\src\\\\components\\\\TimeBlock.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, this),\n            !isEditable && showTooltip && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                className: \"absolute pointer-events-none\",\n                style: {\n                    bottom: '-36px',\n                    left: '50%',\n                    transform: 'translateX(-50%)',\n                    zIndex: 20\n                },\n                initial: {\n                    opacity: 0,\n                    y: -5\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                exit: {\n                    opacity: 0,\n                    y: -5\n                },\n                transition: {\n                    duration: 0.2\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: 'var(--neutral-800)',\n                        color: 'white',\n                        fontSize: 'var(--font-size-xs)',\n                        padding: 'var(--spacing-2) var(--spacing-3)',\n                        borderRadius: 'var(--radius-md)',\n                        boxShadow: 'var(--shadow-lg)',\n                        whiteSpace: 'nowrap'\n                    },\n                    children: \"只能记录已经过去的时间\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\git_resp\\\\Unfinished\\\\time_blocks\\\\chronospect\\\\src\\\\components\\\\TimeBlock.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\git_resp\\\\Unfinished\\\\time_blocks\\\\chronospect\\\\src\\\\components\\\\TimeBlock.tsx\",\n                lineNumber: 222,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\git_resp\\\\Unfinished\\\\time_blocks\\\\chronospect\\\\src\\\\components\\\\TimeBlock.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, this);\n}\n_s(TimeBlock, \"qWasZtJIrRIeU6zXASjiw7vjVFg=\", false, function() {\n    return [\n        _stores_useAppStore__WEBPACK_IMPORTED_MODULE_2__.useAppStore,\n        _hooks_useIsClient__WEBPACK_IMPORTED_MODULE_5__.useIsClient\n    ];\n});\n_c = TimeBlock;\nvar _c;\n$RefreshReg$(_c, \"TimeBlock\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TimeBlock.tsx\n"));

/***/ })

});