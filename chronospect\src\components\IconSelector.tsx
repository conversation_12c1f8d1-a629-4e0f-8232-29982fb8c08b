'use client';

import React, { useRef, useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { Search, Check } from 'lucide-react';
import * as LucideIcons from 'lucide-react';

interface IconSelectorProps {
  selectedIcon: string;
  onIconSelect: (icon: string) => void;
  onClose: () => void;
}

// 常用图标分组
const ICON_GROUPS = [
  {
    name: '工作相关',
    icons: [
      'Laptop', 'Monitor', 'Code', 'FileText', 'Briefcase', 
      'Users', 'MessageSquare', 'Phone', 'Mail', 'Calendar'
    ]
  },
  {
    name: '学习教育',
    icons: [
      'BookOpen', 'GraduationCap', 'PenTool', 'Edit3', 'FileEdit',
      'Library', 'Brain', 'Lightbulb', 'Target', 'Award'
    ]
  },
  {
    name: '运动健康',
    icons: [
      'Dumbbell', 'Activity', 'Heart', 'Bike', 'Footprints',
      'Apple', 'Zap', 'Sun', 'Moon', 'Droplets'
    ]
  },
  {
    name: '生活日常',
    icons: [
      'Coffee', 'Utensils', 'Home', 'Car', 'ShoppingCart',
      'Music', 'Gamepad2', 'Tv', 'Camera', 'Gift'
    ]
  },
  {
    name: '休闲娱乐',
    icons: [
      'Film', 'Headphones', 'Guitar', 'Palette', 'Brush',
      'Book', 'Puzzle', 'Dice1', 'PartyPopper', 'Smile'
    ]
  },
  {
    name: '基础图形',
    icons: [
      'Circle', 'Square', 'Triangle', 'Star', 'Heart',
      'Diamond', 'Hexagon', 'Octagon', 'Plus', 'Minus'
    ]
  }
];

// 获取所有可用图标
const ALL_ICONS = ICON_GROUPS.flatMap(group => group.icons);

export function IconSelector({ selectedIcon, onIconSelect, onClose }: IconSelectorProps) {
  const selectorRef = useRef<HTMLDivElement>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredGroups, setFilteredGroups] = useState(ICON_GROUPS);

  // 点击外部关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectorRef.current && !selectorRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  // 搜索过滤
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredGroups(ICON_GROUPS);
      return;
    }

    const filtered = ICON_GROUPS.map(group => ({
      ...group,
      icons: group.icons.filter(icon => 
        icon.toLowerCase().includes(searchTerm.toLowerCase())
      )
    })).filter(group => group.icons.length > 0);

    setFilteredGroups(filtered);
  }, [searchTerm]);

  /**
   * 图标网格列优先索引转换函数
   *
   * 目标：将原始按行排序的图标数组转换为按列优先显示的顺序，
   * 使用户的视觉选择顺序（从左到右、从上到下）与代码索引逻辑保持一致。
   *
   * 与项目时间网格系统保持一致的转换逻辑：列优先排序
   *
   * @param icons 原始图标数组（按行排序）
   * @returns 转换后的图标数组（按列优先排序）
   *
   * 示例：
   * 原始数组: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J']
   * 当前显示（按行）: A B C D
   *                  E F G H
   *                  I J
   *
   * 转换后显示（按列）: A E I B
   *                   F J C G
   *                   D H
   *
   * 用户选择顺序：A→E→I→B→F→J→C→G→D→H（按列优先）
   */
  const convertToColumnFirstOrder = (icons: string[]) => {
    const cols = 4; // 4列网格布局（与CSS grid-cols-4保持一致）
    const rows = Math.ceil(icons.length / cols); // 计算需要的行数
    const reorderedIcons: string[] = [];

    // 按列优先顺序重新排列图标
    // 外层循环：遍历每一列
    // 内层循环：遍历每一行
    for (let col = 0; col < cols; col++) {
      for (let row = 0; row < rows; row++) {
        const originalIndex = row * cols + col; // 计算原始按行排序的索引位置
        if (originalIndex < icons.length) {
          reorderedIcons.push(icons[originalIndex]);
        }
      }
    }

    return reorderedIcons;
  };

  // 处理图标选择
  const handleIconClick = (icon: string) => {
    onIconSelect(icon);
  };

  // 渲染图标组件
  const renderIcon = (iconName: string, size: number = 20) => {
    const IconComponent = (LucideIcons as any)[iconName];
    if (!IconComponent) {
      return <div className="w-5 h-5 bg-gray-200 rounded" />;
    }
    return <IconComponent size={size} />;
  };

  return (
    <div
      ref={selectorRef}
      className="w-full max-h-[400px] overflow-hidden flex flex-col"
    >
        {/* 头部和搜索 */}
        <div className="space-y-3 mb-4">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium text-gray-900">选择图标</h3>
            <div className="text-xs text-gray-500">
              当前: {selectedIcon}
            </div>
          </div>
          
          {/* 搜索框 */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="搜索图标..."
            />
          </div>
        </div>

        {/* 图标列表 */}
        <div className="flex-1 overflow-y-auto space-y-6">
          {filteredGroups.length > 0 ? (
            filteredGroups.map((group, groupIndex) => (
              <div key={group.name} className="space-y-3">
                <h4 className="text-xs font-semibold text-gray-800 uppercase tracking-wide border-b border-gray-100 pb-1">
                  {group.name}
                </h4>
                <div className="grid grid-cols-4 gap-3">
                  {/* 应用列优先索引转换，使图标按列排序显示 */}
                  {convertToColumnFirstOrder(group.icons).map((iconName, displayIndex) => (
                    <IconButton
                      key={`${group.name}-${iconName}-${displayIndex}`}
                      iconName={iconName}
                      isSelected={selectedIcon === iconName}
                      onClick={() => handleIconClick(iconName)}
                      renderIcon={renderIcon}
                    />
                  ))}
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8 text-gray-400">
              <Search className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">未找到匹配的图标</p>
              <p className="text-xs mt-1">尝试使用其他关键词搜索</p>
            </div>
          )}
        </div>

        {/* 当前选中的图标预览 */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-xs text-gray-600">当前选中:</span>
              <div className="flex items-center space-x-1">
                {renderIcon(selectedIcon, 16)}
                <span className="text-xs font-medium text-gray-900">{selectedIcon}</span>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-xs text-blue-600 hover:text-blue-700 font-medium"
            >
              确认选择
            </button>
          </div>
        </div>
    </div>
  );
}

// 图标按钮组件
interface IconButtonProps {
  iconName: string;
  isSelected: boolean;
  onClick: () => void;
  renderIcon: (iconName: string, size?: number) => React.ReactNode;
}

function IconButton({ iconName, isSelected, onClick, renderIcon }: IconButtonProps) {
  return (
    <motion.button
      onClick={onClick}
      className={`relative w-12 h-12 rounded-xl border-2 flex items-center justify-center transition-all duration-200 ${
        isSelected
          ? 'border-blue-500 bg-blue-50 text-blue-600 shadow-md'
          : 'border-gray-200 hover:border-blue-300 hover:bg-blue-50 hover:shadow-sm text-gray-600'
      }`}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      title={iconName}
    >
      {renderIcon(iconName, 22)}

      {isSelected && (
        <motion.div
          className="absolute -top-1.5 -right-1.5 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center shadow-lg"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ type: "spring", stiffness: 400, damping: 25 }}
        >
          <Check className="w-3 h-3 text-white" />
        </motion.div>
      )}
    </motion.button>
  );
}
