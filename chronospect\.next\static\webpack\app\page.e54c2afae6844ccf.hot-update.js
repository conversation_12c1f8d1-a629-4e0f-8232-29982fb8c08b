"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/TimeBlock.tsx":
/*!**************************************!*\
  !*** ./src/components/TimeBlock.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TimeBlock: () => (/* binding */ TimeBlock)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _stores_useAppStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/stores/useAppStore */ \"(app-pages-browser)/./src/stores/useAppStore.ts\");\n/* harmony import */ var _utils_timeUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/timeUtils */ \"(app-pages-browser)/./src/utils/timeUtils.ts\");\n/* harmony import */ var _utils_colorUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/colorUtils */ \"(app-pages-browser)/./src/utils/colorUtils.ts\");\n/* harmony import */ var _hooks_useIsClient__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useIsClient */ \"(app-pages-browser)/./src/hooks/useIsClient.ts\");\n/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* __next_internal_client_entry_do_not_use__ TimeBlock auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction TimeBlock(param) {\n    let { block, isSelected, onClick, onMouseDown, onMouseEnter } = param;\n    _s();\n    const { getActivityById, currentTimeSlot } = (0,_stores_useAppStore__WEBPACK_IMPORTED_MODULE_2__.useAppStore)();\n    const [showTooltip, setShowTooltip] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isClient = (0,_hooks_useIsClient__WEBPACK_IMPORTED_MODULE_5__.useIsClient)();\n    // 记忆化计算是否可编辑（响应式，依赖currentTimeSlot状态）\n    const isEditable = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"TimeBlock.useMemo[isEditable]\": ()=>{\n            return (0,_utils_timeUtils__WEBPACK_IMPORTED_MODULE_3__.isTimeSlotEditable)(block.date, block.timeSlot);\n        }\n    }[\"TimeBlock.useMemo[isEditable]\"], [\n        block.date,\n        block.timeSlot,\n        currentTimeSlot\n    ]);\n    // 只有可编辑的时间槽才显示活动，未来时间的活动数据不应该显示\n    const activity = block.activityId && isEditable ? getActivityById(block.activityId) : null;\n    // 获取图标组件\n    const getIconComponent = (iconName)=>{\n        if (!iconName) return null;\n        const IconComponent = lucide_react__WEBPACK_IMPORTED_MODULE_6__[iconName];\n        return IconComponent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n            size: 16\n        }, void 0, false, {\n            fileName: \"D:\\\\git_resp\\\\Unfinished\\\\time_blocks\\\\chronospect\\\\src\\\\components\\\\TimeBlock.tsx\",\n            lineNumber: 44,\n            columnNumber: 28\n        }, this) : null;\n    };\n    // 记忆化计算样式 - 现代简约2.0版本\n    const blockStyle = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"TimeBlock.useMemo[blockStyle]\": ()=>{\n            const baseStyle = {\n                opacity: isEditable ? 1 : 0.4,\n                cursor: isEditable ? 'pointer' : 'not-allowed'\n            };\n            // 获取现代简约选中边框样式\n            const selectionBorder = (0,_utils_colorUtils__WEBPACK_IMPORTED_MODULE_4__.getSmartSelectionBorder)(isSelected, isEditable, activity === null || activity === void 0 ? void 0 : activity.color);\n            // 现代简约2.0：选中时统一使用柔和中性色背景\n            if (isSelected && isEditable) {\n                return {\n                    ...baseStyle,\n                    backgroundColor: 'var(--neutral-100)',\n                    borderColor: selectionBorder.borderColor,\n                    borderWidth: selectionBorder.borderWidth,\n                    boxShadow: selectionBorder.boxShadow\n                };\n            }\n            // 已填充活动的时间块（未选中时）\n            if (activity) {\n                return {\n                    ...baseStyle,\n                    backgroundColor: activity.color,\n                    borderColor: activity.color,\n                    borderWidth: '2px',\n                    boxShadow: isEditable ? 'var(--shadow-sm)' : 'none'\n                };\n            }\n            // 空白时间块（未选中时）\n            return {\n                ...baseStyle,\n                backgroundColor: isEditable ? 'var(--neutral-50)' : 'var(--neutral-100)',\n                borderColor: isEditable ? 'var(--neutral-200)' : 'var(--neutral-300)',\n                borderWidth: '2px',\n                boxShadow: isEditable ? 'var(--shadow-sm)' : 'none'\n            };\n        }\n    }[\"TimeBlock.useMemo[blockStyle]\"], [\n        isEditable,\n        activity,\n        isSelected\n    ]);\n    const isActive = !!activity;\n    // 现代简约2.0：统一白色图标文字美学\n    const textColor = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"TimeBlock.useMemo[textColor]\": ()=>{\n            if (isSelected && isEditable) {\n                // 选中时使用深色文字，确保在浅色背景(--neutral-100)上的可读性\n                return 'var(--neutral-700)';\n            }\n            if (isActive) {\n                // 活动时间块统一使用白色图标和文字，创造一致的视觉体验\n                return 'white';\n            }\n            // 空白时间块使用中性色文字\n            return 'var(--neutral-400)';\n        }\n    }[\"TimeBlock.useMemo[textColor]\"], [\n        isSelected,\n        isEditable,\n        isActive\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n        className: \"relative flex items-center justify-center\",\n        style: {\n            height: '60px',\n            border: \"\".concat(blockStyle.borderWidth || '2px', \" solid\"),\n            borderRadius: 'var(--radius-lg)',\n            transition: 'all var(--duration-fast) var(--ease-out)',\n            ...blockStyle\n        },\n        whileHover: isEditable ? {\n            scale: 1.02,\n            y: -1,\n            boxShadow: 'var(--shadow-md)'\n        } : {},\n        whileTap: isEditable ? {\n            scale: 0.98\n        } : {},\n        tabIndex: isClient && isEditable ? 0 : undefined,\n        onClick: isEditable ? onClick : undefined,\n        onMouseDown: isEditable ? onMouseDown : undefined,\n        onMouseEnter: ()=>{\n            if (isEditable) {\n                onMouseEnter();\n            } else {\n                setShowTooltip(true);\n            }\n        },\n        onMouseLeave: ()=>{\n            setShowTooltip(false);\n        },\n        initial: {\n            opacity: 0,\n            scale: 0.9\n        },\n        animate: {\n            scale: 1,\n            // 添加平滑的可编辑状态过渡\n            ...blockStyle,\n            opacity: 1\n        },\n        transition: {\n            duration: 0.15,\n            ease: [\n                0.4,\n                0,\n                0.2,\n                1\n            ],\n            // 为透明度和颜色变化添加更长的过渡时间\n            opacity: {\n                duration: 0.3\n            },\n            backgroundColor: {\n                duration: 0.3\n            },\n            borderColor: {\n                duration: 0.3\n            }\n        },\n        children: [\n            activity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                className: \"flex flex-col items-center justify-center text-center\",\n                style: {\n                    padding: '0 var(--spacing-1)'\n                },\n                initial: {\n                    opacity: 0,\n                    scale: 0.8\n                },\n                animate: {\n                    opacity: 1,\n                    scale: 1\n                },\n                transition: {\n                    delay: 0.1,\n                    duration: 0.2\n                },\n                children: [\n                    activity.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: textColor,\n                            marginBottom: 'var(--spacing-1)'\n                        },\n                        children: getIconComponent(activity.icon)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\git_resp\\\\Unfinished\\\\time_blocks\\\\chronospect\\\\src\\\\components\\\\TimeBlock.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: 'var(--font-size-xs)',\n                            fontWeight: 'var(--font-weight-medium)',\n                            color: textColor,\n                            overflow: 'hidden',\n                            textOverflow: 'ellipsis',\n                            whiteSpace: 'nowrap',\n                            maxWidth: '100%'\n                        },\n                        children: activity.name\n                    }, void 0, false, {\n                        fileName: \"D:\\\\git_resp\\\\Unfinished\\\\time_blocks\\\\chronospect\\\\src\\\\components\\\\TimeBlock.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\git_resp\\\\Unfinished\\\\time_blocks\\\\chronospect\\\\src\\\\components\\\\TimeBlock.tsx\",\n                lineNumber: 153,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute pointer-events-none\",\n                style: {\n                    bottom: '-24px',\n                    left: '50%',\n                    transform: 'translateX(-50%)',\n                    opacity: 0,\n                    transition: 'opacity var(--duration-fast) var(--ease-out)',\n                    zIndex: 10\n                },\n                onMouseEnter: (e)=>{\n                    e.currentTarget.style.opacity = '1';\n                },\n                onMouseLeave: (e)=>{\n                    e.currentTarget.style.opacity = '0';\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: 'var(--neutral-800)',\n                        color: 'white',\n                        fontSize: 'var(--font-size-xs)',\n                        padding: 'var(--spacing-1) var(--spacing-2)',\n                        borderRadius: 'var(--radius-md)',\n                        boxShadow: 'var(--shadow-lg)',\n                        whiteSpace: 'nowrap'\n                    },\n                    children: [\n                        block.startTime,\n                        \" - \",\n                        block.endTime\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\git_resp\\\\Unfinished\\\\time_blocks\\\\chronospect\\\\src\\\\components\\\\TimeBlock.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\git_resp\\\\Unfinished\\\\time_blocks\\\\chronospect\\\\src\\\\components\\\\TimeBlock.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, this),\n            !isEditable && showTooltip && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                className: \"absolute pointer-events-none\",\n                style: {\n                    bottom: '-36px',\n                    left: '50%',\n                    transform: 'translateX(-50%)',\n                    zIndex: 20\n                },\n                initial: {\n                    opacity: 0,\n                    y: -5\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                exit: {\n                    opacity: 0,\n                    y: -5\n                },\n                transition: {\n                    duration: 0.2\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: 'var(--neutral-800)',\n                        color: 'white',\n                        fontSize: 'var(--font-size-xs)',\n                        padding: 'var(--spacing-2) var(--spacing-3)',\n                        borderRadius: 'var(--radius-md)',\n                        boxShadow: 'var(--shadow-lg)',\n                        whiteSpace: 'nowrap'\n                    },\n                    children: \"只能记录已经过去的时间\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\git_resp\\\\Unfinished\\\\time_blocks\\\\chronospect\\\\src\\\\components\\\\TimeBlock.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\git_resp\\\\Unfinished\\\\time_blocks\\\\chronospect\\\\src\\\\components\\\\TimeBlock.tsx\",\n                lineNumber: 222,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\git_resp\\\\Unfinished\\\\time_blocks\\\\chronospect\\\\src\\\\components\\\\TimeBlock.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, this);\n}\n_s(TimeBlock, \"qWasZtJIrRIeU6zXASjiw7vjVFg=\", false, function() {\n    return [\n        _stores_useAppStore__WEBPACK_IMPORTED_MODULE_2__.useAppStore,\n        _hooks_useIsClient__WEBPACK_IMPORTED_MODULE_5__.useIsClient\n    ];\n});\n_c = TimeBlock;\nvar _c;\n$RefreshReg$(_c, \"TimeBlock\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TimeBlock.tsx\n"));

/***/ })

});