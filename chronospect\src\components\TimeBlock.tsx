'use client';

import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { useAppStore } from '@/stores/useAppStore';
import { TimeBlock as TimeBlockType } from '@/types';
import { isTimeSlotEditable } from '@/utils/timeUtils';
import { getSmartSelectionBorder } from '@/utils/colorUtils';
import { useIsClient } from '@/hooks/useIsClient';
import * as LucideIcons from 'lucide-react';

interface TimeBlockProps {
  block: TimeBlockType;
  isSelected: boolean;
  onClick: (event: React.MouseEvent) => void;
  onMouseDown: (event: React.MouseEvent) => void;
  onMouseEnter: () => void;
}

export function TimeBlock({
  block,
  isSelected,
  onClick,
  onMouseDown,
  onMouseEnter
}: TimeBlockProps) {
  const { getActivityById, currentTimeSlot } = useAppStore();
  const [showTooltip, setShowTooltip] = useState(false);
  const isClient = useIsClient();

  // 记忆化计算是否可编辑（响应式，依赖currentTimeSlot状态）
  const isEditable = useMemo(() => {
    return isTimeSlotEditable(block.date, block.timeSlot);
  }, [block.date, block.timeSlot, currentTimeSlot]);

  // 只有可编辑的时间槽才显示活动，未来时间的活动数据不应该显示
  const activity = (block.activityId && isEditable) ? getActivityById(block.activityId) : null;
  
  // 获取图标组件
  const getIconComponent = (iconName?: string) => {
    if (!iconName) return null;
    
    const IconComponent = (LucideIcons as any)[iconName];
    return IconComponent ? <IconComponent size={16} /> : null;
  };

  // 记忆化计算样式 - 现代简约2.0版本
  const blockStyle = useMemo(() => {
    const baseStyle = {
      opacity: isEditable ? 1 : 0.4, // 不可编辑时降低透明度
      cursor: isEditable ? 'pointer' : 'not-allowed', // 不可编辑时显示禁用光标
    };

    // 获取现代简约选中边框样式
    const selectionBorder = getSmartSelectionBorder(isSelected, isEditable, activity?.color);

    // 现代简约2.0：选中时统一使用柔和中性色背景
    if (isSelected && isEditable) {
      return {
        ...baseStyle,
        backgroundColor: 'var(--neutral-100)', // 统一的柔和中性色背景
        borderColor: selectionBorder.borderColor,
        borderWidth: selectionBorder.borderWidth,
        boxShadow: selectionBorder.boxShadow, // 无阴影，极简风格
      };
    }

    // 已填充活动的时间块（未选中时）
    if (activity) {
      return {
        ...baseStyle,
        backgroundColor: activity.color,
        borderColor: activity.color,
        borderWidth: '2px',
        boxShadow: isEditable ? 'var(--shadow-sm)' : 'none',
      };
    }

    // 空白时间块（未选中时）
    return {
      ...baseStyle,
      backgroundColor: isEditable ? 'var(--neutral-50)' : 'var(--neutral-100)',
      borderColor: isEditable ? 'var(--neutral-200)' : 'var(--neutral-300)',
      borderWidth: '2px',
      boxShadow: isEditable ? 'var(--shadow-sm)' : 'none',
    };
  }, [isEditable, activity, isSelected]);
  const isActive = !!activity;

  // 现代简约2.0：统一白色图标文字美学
  const textColor = useMemo(() => {
    if (isSelected && isEditable) {
      // 选中时使用深色文字，确保在浅色背景(--neutral-100)上的可读性
      return 'var(--neutral-700)';
    }

    if (isActive) {
      // 活动时间块统一使用白色图标和文字，创造一致的视觉体验
      return 'white';
    }

    // 空白时间块使用中性色文字
    return 'var(--neutral-400)';
  }, [isSelected, isEditable, isActive]);

  return (
    <motion.div
      className="relative flex items-center justify-center"
      style={{
        height: '60px',
        border: `${blockStyle.borderWidth || '2px'} solid`,
        borderRadius: 'var(--radius-lg)',
        transition: 'all var(--duration-fast) var(--ease-out)',
        ...blockStyle,
      }}
      whileHover={isEditable ? {
        scale: 1.02,
        y: -1,
        boxShadow: 'var(--shadow-md)'
      } : {}}
      whileTap={isEditable ? { scale: 0.98 } : {}}
      tabIndex={isClient && isEditable ? 0 : undefined}
      onClick={isEditable ? onClick : undefined}
      onMouseDown={isEditable ? onMouseDown : undefined}
      onMouseEnter={() => {
        if (isEditable) {
          onMouseEnter();
        } else {
          setShowTooltip(true);
        }
      }}
      onMouseLeave={() => {
        setShowTooltip(false);
      }}
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{
        scale: 1,
        // 添加平滑的可编辑状态过渡
        ...blockStyle,
        opacity: 1
      }}
      transition={{
        duration: 0.15,
        ease: [0.4, 0, 0.2, 1],
        // 为透明度和颜色变化添加更长的过渡时间
        opacity: { duration: 0.3 },
        backgroundColor: { duration: 0.3 },
        borderColor: { duration: 0.3 }
      }}
    >
      {/* 活动内容 */}
      {activity && (
        <motion.div
          className="flex flex-col items-center justify-center text-center"
          style={{ padding: '0 var(--spacing-1)' }}
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.1, duration: 0.2 }}
        >
          {/* 图标 */}
          {activity.icon && (
            <div style={{
              color: textColor,
              marginBottom: 'var(--spacing-1)'
            }}>
              {getIconComponent(activity.icon)}
            </div>
          )}

          {/* 活动名称 */}
          <div style={{
            fontSize: 'var(--font-size-xs)',
            fontWeight: 'var(--font-weight-medium)',
            color: textColor,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            maxWidth: '100%'
          }}>
            {activity.name}
          </div>
        </motion.div>
      )}

      {/* 选中状态指示器已移除，现在使用智能边框样式 */}

      {/* 时间显示（仅在hover时显示） */}
      <div
        className="absolute pointer-events-none"
        style={{
          bottom: '-24px',
          left: '50%',
          transform: 'translateX(-50%)',
          opacity: 0,
          transition: 'opacity var(--duration-fast) var(--ease-out)',
          zIndex: 10
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.opacity = '1';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.opacity = '0';
        }}
      >
        <div style={{
          background: 'var(--neutral-800)',
          color: 'white',
          fontSize: 'var(--font-size-xs)',
          padding: 'var(--spacing-1) var(--spacing-2)',
          borderRadius: 'var(--radius-md)',
          boxShadow: 'var(--shadow-lg)',
          whiteSpace: 'nowrap'
        }}>
          {block.startTime} - {block.endTime}
        </div>
      </div>

      {/* 活动详情提示已移除，现在使用右侧固定信息面板 */}

      {/* 不可编辑状态的提示 */}
      {!isEditable && showTooltip && (
        <motion.div
          className="absolute pointer-events-none"
          style={{
            bottom: '-36px',
            left: '50%',
            transform: 'translateX(-50%)',
            zIndex: 20
          }}
          initial={{ opacity: 0, y: -5 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -5 }}
          transition={{ duration: 0.2 }}
        >
          <div style={{
            background: 'var(--neutral-800)',
            color: 'white',
            fontSize: 'var(--font-size-xs)',
            padding: 'var(--spacing-2) var(--spacing-3)',
            borderRadius: 'var(--radius-md)',
            boxShadow: 'var(--shadow-lg)',
            whiteSpace: 'nowrap'
          }}>
            只能记录已经过去的时间
          </div>
        </motion.div>
      )}
    </motion.div>
  );
}
