'use client';

import React, { useCallback, useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { useAppStore } from '@/stores/useAppStore';
import { isTimeSlotEditable } from '@/utils/timeUtils';
import type { TimeBlock as TimeBlockType } from '@/types';
import {
  calculateDragPath,
  detectDragDirection,
  getDragSuggestion,
  type DragPath
} from '@/utils/dragUtils';
import { TimeBlock } from './TimeBlock';
import { ActivityPalette } from './ActivityPalette';
import { TimelineIndicator } from './TimelineIndicator';
import { DragPathVisualizer } from './DragPreview';
import { DragGuide } from './DragGuide';
import { SelectionInfoPanel } from './SelectionInfoPanel';


export function TimeGrid() {
  const {
    currentDate,
    getTimeBlocksForDate,
    ui,
    setSelectedBlocks,
    clearSelectedBlocks,
    setShowActivityPalette,
    setDragging,
    updateMultipleTimeBlocks
  } = useAppStore();

  const timeBlocks = getTimeBlocksForDate(currentDate);
  const [dragStart, setDragStart] = useState<number | null>(null);
  const [dragEnd, setDragEnd] = useState<number | null>(null);
  const [currentDragPath, setCurrentDragPath] = useState<DragPath | null>(null);
  const [showDragGuide, setShowDragGuide] = useState(false);


  // 检查是否需要显示拖拽指南（首次使用）
  useEffect(() => {
    const hasSeenDragGuide = localStorage.getItem('chronospect-drag-guide-seen');
    if (!hasSeenDragGuide) {
      // 延迟显示，让用户先看到新界面
      const timer = setTimeout(() => {
        setShowDragGuide(true);
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, []);

  // 关闭拖拽指南
  const handleCloseDragGuide = useCallback(() => {
    setShowDragGuide(false);
    localStorage.setItem('chronospect-drag-guide-seen', 'true');
  }, []);

  // 处理鼠标按下开始拖拽
  const handleMouseDown = useCallback((timeSlot: number, event: React.MouseEvent) => {
    event.preventDefault();

    // 检查是否可编辑
    if (!isTimeSlotEditable(currentDate, timeSlot)) {
      return; // 不可编辑的区块不允许拖拽
    }

    setDragStart(timeSlot);
    setDragEnd(timeSlot);
    setDragging(true, timeSlot);
    setSelectedBlocks([timeSlot]);
  }, [currentDate, setDragging, setSelectedBlocks]);

  // 处理鼠标移动时的拖拽 - 集成智能方向检测
  const handleMouseEnter = useCallback((timeSlot: number) => {
    if (dragStart !== null && ui.isDragging) {
      // 检查目标时间槽是否可编辑
      if (!isTimeSlotEditable(currentDate, timeSlot)) {
        return; // 不允许拖拽到不可编辑的区块
      }

      setDragEnd(timeSlot);

      // 计算智能拖拽路径
      const dragPath = calculateDragPath(dragStart, timeSlot);
      setCurrentDragPath(dragPath);

      // 过滤出可编辑的时间槽
      const editableSlots = dragPath.selectedSlots.filter(slot =>
        isTimeSlotEditable(currentDate, slot)
      );

      setSelectedBlocks(editableSlots);
    }
  }, [dragStart, ui.isDragging, currentDate, setSelectedBlocks]);

  // 处理鼠标释放结束拖拽
  const handleMouseUp = useCallback(() => {
    // 先保存当前状态，避免状态更新导致的时序问题
    const wasDragging = ui.isDragging;
    const selectedCount = ui.selectedBlocks.length;

    // 重置拖拽状态
    setDragging(false);
    setDragStart(null);
    setDragEnd(null);
    setCurrentDragPath(null);

    // 如果是拖拽结束且有选中的块，显示活动选择面板
    if (wasDragging && selectedCount > 0) {
      setShowActivityPalette(true);
    }
  }, [ui.isDragging, ui.selectedBlocks.length, setShowActivityPalette, setDragging]);

  // 处理单击时间块
  const handleBlockClick = useCallback((timeSlot: number, event: React.MouseEvent) => {
    event.stopPropagation();

    // 检查是否可编辑
    if (!isTimeSlotEditable(currentDate, timeSlot)) {
      return; // 不可编辑的区块不响应点击
    }

    if (!ui.isDragging) {
      setSelectedBlocks([timeSlot]);
      setShowActivityPalette(true);
    }
  }, [ui.isDragging, currentDate, setSelectedBlocks, setShowActivityPalette]);

  // 处理活动选择
  const handleActivitySelect = useCallback((activityId: string | null) => {
    if (ui.selectedBlocks.length > 0) {
      updateMultipleTimeBlocks(currentDate, ui.selectedBlocks, activityId);
      clearSelectedBlocks();
      setShowActivityPalette(false);
    }
  }, [ui.selectedBlocks, currentDate, updateMultipleTimeBlocks, clearSelectedBlocks, setShowActivityPalette]);

  // 处理点击空白区域
  const handleBackgroundClick = useCallback((event: React.MouseEvent) => {
    // 只有在点击的是背景元素本身时才关闭，避免干扰拖拽
    if (event.target === event.currentTarget && !ui.isDragging) {
      clearSelectedBlocks();
      setShowActivityPalette(false);
    }
  }, [clearSelectedBlocks, setShowActivityPalette, ui.isDragging]);

  // 全局鼠标事件监听
  useEffect(() => {
    const handleGlobalMouseUp = () => {
      if (ui.isDragging) {
        handleMouseUp();
      }
    };

    document.addEventListener('mouseup', handleGlobalMouseUp);
    return () => {
      document.removeEventListener('mouseup', handleGlobalMouseUp);
    };
  }, [ui.isDragging, handleMouseUp]);

  // 生成时间段标签 - 6个4小时时间段
  const generateTimeSegmentLabels = () => {
    const segments = [
      {
        range: '00-04',
        period: '深夜',
        hours: [0, 1, 2, 3],
        theme: 'night'
      },
      {
        range: '04-08',
        period: '黎明',
        hours: [4, 5, 6, 7],
        theme: 'dawn'
      },
      {
        range: '08-12',
        period: '上午',
        hours: [8, 9, 10, 11],
        theme: 'morning'
      },
      {
        range: '12-16',
        period: '下午',
        hours: [12, 13, 14, 15],
        theme: 'afternoon'
      },
      {
        range: '16-20',
        period: '傍晚',
        hours: [16, 17, 18, 19],
        theme: 'evening'
      },
      {
        range: '20-24',
        period: '夜晚',
        hours: [20, 21, 22, 23],
        theme: 'twilight'
      }
    ];
    return segments;
  };

  // 生成小时刻度标签
  const generateHourLabels = () => {
    const labels = [];
    for (let hour = 0; hour < 24; hour += 4) {
      const endHour = Math.min(hour + 4, 24);
      labels.push({
        start: hour,
        end: endHour,
        hours: Array.from({ length: 4 }, (_, i) => hour + i).filter(h => h < 24)
      });
    }
    return labels;
  };

  const timeSegments = generateTimeSegmentLabels();
  const hourLabels = generateHourLabels();

  // 重新排列时间块以按列分时间段
  const reorderTimeBlocksByColumn = (blocks: TimeBlockType[]): TimeBlockType[] => {
    const reorderedBlocks: TimeBlockType[] = [];

    // 6列×8行，每列代表一个4小时时间段
    // 第1列: 00-04 (timeSlot 0-7)
    // 第2列: 04-08 (timeSlot 8-15)
    // 第3列: 08-12 (timeSlot 16-23)
    // 第4列: 12-16 (timeSlot 24-31)
    // 第5列: 16-20 (timeSlot 32-39)
    // 第6列: 20-24 (timeSlot 40-47)

    for (let row = 0; row < 8; row++) {
      for (let col = 0; col < 6; col++) {
        const timeSlot = col * 8 + row; // 按列计算timeSlot
        const block = blocks.find(b => b.timeSlot === timeSlot);
        if (block) {
          reorderedBlocks.push(block);
        }
      }
    }

    return reorderedBlocks;
  };

  // 重新排列时间块
  const reorderedTimeBlocks = reorderTimeBlocksByColumn(timeBlocks);

  return (
    <div className="w-full max-w-6xl mx-auto" style={{ padding: 'var(--spacing-6)' }}>
      <div
        className="relative select-none"
        onClick={handleBackgroundClick}
      >
        {/* 时间段标签行 */}
        <motion.div
          className="grid grid-cols-6"
          style={{
            gap: 'var(--spacing-3)',
            marginBottom: 'var(--spacing-2)'
          }}
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
        >
          {timeSegments.map((segment, index) => (
            <motion.div
              key={index}
              className="text-center cursor-default"
              style={{
                padding: 'var(--spacing-3)',
                borderRadius: 'var(--radius-lg)',
                background: `var(--time-segment-${segment.theme}-bg)`,
                border: `2px solid var(--time-segment-${segment.theme}-border)`,
                boxShadow: 'var(--shadow-sm)',
                transition: 'all var(--duration-fast) var(--ease-out)'
              }}
              whileHover={{
                scale: 1.02,
                boxShadow: 'var(--shadow-md)',
                y: -2
              }}
              transition={{ duration: 0.2, ease: [0.4, 0, 0.2, 1] }}
            >
              <div
                style={{
                  fontSize: 'var(--font-size-sm)',
                  fontWeight: 'var(--font-weight-bold)',
                  color: `var(--time-segment-${segment.theme}-text)`,
                  marginBottom: 'var(--spacing-1)',
                  textShadow: '0 1px 2px rgba(0, 0, 0, 0.1)'
                }}
              >
                {segment.period}
              </div>
              <div
                style={{
                  fontSize: 'var(--font-size-xs)',
                  color: `var(--time-segment-${segment.theme}-text)`,
                  fontFamily: 'var(--font-mono)',
                  opacity: 0.8
                }}
              >
                {segment.range}
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* 小时刻度行 */}
        <motion.div
          className="grid grid-cols-6"
          style={{
            gap: 'var(--spacing-3)',
            marginBottom: 'var(--spacing-4)'
          }}
          initial={{ opacity: 0, y: -5 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1, ease: [0.4, 0, 0.2, 1] }}
        >
          {hourLabels.map((labelGroup, index) => (
            <div
              key={index}
              className="flex justify-between"
              style={{
                padding: '0 var(--spacing-1)'
              }}
            >
              {labelGroup.hours.map((hour) => (
                <div
                  key={hour}
                  style={{
                    fontSize: 'var(--font-size-xs)',
                    color: 'var(--neutral-400)',
                    fontFamily: 'var(--font-mono)',
                    fontWeight: 'var(--font-weight-medium)'
                  }}
                >
                  {hour.toString().padStart(2, '0')}
                </div>
              ))}
            </div>
          ))}
        </motion.div>

        {/* 时间块网格 - 6列×8行布局 */}
        <div className="relative">

          <motion.div
            className="grid grid-cols-6 relative"
            style={{
              gap: 'var(--spacing-2)',
              rowGap: 'var(--spacing-2)'
            }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.2, ease: [0.4, 0, 0.2, 1] }}
          >
          {/* 拖拽路径可视化 */}
          <DragPathVisualizer
            dragPath={currentDragPath}
            isVisible={ui.isDragging}
          />

          {/* 时间线指示器 */}
          <TimelineIndicator />



          {reorderedTimeBlocks.map((block, index) => (
            <motion.div
              key={block.id}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{
                duration: 0.2,
                delay: index * 0.01, // 错位动画
                ease: [0.4, 0, 0.2, 1]
              }}
            >
              <TimeBlock
                block={block}
                isSelected={ui.selectedBlocks.includes(block.timeSlot)}
                onClick={(event) => handleBlockClick(block.timeSlot, event)}
                onMouseDown={(event) => handleMouseDown(block.timeSlot, event)}
                onMouseEnter={() => handleMouseEnter(block.timeSlot)}
              />
            </motion.div>
          ))}
          </motion.div>
        </div>

        {/* 活动选择面板 */}
        {ui.showActivityPalette && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.2, ease: [0.4, 0, 0.2, 1] }}
          >
            <ActivityPalette
              onActivitySelect={handleActivitySelect}
              onClose={() => {
                setShowActivityPalette(false);
                clearSelectedBlocks();
              }}
              selectedBlocks={ui.selectedBlocks}
            />
          </motion.div>
        )}



        {/* 拖拽指南 */}
        <DragGuide
          isVisible={showDragGuide}
          onClose={handleCloseDragGuide}
        />

        {/* 选择信息面板 */}
        <SelectionInfoPanel
          selectedBlocks={ui.selectedBlocks}
          dragPath={currentDragPath}
        />
      </div>
    </div>
  );
}
