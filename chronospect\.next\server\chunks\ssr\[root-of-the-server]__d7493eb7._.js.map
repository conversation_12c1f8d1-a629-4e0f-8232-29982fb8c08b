{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/types/index.ts"], "sourcesContent": ["// 时间块相关类型定义\nexport interface TimeBlock {\n  id: string;\n  date: string; // YYYY-MM-DD格式\n  timeSlot: number; // 0-47，代表48个30分钟时间段\n  activityId: string | null; // 关联的活动ID\n  startTime: string; // HH:MM格式，如 \"08:00\"\n  endTime: string; // HH:MM格式，如 \"08:30\"\n}\n\n// 活动类型定义\nexport interface Activity {\n  id: string;\n  name: string;\n  color: string; // 十六进制颜色值\n  icon?: string; // Lucide图标名称\n  description?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 日期相关类型\nexport interface DateInfo {\n  date: string; // YYYY-MM-DD格式\n  dayOfWeek: number; // 0-6，0为周日\n  isToday: boolean;\n  isWeekend: boolean;\n}\n\n// 统计数据类型\nexport interface DailyStats {\n  date: string;\n  totalMinutes: number; // 一天总分钟数 (24 * 60)\n  filledMinutes: number; // 已记录的分钟数\n  unfilledMinutes: number; // 未记录的分钟数\n  filledPercentage: number; // 已记录时间的百分比\n  activities: ActivityStats[];\n}\n\nexport interface ActivityStats {\n  activityId: string;\n  activityName: string;\n  color: string;\n  totalBlocks: number; // 该活动的时间块数量\n  totalMinutes: number; // 总分钟数\n  percentage: number; // 占已记录时间的百分比\n  timeRanges: TimeRange[]; // 时间段列表\n}\n\nexport interface TimeRange {\n  startTime: string;\n  endTime: string;\n  duration: number; // 分钟数\n}\n\n// UI状态类型\nexport interface UIState {\n  selectedBlocks: number[]; // 当前选中的时间块索引\n  showActivityPalette: boolean;\n  currentView: 'grid' | 'review'; // 当前视图模式\n  isDragging: boolean;\n  dragStartBlock: number | null;\n}\n\n// 应用状态类型\nexport interface AppState {\n  currentDate: string; // 当前查看的日期\n  activities: Activity[];\n  timeBlocks: Record<string, TimeBlock[]>; // 按日期分组的时间块数据\n  ui: UIState;\n}\n\n// 颜色预设\nexport const DEFAULT_COLORS = [\n  '#3B82F6', // 蓝色 - 工作\n  '#EF4444', // 红色 - 紧急\n  '#10B981', // 绿色 - 健康\n  '#F59E0B', // 黄色 - 娱乐\n  '#8B5CF6', // 紫色 - 学习\n  '#EC4899', // 粉色 - 社交\n  '#6B7280', // 灰色 - 休息\n  '#F97316', // 橙色 - 创意\n  '#14B8A6', // 青色 - 运动\n  '#84CC16', // 绿黄 - 自然\n] as const;\n\n// 默认活动预设\nexport const DEFAULT_ACTIVITIES: Omit<Activity, 'id' | 'createdAt' | 'updatedAt'>[] = [\n  {\n    name: '深度工作',\n    color: DEFAULT_COLORS[0],\n    icon: 'Laptop',\n    description: '专注的工作时间'\n  },\n  {\n    name: '会议',\n    color: DEFAULT_COLORS[1],\n    icon: 'Users',\n    description: '团队会议或讨论'\n  },\n  {\n    name: '学习',\n    color: DEFAULT_COLORS[4],\n    icon: 'BookOpen',\n    description: '学习新知识或技能'\n  },\n  {\n    name: '运动',\n    color: DEFAULT_COLORS[8],\n    icon: 'Dumbbell',\n    description: '体育锻炼'\n  },\n  {\n    name: '休息',\n    color: DEFAULT_COLORS[6],\n    icon: 'Coffee',\n    description: '休息放松时间'\n  },\n  {\n    name: '娱乐',\n    color: DEFAULT_COLORS[3],\n    icon: 'Gamepad2',\n    description: '娱乐活动'\n  },\n  {\n    name: '通勤',\n    color: DEFAULT_COLORS[7],\n    icon: 'Car',\n    description: '上下班通勤'\n  },\n  {\n    name: '用餐',\n    color: DEFAULT_COLORS[9],\n    icon: 'Utensils',\n    description: '用餐时间'\n  }\n];\n\n// 时间工具函数类型\nexport interface TimeUtils {\n  formatTime: (timeSlot: number) => { start: string; end: string };\n  getTimeSlot: (hour: number, minute: number) => number;\n  isValidTimeSlot: (timeSlot: number) => boolean;\n  generateTimeBlocks: (date: string) => TimeBlock[];\n}\n"], "names": [], "mappings": "AAAA,YAAY;;;;;AAyEL,MAAM,iBAAiB;IAC5B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAGM,MAAM,qBAAyE;IACpF;QACE,MAAM;QACN,OAAO,cAAc,CAAC,EAAE;QACxB,MAAM;QACN,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO,cAAc,CAAC,EAAE;QACxB,MAAM;QACN,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO,cAAc,CAAC,EAAE;QACxB,MAAM;QACN,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO,cAAc,CAAC,EAAE;QACxB,MAAM;QACN,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO,cAAc,CAAC,EAAE;QACxB,MAAM;QACN,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO,cAAc,CAAC,EAAE;QACxB,MAAM;QACN,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO,cAAc,CAAC,EAAE;QACxB,MAAM;QACN,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO,cAAc,CAAC,EAAE;QACxB,MAAM;QACN,aAAa;IACf;CACD", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/utils/timeUtils.ts"], "sourcesContent": ["import { TimeBlock } from '@/types';\r\n\r\n/**\r\n * 将时间槽索引转换为时间字符串\r\n * @param timeSlot 时间槽索引 (0-47)\r\n * @returns 包含开始和结束时间的对象\r\n */\r\nexport function formatTime(timeSlot: number): { start: string; end: string } {\r\n  if (!isValidTimeSlot(timeSlot)) {\r\n    throw new Error(`Invalid time slot: ${timeSlot}`);\r\n  }\r\n\r\n  const totalMinutes = timeSlot * 30;\r\n  const hours = Math.floor(totalMinutes / 60);\r\n  const minutes = totalMinutes % 60;\r\n  \r\n  const startHour = hours.toString().padStart(2, '0');\r\n  const startMinute = minutes.toString().padStart(2, '0');\r\n  \r\n  const endTotalMinutes = totalMinutes + 30;\r\n  const endHours = Math.floor(endTotalMinutes / 60);\r\n  const endMinutes = endTotalMinutes % 60;\r\n  \r\n  const endHour = endHours.toString().padStart(2, '0');\r\n  const endMinuteStr = endMinutes.toString().padStart(2, '0');\r\n\r\n  return {\r\n    start: `${startHour}:${startMinute}`,\r\n    end: `${endHour}:${endMinuteStr}`\r\n  };\r\n}\r\n\r\n/**\r\n * 根据小时和分钟获取时间槽索引\r\n * @param hour 小时 (0-23)\r\n * @param minute 分钟 (0, 30)\r\n * @returns 时间槽索引\r\n */\r\nexport function getTimeSlot(hour: number, minute: number): number {\r\n  if (hour < 0 || hour > 23) {\r\n    throw new Error(`Invalid hour: ${hour}`);\r\n  }\r\n  if (minute !== 0 && minute !== 30) {\r\n    throw new Error(`Invalid minute: ${minute}. Must be 0 or 30.`);\r\n  }\r\n\r\n  return hour * 2 + (minute === 30 ? 1 : 0);\r\n}\r\n\r\n/**\r\n * 验证时间槽索引是否有效\r\n * @param timeSlot 时间槽索引\r\n * @returns 是否有效\r\n */\r\nexport function isValidTimeSlot(timeSlot: number): boolean {\r\n  return Number.isInteger(timeSlot) && timeSlot >= 0 && timeSlot < 48;\r\n}\r\n\r\n/**\r\n * 为指定日期生成48个空的时间块\r\n * @param date 日期字符串 (YYYY-MM-DD)\r\n * @returns 时间块数组\r\n */\r\nexport function generateTimeBlocks(date: string): TimeBlock[] {\r\n  const blocks: TimeBlock[] = [];\r\n  \r\n  for (let i = 0; i < 48; i++) {\r\n    const timeInfo = formatTime(i);\r\n    blocks.push({\r\n      id: `${date}-${i}`,\r\n      date,\r\n      timeSlot: i,\r\n      activityId: null,\r\n      startTime: timeInfo.start,\r\n      endTime: timeInfo.end\r\n    });\r\n  }\r\n  \r\n  return blocks;\r\n}\r\n\r\n/**\r\n * 获取当前日期字符串\r\n * @returns YYYY-MM-DD格式的日期字符串\r\n */\r\nexport function getCurrentDate(): string {\r\n  // SSR安全检查：在服务器端返回安全的默认值\r\n  if (typeof window === 'undefined') {\r\n    return '1970-01-01';\r\n  }\r\n  return new Date().toISOString().split('T')[0];\r\n}\r\n\r\n/**\r\n * 格式化日期显示\r\n * @param date 日期字符串 (YYYY-MM-DD)\r\n * @returns 格式化的日期字符串\r\n */\r\nexport function formatDate(date: string): string {\r\n  const dateObj = new Date(date);\r\n  const today = new Date();\r\n  const yesterday = new Date(today);\r\n  yesterday.setDate(today.getDate() - 1);\r\n  const tomorrow = new Date(today);\r\n  tomorrow.setDate(today.getDate() + 1);\r\n\r\n  const dateStr = dateObj.toISOString().split('T')[0];\r\n  const todayStr = today.toISOString().split('T')[0];\r\n  const yesterdayStr = yesterday.toISOString().split('T')[0];\r\n  const tomorrowStr = tomorrow.toISOString().split('T')[0];\r\n\r\n  if (dateStr === todayStr) {\r\n    return '今天';\r\n  } else if (dateStr === yesterdayStr) {\r\n    return '昨天';\r\n  } else if (dateStr === tomorrowStr) {\r\n    return '明天';\r\n  }\r\n\r\n  // 返回月日格式\r\n  const month = dateObj.getMonth() + 1;\r\n  const day = dateObj.getDate();\r\n  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];\r\n  const weekday = weekdays[dateObj.getDay()];\r\n  \r\n  return `${month}月${day}日 ${weekday}`;\r\n}\r\n\r\n/**\r\n * 获取日期信息\r\n * @param date 日期字符串 (YYYY-MM-DD)\r\n * @returns 日期信息对象\r\n */\r\nexport function getDateInfo(date: string) {\r\n  const dateObj = new Date(date);\r\n  const today = new Date().toISOString().split('T')[0];\r\n  \r\n  return {\r\n    date,\r\n    dayOfWeek: dateObj.getDay(),\r\n    isToday: date === today,\r\n    isWeekend: dateObj.getDay() === 0 || dateObj.getDay() === 6\r\n  };\r\n}\r\n\r\n/**\r\n * 获取相邻日期\r\n * @param date 当前日期\r\n * @param offset 偏移天数 (正数为未来，负数为过去)\r\n * @returns 新的日期字符串\r\n */\r\nexport function getAdjacentDate(date: string, offset: number): string {\r\n  const dateObj = new Date(date);\r\n  dateObj.setDate(dateObj.getDate() + offset);\r\n  return dateObj.toISOString().split('T')[0];\r\n}\r\n\r\n/**\r\n * 计算时间块的总分钟数\r\n * @param blocks 时间块数组\r\n * @returns 总分钟数\r\n */\r\nexport function calculateTotalMinutes(blocks: TimeBlock[]): number {\r\n  return blocks.filter(block => block.activityId !== null).length * 30;\r\n}\r\n\r\n/**\r\n * 获取当前时间信息\r\n * @returns 当前时间的详细信息\r\n */\r\nexport function getCurrentTime() {\r\n  // SSR安全检查：在服务器端返回安全的默认值\r\n  if (typeof window === 'undefined') {\r\n    return {\r\n      date: '1970-01-01',\r\n      hour: 0,\r\n      minute: 0,\r\n      timestamp: 0\r\n    };\r\n  }\r\n\r\n  const now = new Date();\r\n  return {\r\n    date: now.toISOString().split('T')[0], // YYYY-MM-DD\r\n    hour: now.getHours(),\r\n    minute: now.getMinutes(),\r\n    timestamp: now.getTime()\r\n  };\r\n}\r\n\r\n/**\r\n * 获取当前时间对应的时间槽\r\n * @returns 当前时间槽索引 (0-47)，如果是未来时间则返回 -1\r\n */\r\nexport function getCurrentTimeSlot(): number {\r\n  // SSR安全检查：在服务器端返回安全的默认值\r\n  if (typeof window === 'undefined') {\r\n    return 0;\r\n  }\r\n\r\n  const { hour, minute } = getCurrentTime();\r\n\r\n  // 计算当前时间槽（向下取整到最近的30分钟）\r\n  const currentSlot = hour * 2 + (minute >= 30 ? 1 : 0);\r\n\r\n  return currentSlot;\r\n}\r\n\r\n/**\r\n * 判断指定日期和时间槽是否可编辑\r\n * @param date 日期字符串 (YYYY-MM-DD)\r\n * @param timeSlot 时间槽索引 (0-47)\r\n * @returns 是否可编辑\r\n */\r\nexport function isTimeSlotEditable(date: string, timeSlot: number): boolean {\r\n  if (!isValidTimeSlot(timeSlot)) {\r\n    return false;\r\n  }\r\n\r\n  const currentTime = getCurrentTime();\r\n  const currentDate = currentTime.date;\r\n\r\n  // 过去的日期都可以编辑\r\n  if (date < currentDate) {\r\n    return true;\r\n  }\r\n\r\n  // 未来的日期都不可编辑\r\n  if (date > currentDate) {\r\n    return false;\r\n  }\r\n\r\n  // 当天的情况：只有已经结束的时间槽可以编辑\r\n  // 时间槽结束时间必须小于等于当前时间\r\n\r\n  // 当前时间槽的结束时间\r\n  const slotEndTime = formatTime(timeSlot);\r\n  const [endHour, endMinute] = slotEndTime.end.split(':').map(Number);\r\n\r\n  // 比较时间槽结束时间与当前时间\r\n  if (endHour < currentTime.hour) {\r\n    return true;\r\n  } else if (endHour === currentTime.hour) {\r\n    return endMinute <= currentTime.minute;\r\n  } else {\r\n    return false;\r\n  }\r\n}\r\n\r\n/**\r\n * 获取日期的可编辑时间槽范围\r\n * @param date 日期字符串 (YYYY-MM-DD)\r\n * @returns 可编辑的时间槽范围 { start: number, end: number }\r\n */\r\nexport function getEditableTimeSlotRange(date: string): { start: number; end: number } {\r\n  const currentTime = getCurrentTime();\r\n  const currentDate = currentTime.date;\r\n\r\n  // 过去的日期：全部可编辑\r\n  if (date < currentDate) {\r\n    return { start: 0, end: 47 };\r\n  }\r\n\r\n  // 未来的日期：全部不可编辑\r\n  if (date > currentDate) {\r\n    return { start: -1, end: -1 };\r\n  }\r\n\r\n  // 当天：从0到当前已结束的时间槽\r\n  let editableEnd = -1;\r\n\r\n  // 找到最后一个可编辑的时间槽\r\n  for (let i = 0; i < 48; i++) {\r\n    if (isTimeSlotEditable(date, i)) {\r\n      editableEnd = i;\r\n    } else {\r\n      break;\r\n    }\r\n  }\r\n\r\n  return { start: 0, end: editableEnd };\r\n}\r\n\r\n/**\r\n * 清理未来时间的活动数据\r\n * @param timeBlocks 时间块数组\r\n * @param date 日期字符串 (YYYY-MM-DD)\r\n * @returns 清理后的时间块数组\r\n */\r\nexport function cleanFutureTimeBlocks(timeBlocks: TimeBlock[], date: string): TimeBlock[] {\r\n  return timeBlocks.map(block => {\r\n    // 如果时间槽不可编辑，清除其活动数据\r\n    if (!isTimeSlotEditable(date, block.timeSlot)) {\r\n      return {\r\n        ...block,\r\n        activityId: null\r\n      };\r\n    }\r\n    return block;\r\n  });\r\n}\r\n\r\n/**\r\n * 将分钟数转换为小时分钟格式\r\n * @param minutes 分钟数\r\n * @returns 格式化的时间字符串\r\n */\r\nexport function formatDuration(minutes: number): string {\r\n  const hours = Math.floor(minutes / 60);\r\n  const mins = minutes % 60;\r\n\r\n  if (hours === 0) {\r\n    return `${mins}分钟`;\r\n  } else if (mins === 0) {\r\n    return `${hours}小时`;\r\n  } else {\r\n    return `${hours}小时${mins}分钟`;\r\n  }\r\n}\r\n\r\n// ==================== 智能精确时间触发器工具函数 ====================\r\n\r\n/**\r\n * 计算到下一个整分钟的延迟时间（毫秒）\r\n * 使用高精度时间计算，确保精确触发\r\n * @returns 到下一个整分钟的毫秒数\r\n */\r\nexport function calculateNextMinuteDelay(): number {\r\n  // SSR安全检查\r\n  if (typeof window === 'undefined') {\r\n    return 60000; // 默认1分钟\r\n  }\r\n\r\n  const now = new Date();\r\n  const nextMinute = new Date(now);\r\n\r\n  // 设置到下一个整分钟的0秒0毫秒\r\n  nextMinute.setSeconds(0, 0);\r\n  nextMinute.setMinutes(nextMinute.getMinutes() + 1);\r\n\r\n  const delay = nextMinute.getTime() - now.getTime();\r\n\r\n  // 确保延迟时间为正数，最小为1ms\r\n  return Math.max(delay, 1);\r\n}\r\n\r\n/**\r\n * 计算到下一个时间槽边界的延迟时间（毫秒）\r\n * 时间槽以30分钟为单位（00:00, 00:30, 01:00, 01:30...）\r\n * @returns 到下一个时间槽边界的毫秒数\r\n */\r\nexport function calculateNextTimeSlotDelay(): number {\r\n  // SSR安全检查\r\n  if (typeof window === 'undefined') {\r\n    return 1800000; // 默认30分钟\r\n  }\r\n\r\n  const now = new Date();\r\n  const currentMinute = now.getMinutes();\r\n  const nextSlotMinute = currentMinute < 30 ? 30 : 0;\r\n\r\n  const nextSlot = new Date(now);\r\n  nextSlot.setSeconds(0, 0);\r\n\r\n  if (nextSlotMinute === 0) {\r\n    // 下一个小时的0分\r\n    nextSlot.setHours(nextSlot.getHours() + 1, 0);\r\n  } else {\r\n    // 当前小时的30分\r\n    nextSlot.setMinutes(30);\r\n  }\r\n\r\n  const delay = nextSlot.getTime() - now.getTime();\r\n\r\n  // 确保延迟时间为正数，最小为1ms\r\n  return Math.max(delay, 1);\r\n}\r\n\r\n/**\r\n * 获取高精度当前时间戳\r\n * 优先使用 performance.now() + performance.timeOrigin 获得高精度时间\r\n * @returns 高精度时间戳（毫秒）\r\n */\r\nexport function getHighPrecisionTimestamp(): number {\r\n  // SSR安全检查\r\n  if (typeof window === 'undefined') {\r\n    return Date.now();\r\n  }\r\n\r\n  // 使用高精度时间API\r\n  if (typeof performance !== 'undefined' && performance.now && performance.timeOrigin) {\r\n    return performance.timeOrigin + performance.now();\r\n  }\r\n\r\n  // 降级到标准时间API\r\n  return Date.now();\r\n}\r\n\r\n/**\r\n * 检查当前时间槽是否发生了变化\r\n * @param previousTimeSlot 之前的时间槽\r\n * @returns 是否发生变化\r\n */\r\nexport function hasTimeSlotChanged(previousTimeSlot: number): boolean {\r\n  const currentTimeSlot = getCurrentTimeSlot();\r\n  return currentTimeSlot !== previousTimeSlot;\r\n}\r\n\r\n/**\r\n * 验证时间触发器的精度\r\n * 用于调试和性能监控\r\n * @param expectedTime 期望的触发时间\r\n * @param actualTime 实际的触发时间\r\n * @returns 精度信息对象\r\n */\r\nexport function validateTriggerPrecision(expectedTime: number, actualTime: number) {\r\n  const deviation = Math.abs(actualTime - expectedTime);\r\n  const isAccurate = deviation <= 100; // 100ms内认为是精确的\r\n\r\n  return {\r\n    expectedTime,\r\n    actualTime,\r\n    deviation,\r\n    isAccurate,\r\n    deviationMs: deviation,\r\n    deviationPercent: (deviation / 1000) * 100 // 相对于1秒的百分比\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAOO,SAAS,WAAW,QAAgB;IACzC,IAAI,CAAC,gBAAgB,WAAW;QAC9B,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,UAAU;IAClD;IAEA,MAAM,eAAe,WAAW;IAChC,MAAM,QAAQ,KAAK,KAAK,CAAC,eAAe;IACxC,MAAM,UAAU,eAAe;IAE/B,MAAM,YAAY,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG;IAC/C,MAAM,cAAc,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG;IAEnD,MAAM,kBAAkB,eAAe;IACvC,MAAM,WAAW,KAAK,KAAK,CAAC,kBAAkB;IAC9C,MAAM,aAAa,kBAAkB;IAErC,MAAM,UAAU,SAAS,QAAQ,GAAG,QAAQ,CAAC,GAAG;IAChD,MAAM,eAAe,WAAW,QAAQ,GAAG,QAAQ,CAAC,GAAG;IAEvD,OAAO;QACL,OAAO,GAAG,UAAU,CAAC,EAAE,aAAa;QACpC,KAAK,GAAG,QAAQ,CAAC,EAAE,cAAc;IACnC;AACF;AAQO,SAAS,YAAY,IAAY,EAAE,MAAc;IACtD,IAAI,OAAO,KAAK,OAAO,IAAI;QACzB,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,MAAM;IACzC;IACA,IAAI,WAAW,KAAK,WAAW,IAAI;QACjC,MAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE,OAAO,kBAAkB,CAAC;IAC/D;IAEA,OAAO,OAAO,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC;AAC1C;AAOO,SAAS,gBAAgB,QAAgB;IAC9C,OAAO,OAAO,SAAS,CAAC,aAAa,YAAY,KAAK,WAAW;AACnE;AAOO,SAAS,mBAAmB,IAAY;IAC7C,MAAM,SAAsB,EAAE;IAE9B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;QAC3B,MAAM,WAAW,WAAW;QAC5B,OAAO,IAAI,CAAC;YACV,IAAI,GAAG,KAAK,CAAC,EAAE,GAAG;YAClB;YACA,UAAU;YACV,YAAY;YACZ,WAAW,SAAS,KAAK;YACzB,SAAS,SAAS,GAAG;QACvB;IACF;IAEA,OAAO;AACT;AAMO,SAAS;IACd,wBAAwB;IACxB,wCAAmC;QACjC,OAAO;IACT;;;AAEF;AAOO,SAAS,WAAW,IAAY;IACrC,MAAM,UAAU,IAAI,KAAK;IACzB,MAAM,QAAQ,IAAI;IAClB,MAAM,YAAY,IAAI,KAAK;IAC3B,UAAU,OAAO,CAAC,MAAM,OAAO,KAAK;IACpC,MAAM,WAAW,IAAI,KAAK;IAC1B,SAAS,OAAO,CAAC,MAAM,OAAO,KAAK;IAEnC,MAAM,UAAU,QAAQ,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IACnD,MAAM,WAAW,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IAClD,MAAM,eAAe,UAAU,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IAC1D,MAAM,cAAc,SAAS,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IAExD,IAAI,YAAY,UAAU;QACxB,OAAO;IACT,OAAO,IAAI,YAAY,cAAc;QACnC,OAAO;IACT,OAAO,IAAI,YAAY,aAAa;QAClC,OAAO;IACT;IAEA,SAAS;IACT,MAAM,QAAQ,QAAQ,QAAQ,KAAK;IACnC,MAAM,MAAM,QAAQ,OAAO;IAC3B,MAAM,WAAW;QAAC;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK;IAC3D,MAAM,UAAU,QAAQ,CAAC,QAAQ,MAAM,GAAG;IAE1C,OAAO,GAAG,MAAM,CAAC,EAAE,IAAI,EAAE,EAAE,SAAS;AACtC;AAOO,SAAS,YAAY,IAAY;IACtC,MAAM,UAAU,IAAI,KAAK;IACzB,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IAEpD,OAAO;QACL;QACA,WAAW,QAAQ,MAAM;QACzB,SAAS,SAAS;QAClB,WAAW,QAAQ,MAAM,OAAO,KAAK,QAAQ,MAAM,OAAO;IAC5D;AACF;AAQO,SAAS,gBAAgB,IAAY,EAAE,MAAc;IAC1D,MAAM,UAAU,IAAI,KAAK;IACzB,QAAQ,OAAO,CAAC,QAAQ,OAAO,KAAK;IACpC,OAAO,QAAQ,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;AAC5C;AAOO,SAAS,sBAAsB,MAAmB;IACvD,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,UAAU,KAAK,MAAM,MAAM,GAAG;AACpE;AAMO,SAAS;IACd,wBAAwB;IACxB,wCAAmC;QACjC,OAAO;YACL,MAAM;YACN,MAAM;YACN,QAAQ;YACR,WAAW;QACb;IACF;;;IAEA,MAAM;AAOR;AAMO,SAAS;IACd,wBAAwB;IACxB,wCAAmC;QACjC,OAAO;IACT;;;IAEA,MAAQ,kBAAM;IAEd,wBAAwB;IACxB,MAAM;AAGR;AAQO,SAAS,mBAAmB,IAAY,EAAE,QAAgB;IAC/D,IAAI,CAAC,gBAAgB,WAAW;QAC9B,OAAO;IACT;IAEA,MAAM,cAAc;IACpB,MAAM,cAAc,YAAY,IAAI;IAEpC,aAAa;IACb,IAAI,OAAO,aAAa;QACtB,OAAO;IACT;IAEA,aAAa;IACb,IAAI,OAAO,aAAa;QACtB,OAAO;IACT;IAEA,uBAAuB;IACvB,oBAAoB;IAEpB,aAAa;IACb,MAAM,cAAc,WAAW;IAC/B,MAAM,CAAC,SAAS,UAAU,GAAG,YAAY,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;IAE5D,iBAAiB;IACjB,IAAI,UAAU,YAAY,IAAI,EAAE;QAC9B,OAAO;IACT,OAAO,IAAI,YAAY,YAAY,IAAI,EAAE;QACvC,OAAO,aAAa,YAAY,MAAM;IACxC,OAAO;QACL,OAAO;IACT;AACF;AAOO,SAAS,yBAAyB,IAAY;IACnD,MAAM,cAAc;IACpB,MAAM,cAAc,YAAY,IAAI;IAEpC,cAAc;IACd,IAAI,OAAO,aAAa;QACtB,OAAO;YAAE,OAAO;YAAG,KAAK;QAAG;IAC7B;IAEA,eAAe;IACf,IAAI,OAAO,aAAa;QACtB,OAAO;YAAE,OAAO,CAAC;YAAG,KAAK,CAAC;QAAE;IAC9B;IAEA,kBAAkB;IAClB,IAAI,cAAc,CAAC;IAEnB,gBAAgB;IAChB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;QAC3B,IAAI,mBAAmB,MAAM,IAAI;YAC/B,cAAc;QAChB,OAAO;YACL;QACF;IACF;IAEA,OAAO;QAAE,OAAO;QAAG,KAAK;IAAY;AACtC;AAQO,SAAS,sBAAsB,UAAuB,EAAE,IAAY;IACzE,OAAO,WAAW,GAAG,CAAC,CAAA;QACpB,oBAAoB;QACpB,IAAI,CAAC,mBAAmB,MAAM,MAAM,QAAQ,GAAG;YAC7C,OAAO;gBACL,GAAG,KAAK;gBACR,YAAY;YACd;QACF;QACA,OAAO;IACT;AACF;AAOO,SAAS,eAAe,OAAe;IAC5C,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,OAAO,UAAU;IAEvB,IAAI,UAAU,GAAG;QACf,OAAO,GAAG,KAAK,EAAE,CAAC;IACpB,OAAO,IAAI,SAAS,GAAG;QACrB,OAAO,GAAG,MAAM,EAAE,CAAC;IACrB,OAAO;QACL,OAAO,GAAG,MAAM,EAAE,EAAE,KAAK,EAAE,CAAC;IAC9B;AACF;AASO,SAAS;IACd,UAAU;IACV,wCAAmC;QACjC,OAAO,OAAO,QAAQ;IACxB;;;IAEA,MAAM;IACN,MAAM;IAMN,MAAM;AAIR;AAOO,SAAS;IACd,UAAU;IACV,wCAAmC;QACjC,OAAO,SAAS,SAAS;IAC3B;;;IAEA,MAAM;IACN,MAAM;IACN,MAAM;IAEN,MAAM;IAWN,MAAM;AAIR;AAOO,SAAS;IACd,UAAU;IACV,wCAAmC;QACjC,OAAO,KAAK,GAAG;IACjB;;;AASF;AAOO,SAAS,mBAAmB,gBAAwB;IACzD,MAAM,kBAAkB;IACxB,OAAO,oBAAoB;AAC7B;AASO,SAAS,yBAAyB,YAAoB,EAAE,UAAkB;IAC/E,MAAM,YAAY,KAAK,GAAG,CAAC,aAAa;IACxC,MAAM,aAAa,aAAa,KAAK,eAAe;IAEpD,OAAO;QACL;QACA;QACA;QACA;QACA,aAAa;QACb,kBAAkB,AAAC,YAAY,OAAQ,IAAI,YAAY;IACzD;AACF", "debugId": null}}, {"offset": {"line": 371, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/utils/statsUtils.ts"], "sourcesContent": ["import { TimeBlock, Activity, ActivityStats, TimeRange, DailyStats } from '@/types';\nimport { formatTime, formatDuration } from './timeUtils';\n\n/**\n * 计算指定日期的活动统计数据\n * @param timeBlocks 时间块数组\n * @param activities 活动数组\n * @returns 每日统计数据\n */\nexport function calculateDailyStats(\n  timeBlocks: TimeBlock[], \n  activities: Activity[]\n): DailyStats {\n  // 过滤出已填充的时间块\n  const filledBlocks = timeBlocks.filter(block => block.activityId !== null);\n  const totalFilledMinutes = filledBlocks.length * 30;\n  const totalMinutes = 24 * 60; // 一天总分钟数\n  \n  // 按活动分组统计\n  const activityStatsMap = new Map<string, {\n    blocks: TimeBlock[];\n    activity: Activity;\n  }>();\n\n  // 收集每个活动的时间块\n  filledBlocks.forEach(block => {\n    const activity = activities.find(a => a.id === block.activityId);\n    if (activity) {\n      if (!activityStatsMap.has(activity.id)) {\n        activityStatsMap.set(activity.id, {\n          blocks: [],\n          activity\n        });\n      }\n      activityStatsMap.get(activity.id)!.blocks.push(block);\n    }\n  });\n\n  // 生成活动统计数据\n  const activityStats: ActivityStats[] = Array.from(activityStatsMap.entries()).map(\n    ([activityId, { blocks, activity }]) => {\n      const totalBlocks = blocks.length;\n      const totalMinutes = totalBlocks * 30;\n      const percentage = totalFilledMinutes > 0 ? (totalMinutes / totalFilledMinutes) * 100 : 0;\n      \n      // 生成时间段列表\n      const timeRanges = generateTimeRanges(blocks);\n\n      return {\n        activityId,\n        activityName: activity.name,\n        color: activity.color,\n        totalBlocks,\n        totalMinutes,\n        percentage,\n        timeRanges\n      };\n    }\n  );\n\n  // 按时间长度排序\n  activityStats.sort((a, b) => b.totalMinutes - a.totalMinutes);\n\n  return {\n    date: timeBlocks[0]?.date || '',\n    totalMinutes,\n    filledMinutes: totalFilledMinutes,\n    unfilledMinutes: totalMinutes - totalFilledMinutes,\n    filledPercentage: (totalFilledMinutes / totalMinutes) * 100,\n    activities: activityStats\n  };\n}\n\n/**\n * 将时间块转换为连续的时间段\n * @param blocks 时间块数组\n * @returns 时间段数组\n */\nexport function generateTimeRanges(blocks: TimeBlock[]): TimeRange[] {\n  if (blocks.length === 0) return [];\n\n  // 按时间槽排序\n  const sortedBlocks = [...blocks].sort((a, b) => a.timeSlot - b.timeSlot);\n  const ranges: TimeRange[] = [];\n  \n  interface CurrentRange {\n    startSlot: number;\n    endSlot: number;\n    startTime: string;\n    endTime: string;\n  }\n\n  let currentRange: CurrentRange | null = null;\n\n  sortedBlocks.forEach(block => {\n    if (currentRange === null) {\n      // 开始新的时间段\n      currentRange = {\n        startSlot: block.timeSlot,\n        endSlot: block.timeSlot,\n        startTime: block.startTime,\n        endTime: block.endTime\n      };\n    } else if (block.timeSlot === currentRange.endSlot + 1) {\n      // 连续的时间块，扩展当前时间段\n      currentRange.endSlot = block.timeSlot;\n      currentRange.endTime = block.endTime;\n    } else {\n      // 不连续，保存当前时间段并开始新的\n      const range = currentRange as CurrentRange;\n      ranges.push({\n        startTime: range.startTime,\n        endTime: range.endTime,\n        duration: (range.endSlot - range.startSlot + 1) * 30\n      });\n      \n      currentRange = {\n        startSlot: block.timeSlot,\n        endSlot: block.timeSlot,\n        startTime: block.startTime,\n        endTime: block.endTime\n      };\n    }\n  });\n\n  // 添加最后一个时间段\n  if (currentRange !== null) {\n    const range = currentRange as CurrentRange;\n    ranges.push({\n      startTime: range.startTime,\n      endTime: range.endTime,\n      duration: (range.endSlot - range.startSlot + 1) * 30\n    });\n  }\n\n  return ranges;\n}\n\n/**\n * 获取活动的总时长描述\n * @param stats 活动统计数据\n * @returns 格式化的时长字符串\n */\nexport function getActivityDurationText(stats: ActivityStats): string {\n  return formatDuration(stats.totalMinutes);\n}\n\n/**\n * 获取活动的时间段描述\n * @param timeRanges 时间段数组\n * @returns 时间段描述字符串\n */\nexport function getTimeRangesText(timeRanges: TimeRange[]): string {\n  if (timeRanges.length === 0) return '';\n  \n  if (timeRanges.length === 1) {\n    const range = timeRanges[0];\n    return `${range.startTime} - ${range.endTime}`;\n  }\n  \n  return `${timeRanges.length}个时间段`;\n}\n\n/**\n * 计算活动的效率分数（基于连续性）\n * @param timeRanges 时间段数组\n * @param totalMinutes 总分钟数\n * @returns 效率分数 (0-100)\n */\nexport function calculateEfficiencyScore(timeRanges: TimeRange[], totalMinutes: number): number {\n  if (timeRanges.length === 0 || totalMinutes === 0) return 0;\n  \n  // 连续性越高，效率分数越高\n  const averageDuration = totalMinutes / timeRanges.length;\n  const maxPossibleDuration = totalMinutes; // 如果全部连续\n  \n  return Math.min(100, (averageDuration / maxPossibleDuration) * 100);\n}\n\n/**\n * 获取一天中最活跃的时间段\n * @param dailyStats 每日统计数据\n * @returns 最活跃的活动统计\n */\nexport function getMostActiveActivity(dailyStats: DailyStats): ActivityStats | null {\n  if (dailyStats.activities.length === 0) return null;\n  \n  return dailyStats.activities.reduce((prev, current) => \n    current.totalMinutes > prev.totalMinutes ? current : prev\n  );\n}\n\n/**\n * 获取时间利用率等级\n * @param filledPercentage 填充百分比\n * @returns 等级描述\n */\nexport function getUtilizationLevel(filledPercentage: number): {\n  level: string;\n  color: string;\n  description: string;\n} {\n  if (filledPercentage >= 80) {\n    return {\n      level: '高效',\n      color: '#10B981', // green-500\n      description: '时间利用率很高'\n    };\n  } else if (filledPercentage >= 60) {\n    return {\n      level: '良好',\n      color: '#3B82F6', // blue-500\n      description: '时间利用率不错'\n    };\n  } else if (filledPercentage >= 40) {\n    return {\n      level: '一般',\n      color: '#F59E0B', // amber-500\n      description: '还有提升空间'\n    };\n  } else {\n    return {\n      level: '较低',\n      color: '#EF4444', // red-500\n      description: '建议记录更多时间'\n    };\n  }\n}\n\n/**\n * 生成每日总结文本\n * @param dailyStats 每日统计数据\n * @returns 总结文本\n */\nexport function generateDailySummary(dailyStats: DailyStats): string {\n  const { filledMinutes, activities } = dailyStats;\n  const filledHours = Math.floor(filledMinutes / 60);\n  const remainingMinutes = filledMinutes % 60;\n  \n  if (activities.length === 0) {\n    return '今天还没有记录任何活动';\n  }\n  \n  const mostActive = getMostActiveActivity(dailyStats);\n  const timeText = filledHours > 0 \n    ? `${filledHours}小时${remainingMinutes > 0 ? remainingMinutes + '分钟' : ''}`\n    : `${remainingMinutes}分钟`;\n  \n  return `今天记录了${timeText}，主要活动是${mostActive?.activityName}`;\n}\n"], "names": [], "mappings": ";;;;;;;;;;AACA;;AAQO,SAAS,oBACd,UAAuB,EACvB,UAAsB;IAEtB,aAAa;IACb,MAAM,eAAe,WAAW,MAAM,CAAC,CAAA,QAAS,MAAM,UAAU,KAAK;IACrE,MAAM,qBAAqB,aAAa,MAAM,GAAG;IACjD,MAAM,eAAe,KAAK,IAAI,SAAS;IAEvC,UAAU;IACV,MAAM,mBAAmB,IAAI;IAK7B,aAAa;IACb,aAAa,OAAO,CAAC,CAAA;QACnB,MAAM,WAAW,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,UAAU;QAC/D,IAAI,UAAU;YACZ,IAAI,CAAC,iBAAiB,GAAG,CAAC,SAAS,EAAE,GAAG;gBACtC,iBAAiB,GAAG,CAAC,SAAS,EAAE,EAAE;oBAChC,QAAQ,EAAE;oBACV;gBACF;YACF;YACA,iBAAiB,GAAG,CAAC,SAAS,EAAE,EAAG,MAAM,CAAC,IAAI,CAAC;QACjD;IACF;IAEA,WAAW;IACX,MAAM,gBAAiC,MAAM,IAAI,CAAC,iBAAiB,OAAO,IAAI,GAAG,CAC/E,CAAC,CAAC,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;QACjC,MAAM,cAAc,OAAO,MAAM;QACjC,MAAM,eAAe,cAAc;QACnC,MAAM,aAAa,qBAAqB,IAAI,AAAC,eAAe,qBAAsB,MAAM;QAExF,UAAU;QACV,MAAM,aAAa,mBAAmB;QAEtC,OAAO;YACL;YACA,cAAc,SAAS,IAAI;YAC3B,OAAO,SAAS,KAAK;YACrB;YACA;YACA;YACA;QACF;IACF;IAGF,UAAU;IACV,cAAc,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,YAAY,GAAG,EAAE,YAAY;IAE5D,OAAO;QACL,MAAM,UAAU,CAAC,EAAE,EAAE,QAAQ;QAC7B;QACA,eAAe;QACf,iBAAiB,eAAe;QAChC,kBAAkB,AAAC,qBAAqB,eAAgB;QACxD,YAAY;IACd;AACF;AAOO,SAAS,mBAAmB,MAAmB;IACpD,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO,EAAE;IAElC,SAAS;IACT,MAAM,eAAe;WAAI;KAAO,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;IACvE,MAAM,SAAsB,EAAE;IAS9B,IAAI,eAAoC;IAExC,aAAa,OAAO,CAAC,CAAA;QACnB,IAAI,iBAAiB,MAAM;YACzB,UAAU;YACV,eAAe;gBACb,WAAW,MAAM,QAAQ;gBACzB,SAAS,MAAM,QAAQ;gBACvB,WAAW,MAAM,SAAS;gBAC1B,SAAS,MAAM,OAAO;YACxB;QACF,OAAO,IAAI,MAAM,QAAQ,KAAK,aAAa,OAAO,GAAG,GAAG;YACtD,iBAAiB;YACjB,aAAa,OAAO,GAAG,MAAM,QAAQ;YACrC,aAAa,OAAO,GAAG,MAAM,OAAO;QACtC,OAAO;YACL,mBAAmB;YACnB,MAAM,QAAQ;YACd,OAAO,IAAI,CAAC;gBACV,WAAW,MAAM,SAAS;gBAC1B,SAAS,MAAM,OAAO;gBACtB,UAAU,CAAC,MAAM,OAAO,GAAG,MAAM,SAAS,GAAG,CAAC,IAAI;YACpD;YAEA,eAAe;gBACb,WAAW,MAAM,QAAQ;gBACzB,SAAS,MAAM,QAAQ;gBACvB,WAAW,MAAM,SAAS;gBAC1B,SAAS,MAAM,OAAO;YACxB;QACF;IACF;IAEA,YAAY;IACZ,IAAI,iBAAiB,MAAM;QACzB,MAAM,QAAQ;QACd,OAAO,IAAI,CAAC;YACV,WAAW,MAAM,SAAS;YAC1B,SAAS,MAAM,OAAO;YACtB,UAAU,CAAC,MAAM,OAAO,GAAG,MAAM,SAAS,GAAG,CAAC,IAAI;QACpD;IACF;IAEA,OAAO;AACT;AAOO,SAAS,wBAAwB,KAAoB;IAC1D,OAAO,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,YAAY;AAC1C;AAOO,SAAS,kBAAkB,UAAuB;IACvD,IAAI,WAAW,MAAM,KAAK,GAAG,OAAO;IAEpC,IAAI,WAAW,MAAM,KAAK,GAAG;QAC3B,MAAM,QAAQ,UAAU,CAAC,EAAE;QAC3B,OAAO,GAAG,MAAM,SAAS,CAAC,GAAG,EAAE,MAAM,OAAO,EAAE;IAChD;IAEA,OAAO,GAAG,WAAW,MAAM,CAAC,IAAI,CAAC;AACnC;AAQO,SAAS,yBAAyB,UAAuB,EAAE,YAAoB;IACpF,IAAI,WAAW,MAAM,KAAK,KAAK,iBAAiB,GAAG,OAAO;IAE1D,eAAe;IACf,MAAM,kBAAkB,eAAe,WAAW,MAAM;IACxD,MAAM,sBAAsB,cAAc,SAAS;IAEnD,OAAO,KAAK,GAAG,CAAC,KAAK,AAAC,kBAAkB,sBAAuB;AACjE;AAOO,SAAS,sBAAsB,UAAsB;IAC1D,IAAI,WAAW,UAAU,CAAC,MAAM,KAAK,GAAG,OAAO;IAE/C,OAAO,WAAW,UAAU,CAAC,MAAM,CAAC,CAAC,MAAM,UACzC,QAAQ,YAAY,GAAG,KAAK,YAAY,GAAG,UAAU;AAEzD;AAOO,SAAS,oBAAoB,gBAAwB;IAK1D,IAAI,oBAAoB,IAAI;QAC1B,OAAO;YACL,OAAO;YACP,OAAO;YACP,aAAa;QACf;IACF,OAAO,IAAI,oBAAoB,IAAI;QACjC,OAAO;YACL,OAAO;YACP,OAAO;YACP,aAAa;QACf;IACF,OAAO,IAAI,oBAAoB,IAAI;QACjC,OAAO;YACL,OAAO;YACP,OAAO;YACP,aAAa;QACf;IACF,OAAO;QACL,OAAO;YACL,OAAO;YACP,OAAO;YACP,aAAa;QACf;IACF;AACF;AAOO,SAAS,qBAAqB,UAAsB;IACzD,MAAM,EAAE,aAAa,EAAE,UAAU,EAAE,GAAG;IACtC,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,MAAM,mBAAmB,gBAAgB;IAEzC,IAAI,WAAW,MAAM,KAAK,GAAG;QAC3B,OAAO;IACT;IAEA,MAAM,aAAa,sBAAsB;IACzC,MAAM,WAAW,cAAc,IAC3B,GAAG,YAAY,EAAE,EAAE,mBAAmB,IAAI,mBAAmB,OAAO,IAAI,GACxE,GAAG,iBAAiB,EAAE,CAAC;IAE3B,OAAO,CAAC,KAAK,EAAE,SAAS,MAAM,EAAE,YAAY,cAAc;AAC5D", "debugId": null}}, {"offset": {"line": 543, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/utils/cacheUtils.ts"], "sourcesContent": ["/**\r\n * LRU缓存实现，用于优化统计数据的缓存管理\r\n */\r\nexport class LRUCache<K, V> {\r\n  private capacity: number;\r\n  private cache: Map<K, V>;\r\n\r\n  constructor(capacity: number = 50) {\r\n    this.capacity = capacity;\r\n    this.cache = new Map();\r\n  }\r\n\r\n  get(key: K): V | undefined {\r\n    if (this.cache.has(key)) {\r\n      // 移动到最新位置\r\n      const value = this.cache.get(key)!;\r\n      this.cache.delete(key);\r\n      this.cache.set(key, value);\r\n      return value;\r\n    }\r\n    return undefined;\r\n  }\r\n\r\n  set(key: K, value: V): void {\r\n    if (this.cache.has(key)) {\r\n      // 更新现有值\r\n      this.cache.delete(key);\r\n    } else if (this.cache.size >= this.capacity) {\r\n      // 删除最旧的项\r\n      const firstKey = this.cache.keys().next().value;\r\n      this.cache.delete(firstKey);\r\n    }\r\n    this.cache.set(key, value);\r\n  }\r\n\r\n  delete(key: K): boolean {\r\n    return this.cache.delete(key);\r\n  }\r\n\r\n  clear(): void {\r\n    this.cache.clear();\r\n  }\r\n\r\n  size(): number {\r\n    return this.cache.size;\r\n  }\r\n\r\n  has(key: K): boolean {\r\n    return this.cache.has(key);\r\n  }\r\n\r\n  keys(): IterableIterator<K> {\r\n    return this.cache.keys();\r\n  }\r\n}\r\n\r\n/**\r\n * 数据预加载管理器\r\n */\r\nexport class DataPreloader {\r\n  private static instance: DataPreloader;\r\n  private preloadQueue: Set<string> = new Set();\r\n  private isPreloading = false;\r\n\r\n  static getInstance(): DataPreloader {\r\n    if (!DataPreloader.instance) {\r\n      DataPreloader.instance = new DataPreloader();\r\n    }\r\n    return DataPreloader.instance;\r\n  }\r\n\r\n  /**\r\n   * 添加日期到预加载队列\r\n   */\r\n  addToQueue(date: string): void {\r\n    this.preloadQueue.add(date);\r\n    this.processQueue();\r\n  }\r\n\r\n  /**\r\n   * 批量添加日期到预加载队列\r\n   */\r\n  addMultipleToQueue(dates: string[]): void {\r\n    dates.forEach(date => this.preloadQueue.add(date));\r\n    this.processQueue();\r\n  }\r\n\r\n  /**\r\n   * 处理预加载队列\r\n   */\r\n  private async processQueue(): Promise<void> {\r\n    if (this.isPreloading || this.preloadQueue.size === 0) {\r\n      return;\r\n    }\r\n\r\n    this.isPreloading = true;\r\n\r\n    try {\r\n      // 使用requestIdleCallback在浏览器空闲时预加载\r\n      if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {\r\n        window.requestIdleCallback(() => {\r\n          this.performPreload();\r\n        });\r\n      } else {\r\n        // 降级到setTimeout\r\n        setTimeout(() => {\r\n          this.performPreload();\r\n        }, 100);\r\n      }\r\n    } finally {\r\n      this.isPreloading = false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 执行预加载\r\n   */\r\n  private performPreload(): void {\r\n    const dates = Array.from(this.preloadQueue);\r\n    this.preloadQueue.clear();\r\n\r\n    // 这里会在store中实现具体的预加载逻辑\r\n    if (typeof window !== 'undefined') {\r\n      window.dispatchEvent(new CustomEvent('preloadDates', { \r\n        detail: { dates } \r\n      }));\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 清空预加载队列\r\n   */\r\n  clearQueue(): void {\r\n    this.preloadQueue.clear();\r\n  }\r\n}\r\n\r\n/**\r\n * 获取相邻日期列表，用于预加载\r\n */\r\nexport function getAdjacentDates(currentDate: string, range: number = 3): string[] {\r\n  const dates: string[] = [];\r\n  const baseDate = new Date(currentDate);\r\n\r\n  for (let i = -range; i <= range; i++) {\r\n    if (i === 0) continue; // 跳过当前日期\r\n    const date = new Date(baseDate);\r\n    date.setDate(baseDate.getDate() + i);\r\n    dates.push(date.toISOString().split('T')[0]);\r\n  }\r\n\r\n  return dates;\r\n}\r\n\r\n/**\r\n * 内存使用监控器\r\n */\r\nexport class MemoryMonitor {\r\n  private static instance: MemoryMonitor;\r\n  private memoryThreshold = 50 * 1024 * 1024; // 50MB\r\n\r\n  static getInstance(): MemoryMonitor {\r\n    if (!MemoryMonitor.instance) {\r\n      MemoryMonitor.instance = new MemoryMonitor();\r\n    }\r\n    return MemoryMonitor.instance;\r\n  }\r\n\r\n  /**\r\n   * 检查内存使用情况\r\n   */\r\n  checkMemoryUsage(): boolean {\r\n    if (typeof window === 'undefined' || !('performance' in window)) {\r\n      return false;\r\n    }\r\n\r\n    try {\r\n      // @ts-ignore - performance.memory可能不存在\r\n      const memory = window.performance.memory;\r\n      if (memory && memory.usedJSHeapSize > this.memoryThreshold) {\r\n        return true; // 内存使用过高\r\n      }\r\n    } catch (error) {\r\n      console.warn('无法检查内存使用情况:', error);\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  /**\r\n   * 设置内存阈值\r\n   */\r\n  setThreshold(threshold: number): void {\r\n    this.memoryThreshold = threshold;\r\n  }\r\n}\r\n\r\n/**\r\n * 缓存性能统计\r\n */\r\nexport interface CacheStats {\r\n  hits: number;\r\n  misses: number;\r\n  hitRate: number;\r\n  size: number;\r\n  capacity: number;\r\n}\r\n\r\n/**\r\n * 带统计功能的LRU缓存\r\n */\r\nexport class StatisticalLRUCache<K, V> extends LRUCache<K, V> {\r\n  private hits = 0;\r\n  private misses = 0;\r\n\r\n  get(key: K): V | undefined {\r\n    const value = super.get(key);\r\n    if (value !== undefined) {\r\n      this.hits++;\r\n    } else {\r\n      this.misses++;\r\n    }\r\n    return value;\r\n  }\r\n\r\n  getStats(): CacheStats {\r\n    const total = this.hits + this.misses;\r\n    return {\r\n      hits: this.hits,\r\n      misses: this.misses,\r\n      hitRate: total > 0 ? this.hits / total : 0,\r\n      size: this.size(),\r\n      capacity: this.capacity\r\n    };\r\n  }\r\n\r\n  resetStats(): void {\r\n    this.hits = 0;\r\n    this.misses = 0;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;AACM,MAAM;IACH,SAAiB;IACjB,MAAiB;IAEzB,YAAY,WAAmB,EAAE,CAAE;QACjC,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,KAAK,GAAG,IAAI;IACnB;IAEA,IAAI,GAAM,EAAiB;QACzB,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM;YACvB,UAAU;YACV,MAAM,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;YAC7B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YAClB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK;YACpB,OAAO;QACT;QACA,OAAO;IACT;IAEA,IAAI,GAAM,EAAE,KAAQ,EAAQ;QAC1B,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM;YACvB,QAAQ;YACR,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;QACpB,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;YAC3C,SAAS;YACT,MAAM,WAAW,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,GAAG,KAAK;YAC/C,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;QACpB;QACA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK;IACtB;IAEA,OAAO,GAAM,EAAW;QACtB,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IAC3B;IAEA,QAAc;QACZ,IAAI,CAAC,KAAK,CAAC,KAAK;IAClB;IAEA,OAAe;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI;IACxB;IAEA,IAAI,GAAM,EAAW;QACnB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;IACxB;IAEA,OAA4B;QAC1B,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI;IACxB;AACF;AAKO,MAAM;IACX,OAAe,SAAwB;IAC/B,eAA4B,IAAI,MAAM;IACtC,eAAe,MAAM;IAE7B,OAAO,cAA6B;QAClC,IAAI,CAAC,cAAc,QAAQ,EAAE;YAC3B,cAAc,QAAQ,GAAG,IAAI;QAC/B;QACA,OAAO,cAAc,QAAQ;IAC/B;IAEA;;GAEC,GACD,WAAW,IAAY,EAAQ;QAC7B,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;QACtB,IAAI,CAAC,YAAY;IACnB;IAEA;;GAEC,GACD,mBAAmB,KAAe,EAAQ;QACxC,MAAM,OAAO,CAAC,CAAA,OAAQ,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;QAC5C,IAAI,CAAC,YAAY;IACnB;IAEA;;GAEC,GACD,MAAc,eAA8B;QAC1C,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,KAAK,GAAG;YACrD;QACF;QAEA,IAAI,CAAC,YAAY,GAAG;QAEpB,IAAI;YACF,kCAAkC;YAClC,IAAI,gBAAkB,eAAe,yBAAyB;;iBAIvD;gBACL,gBAAgB;gBAChB,WAAW;oBACT,IAAI,CAAC,cAAc;gBACrB,GAAG;YACL;QACF,SAAU;YACR,IAAI,CAAC,YAAY,GAAG;QACtB;IACF;IAEA;;GAEC,GACD,AAAQ,iBAAuB;QAC7B,MAAM,QAAQ,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY;QAC1C,IAAI,CAAC,YAAY,CAAC,KAAK;QAEvB,uBAAuB;QACvB;;IAKF;IAEA;;GAEC,GACD,aAAmB;QACjB,IAAI,CAAC,YAAY,CAAC,KAAK;IACzB;AACF;AAKO,SAAS,iBAAiB,WAAmB,EAAE,QAAgB,CAAC;IACrE,MAAM,QAAkB,EAAE;IAC1B,MAAM,WAAW,IAAI,KAAK;IAE1B,IAAK,IAAI,IAAI,CAAC,OAAO,KAAK,OAAO,IAAK;QACpC,IAAI,MAAM,GAAG,UAAU,SAAS;QAChC,MAAM,OAAO,IAAI,KAAK;QACtB,KAAK,OAAO,CAAC,SAAS,OAAO,KAAK;QAClC,MAAM,IAAI,CAAC,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IAC7C;IAEA,OAAO;AACT;AAKO,MAAM;IACX,OAAe,SAAwB;IAC/B,kBAAkB,KAAK,OAAO,KAAK;IAE3C,OAAO,cAA6B;QAClC,IAAI,CAAC,cAAc,QAAQ,EAAE;YAC3B,cAAc,QAAQ,GAAG,IAAI;QAC/B;QACA,OAAO,cAAc,QAAQ;IAC/B;IAEA;;GAEC,GACD,mBAA4B;QAC1B,IAAI,gBAAkB,eAAe,CAAC,CAAC,iBAAiB,MAAM,GAAG;YAC/D,OAAO;QACT;;;IAaF;IAEA;;GAEC,GACD,aAAa,SAAiB,EAAQ;QACpC,IAAI,CAAC,eAAe,GAAG;IACzB;AACF;AAgBO,MAAM,4BAAkC;IACrC,OAAO,EAAE;IACT,SAAS,EAAE;IAEnB,IAAI,GAAM,EAAiB;QACzB,MAAM,QAAQ,KAAK,CAAC,IAAI;QACxB,IAAI,UAAU,WAAW;YACvB,IAAI,CAAC,IAAI;QACX,OAAO;YACL,IAAI,CAAC,MAAM;QACb;QACA,OAAO;IACT;IAEA,WAAuB;QACrB,MAAM,QAAQ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM;QACrC,OAAO;YACL,MAAM,IAAI,CAAC,IAAI;YACf,QAAQ,IAAI,CAAC,MAAM;YACnB,SAAS,QAAQ,IAAI,IAAI,CAAC,IAAI,GAAG,QAAQ;YACzC,MAAM,IAAI,CAAC,IAAI;YACf,UAAU,IAAI,CAAC,QAAQ;QACzB;IACF;IAEA,aAAmB;QACjB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG;IAChB;AACF", "debugId": null}}, {"offset": {"line": 720, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/utils/loadingUtils.ts"], "sourcesContent": ["/**\n * 加载状态管理工具\n */\n\nexport type LoadingState = 'idle' | 'loading' | 'success' | 'error';\n\nexport interface LoadingStatus {\n  state: LoadingState;\n  message?: string;\n  progress?: number;\n  startTime?: number;\n}\n\n/**\n * 加载状态管理器\n */\nexport class LoadingManager {\n  private static instance: LoadingManager;\n  private loadingStates: Map<string, LoadingStatus> = new Map();\n  private listeners: Map<string, Set<(status: LoadingStatus) => void>> = new Map();\n\n  static getInstance(): LoadingManager {\n    if (!LoadingManager.instance) {\n      LoadingManager.instance = new LoadingManager();\n    }\n    return LoadingManager.instance;\n  }\n\n  /**\n   * 设置加载状态\n   */\n  setLoadingState(key: string, state: LoadingState, message?: string, progress?: number): void {\n    const currentStatus = this.loadingStates.get(key);\n    const newStatus: LoadingStatus = {\n      state,\n      message,\n      progress,\n      startTime: state === 'loading' ? Date.now() : currentStatus?.startTime\n    };\n\n    this.loadingStates.set(key, newStatus);\n    this.notifyListeners(key, newStatus);\n  }\n\n  /**\n   * 获取加载状态\n   */\n  getLoadingState(key: string): LoadingStatus {\n    return this.loadingStates.get(key) || { state: 'idle' };\n  }\n\n  /**\n   * 监听加载状态变化\n   */\n  subscribe(key: string, callback: (status: LoadingStatus) => void): () => void {\n    if (!this.listeners.has(key)) {\n      this.listeners.set(key, new Set());\n    }\n    this.listeners.get(key)!.add(callback);\n\n    // 返回取消订阅函数\n    return () => {\n      const listeners = this.listeners.get(key);\n      if (listeners) {\n        listeners.delete(callback);\n        if (listeners.size === 0) {\n          this.listeners.delete(key);\n        }\n      }\n    };\n  }\n\n  /**\n   * 通知监听器\n   */\n  private notifyListeners(key: string, status: LoadingStatus): void {\n    const listeners = this.listeners.get(key);\n    if (listeners) {\n      listeners.forEach(callback => callback(status));\n    }\n  }\n\n  /**\n   * 清除加载状态\n   */\n  clearLoadingState(key: string): void {\n    this.loadingStates.delete(key);\n    this.notifyListeners(key, { state: 'idle' });\n  }\n\n  /**\n   * 清除所有加载状态\n   */\n  clearAllLoadingStates(): void {\n    const keys = Array.from(this.loadingStates.keys());\n    this.loadingStates.clear();\n    keys.forEach(key => this.notifyListeners(key, { state: 'idle' }));\n  }\n\n  /**\n   * 获取加载持续时间\n   */\n  getLoadingDuration(key: string): number {\n    const status = this.loadingStates.get(key);\n    if (status?.startTime && status.state === 'loading') {\n      return Date.now() - status.startTime;\n    }\n    return 0;\n  }\n}\n\n/**\n * 防抖加载函数\n */\nexport function debounceLoading<T extends any[]>(\n  fn: (...args: T) => Promise<any>,\n  delay: number = 300\n): (...args: T) => Promise<any> {\n  let timeoutId: NodeJS.Timeout;\n  let lastPromise: Promise<any> | null = null;\n\n  return (...args: T): Promise<any> => {\n    return new Promise((resolve, reject) => {\n      clearTimeout(timeoutId);\n      \n      timeoutId = setTimeout(async () => {\n        try {\n          const result = await fn(...args);\n          lastPromise = null;\n          resolve(result);\n        } catch (error) {\n          lastPromise = null;\n          reject(error);\n        }\n      }, delay);\n    });\n  };\n}\n\n/**\n * 批量加载管理器\n */\nexport class BatchLoader<T, R> {\n  private batchSize: number;\n  private batchTimeout: number;\n  private pendingItems: T[] = [];\n  private pendingPromises: Array<{\n    resolve: (value: R[]) => void;\n    reject: (error: any) => void;\n  }> = [];\n  private timeoutId: NodeJS.Timeout | null = null;\n\n  constructor(\n    private loadFn: (items: T[]) => Promise<R[]>,\n    batchSize: number = 10,\n    batchTimeout: number = 100\n  ) {\n    this.batchSize = batchSize;\n    this.batchTimeout = batchTimeout;\n  }\n\n  /**\n   * 添加项目到批量加载队列\n   */\n  load(item: T): Promise<R> {\n    return new Promise<R>((resolve, reject) => {\n      this.pendingItems.push(item);\n      this.pendingPromises.push({\n        resolve: (results: R[]) => {\n          const index = this.pendingItems.length - 1;\n          resolve(results[index]);\n        },\n        reject\n      });\n\n      this.scheduleFlush();\n    });\n  }\n\n  /**\n   * 调度批量处理\n   */\n  private scheduleFlush(): void {\n    if (this.timeoutId) {\n      clearTimeout(this.timeoutId);\n    }\n\n    if (this.pendingItems.length >= this.batchSize) {\n      this.flush();\n    } else {\n      this.timeoutId = setTimeout(() => {\n        this.flush();\n      }, this.batchTimeout);\n    }\n  }\n\n  /**\n   * 执行批量加载\n   */\n  private async flush(): Promise<void> {\n    if (this.pendingItems.length === 0) {\n      return;\n    }\n\n    const items = [...this.pendingItems];\n    const promises = [...this.pendingPromises];\n    \n    this.pendingItems = [];\n    this.pendingPromises = [];\n    this.timeoutId = null;\n\n    try {\n      const results = await this.loadFn(items);\n      promises.forEach(({ resolve }) => resolve(results));\n    } catch (error) {\n      promises.forEach(({ reject }) => reject(error));\n    }\n  }\n}\n\n/**\n * 创建加载状态Hook的工厂函数\n */\nexport function createLoadingHook(key: string) {\n  const manager = LoadingManager.getInstance();\n  \n  return function useLoading() {\n    const [status, setStatus] = React.useState<LoadingStatus>(\n      manager.getLoadingState(key)\n    );\n\n    React.useEffect(() => {\n      const unsubscribe = manager.subscribe(key, setStatus);\n      return unsubscribe;\n    }, []);\n\n    const setLoading = React.useCallback((\n      state: LoadingState, \n      message?: string, \n      progress?: number\n    ) => {\n      manager.setLoadingState(key, state, message, progress);\n    }, []);\n\n    return {\n      ...status,\n      setLoading,\n      isLoading: status.state === 'loading',\n      isError: status.state === 'error',\n      isSuccess: status.state === 'success',\n      duration: manager.getLoadingDuration(key)\n    };\n  };\n}\n\n// 为了兼容性，添加React导入\nimport React from 'react';\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;AA6PD,kBAAkB;AAClB;AAhPO,MAAM;IACX,OAAe,SAAyB;IAChC,gBAA4C,IAAI,MAAM;IACtD,YAA+D,IAAI,MAAM;IAEjF,OAAO,cAA8B;QACnC,IAAI,CAAC,eAAe,QAAQ,EAAE;YAC5B,eAAe,QAAQ,GAAG,IAAI;QAChC;QACA,OAAO,eAAe,QAAQ;IAChC;IAEA;;GAEC,GACD,gBAAgB,GAAW,EAAE,KAAmB,EAAE,OAAgB,EAAE,QAAiB,EAAQ;QAC3F,MAAM,gBAAgB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;QAC7C,MAAM,YAA2B;YAC/B;YACA;YACA;YACA,WAAW,UAAU,YAAY,KAAK,GAAG,KAAK,eAAe;QAC/D;QAEA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK;QAC5B,IAAI,CAAC,eAAe,CAAC,KAAK;IAC5B;IAEA;;GAEC,GACD,gBAAgB,GAAW,EAAiB;QAC1C,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ;YAAE,OAAO;QAAO;IACxD;IAEA;;GAEC,GACD,UAAU,GAAW,EAAE,QAAyC,EAAc;QAC5E,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM;YAC5B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,IAAI;QAC9B;QACA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAM,GAAG,CAAC;QAE7B,WAAW;QACX,OAAO;YACL,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;YACrC,IAAI,WAAW;gBACb,UAAU,MAAM,CAAC;gBACjB,IAAI,UAAU,IAAI,KAAK,GAAG;oBACxB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;gBACxB;YACF;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,gBAAgB,GAAW,EAAE,MAAqB,EAAQ;QAChE,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QACrC,IAAI,WAAW;YACb,UAAU,OAAO,CAAC,CAAA,WAAY,SAAS;QACzC;IACF;IAEA;;GAEC,GACD,kBAAkB,GAAW,EAAQ;QACnC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;QAC1B,IAAI,CAAC,eAAe,CAAC,KAAK;YAAE,OAAO;QAAO;IAC5C;IAEA;;GAEC,GACD,wBAA8B;QAC5B,MAAM,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI;QAC/C,IAAI,CAAC,aAAa,CAAC,KAAK;QACxB,KAAK,OAAO,CAAC,CAAA,MAAO,IAAI,CAAC,eAAe,CAAC,KAAK;gBAAE,OAAO;YAAO;IAChE;IAEA;;GAEC,GACD,mBAAmB,GAAW,EAAU;QACtC,MAAM,SAAS,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;QACtC,IAAI,QAAQ,aAAa,OAAO,KAAK,KAAK,WAAW;YACnD,OAAO,KAAK,GAAG,KAAK,OAAO,SAAS;QACtC;QACA,OAAO;IACT;AACF;AAKO,SAAS,gBACd,EAAgC,EAChC,QAAgB,GAAG;IAEnB,IAAI;IACJ,IAAI,cAAmC;IAEvC,OAAO,CAAC,GAAG;QACT,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,aAAa;YAEb,YAAY,WAAW;gBACrB,IAAI;oBACF,MAAM,SAAS,MAAM,MAAM;oBAC3B,cAAc;oBACd,QAAQ;gBACV,EAAE,OAAO,OAAO;oBACd,cAAc;oBACd,OAAO;gBACT;YACF,GAAG;QACL;IACF;AACF;AAKO,MAAM;;IACH,UAAkB;IAClB,aAAqB;IACrB,aAAuB;IACvB,gBAGA;IACA,UAAwC;IAEhD,YACE,AAAQ,MAAoC,EAC5C,YAAoB,EAAE,EACtB,eAAuB,GAAG,CAC1B;aAHQ,SAAA;aARF,eAAoB,EAAE;aACtB,kBAGH,EAAE;aACC,YAAmC;QAOzC,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,YAAY,GAAG;IACtB;IAEA;;GAEC,GACD,KAAK,IAAO,EAAc;QACxB,OAAO,IAAI,QAAW,CAAC,SAAS;YAC9B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;YACvB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;gBACxB,SAAS,CAAC;oBACR,MAAM,QAAQ,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG;oBACzC,QAAQ,OAAO,CAAC,MAAM;gBACxB;gBACA;YACF;YAEA,IAAI,CAAC,aAAa;QACpB;IACF;IAEA;;GAEC,GACD,AAAQ,gBAAsB;QAC5B,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,aAAa,IAAI,CAAC,SAAS;QAC7B;QAEA,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,EAAE;YAC9C,IAAI,CAAC,KAAK;QACZ,OAAO;YACL,IAAI,CAAC,SAAS,GAAG,WAAW;gBAC1B,IAAI,CAAC,KAAK;YACZ,GAAG,IAAI,CAAC,YAAY;QACtB;IACF;IAEA;;GAEC,GACD,MAAc,QAAuB;QACnC,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,GAAG;YAClC;QACF;QAEA,MAAM,QAAQ;eAAI,IAAI,CAAC,YAAY;SAAC;QACpC,MAAM,WAAW;eAAI,IAAI,CAAC,eAAe;SAAC;QAE1C,IAAI,CAAC,YAAY,GAAG,EAAE;QACtB,IAAI,CAAC,eAAe,GAAG,EAAE;QACzB,IAAI,CAAC,SAAS,GAAG;QAEjB,IAAI;YACF,MAAM,UAAU,MAAM,IAAI,CAAC,MAAM,CAAC;YAClC,SAAS,OAAO,CAAC,CAAC,EAAE,OAAO,EAAE,GAAK,QAAQ;QAC5C,EAAE,OAAO,OAAO;YACd,SAAS,OAAO,CAAC,CAAC,EAAE,MAAM,EAAE,GAAK,OAAO;QAC1C;IACF;AACF;AAKO,SAAS,kBAAkB,GAAW;IAC3C,MAAM,UAAU,eAAe,WAAW;IAE1C,OAAO,SAAS;QACd,MAAM,CAAC,QAAQ,UAAU,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CACxC,QAAQ,eAAe,CAAC;QAG1B,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;YACd,MAAM,cAAc,QAAQ,SAAS,CAAC,KAAK;YAC3C,OAAO;QACT,GAAG,EAAE;QAEL,MAAM,aAAa,qMAAA,CAAA,UAAK,CAAC,WAAW,CAAC,CACnC,OACA,SACA;YAEA,QAAQ,eAAe,CAAC,KAAK,OAAO,SAAS;QAC/C,GAAG,EAAE;QAEL,OAAO;YACL,GAAG,MAAM;YACT;YACA,WAAW,OAAO,KAAK,KAAK;YAC5B,SAAS,OAAO,KAAK,KAAK;YAC1B,WAAW,OAAO,KAAK,KAAK;YAC5B,UAAU,QAAQ,kBAAkB,CAAC;QACvC;IACF;AACF", "debugId": null}}, {"offset": {"line": 925, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/utils/smartTimeUpdater.ts"], "sourcesContent": ["/**\r\n * 智能精确时间触发器\r\n * 基于业界最佳实践实现的高精度时间更新机制\r\n * \r\n * 核心特性：\r\n * - 精确分钟边界触发\r\n * - 页面可见性感知\r\n * - 递归setTimeout机制\r\n * - 错误恢复和重新同步\r\n * - 性能监控和调试支持\r\n */\r\n\r\nimport { \r\n  calculateNextMinuteDelay, \r\n  calculateNextTimeSlotDelay, \r\n  getHighPrecisionTimestamp,\r\n  validateTriggerPrecision \r\n} from './timeUtils';\r\n\r\nexport interface SmartTimeUpdaterOptions {\r\n  /** 时间更新回调函数 */\r\n  onTimeUpdate: () => void;\r\n  /** 是否启用调试模式 */\r\n  debug?: boolean;\r\n  /** 是否使用时间槽边界触发（30分钟），默认为分钟边界触发 */\r\n  useTimeSlotBoundary?: boolean;\r\n  /** 错误恢复检查间隔（毫秒），默认5分钟 */\r\n  recoveryCheckInterval?: number;\r\n  /** 页面可见性变化时是否立即同步，默认true */\r\n  syncOnVisibilityChange?: boolean;\r\n}\r\n\r\nexport interface TriggerStats {\r\n  /** 总触发次数 */\r\n  totalTriggers: number;\r\n  /** 精确触发次数（100ms内） */\r\n  accurateTriggers: number;\r\n  /** 平均偏差（毫秒） */\r\n  averageDeviation: number;\r\n  /** 最大偏差（毫秒） */\r\n  maxDeviation: number;\r\n  /** 最后触发时间 */\r\n  lastTriggerTime: number;\r\n  /** 页面隐藏期间跳过的触发次数 */\r\n  skippedTriggers: number;\r\n}\r\n\r\n/**\r\n * 智能时间更新器类\r\n * 实现精确的时间边界触发机制\r\n */\r\nexport class SmartTimeUpdater {\r\n  private options: Required<SmartTimeUpdaterOptions>;\r\n  private timeoutId: number | null = null;\r\n  private recoveryIntervalId: number | null = null;\r\n  private isRunning = false;\r\n  private isPageVisible = true;\r\n  private stats: TriggerStats;\r\n  private lastExpectedTriggerTime = 0;\r\n\r\n  constructor(options: SmartTimeUpdaterOptions) {\r\n    this.options = {\r\n      debug: false,\r\n      useTimeSlotBoundary: false,\r\n      recoveryCheckInterval: 5 * 60 * 1000, // 5分钟\r\n      syncOnVisibilityChange: true,\r\n      ...options\r\n    };\r\n\r\n    this.stats = {\r\n      totalTriggers: 0,\r\n      accurateTriggers: 0,\r\n      averageDeviation: 0,\r\n      maxDeviation: 0,\r\n      lastTriggerTime: 0,\r\n      skippedTriggers: 0\r\n    };\r\n\r\n    this.setupVisibilityListener();\r\n  }\r\n\r\n  /**\r\n   * 启动智能时间更新器\r\n   */\r\n  start(): void {\r\n    if (this.isRunning) {\r\n      this.log('SmartTimeUpdater already running');\r\n      return;\r\n    }\r\n\r\n    this.isRunning = true;\r\n    this.log('Starting SmartTimeUpdater');\r\n    \r\n    // 立即执行一次更新\r\n    this.options.onTimeUpdate();\r\n    \r\n    // 开始调度下一次更新\r\n    this.scheduleNextUpdate();\r\n    \r\n    // 启动错误恢复检查\r\n    this.startRecoveryCheck();\r\n  }\r\n\r\n  /**\r\n   * 停止智能时间更新器\r\n   */\r\n  stop(): void {\r\n    if (!this.isRunning) {\r\n      return;\r\n    }\r\n\r\n    this.isRunning = false;\r\n    this.log('Stopping SmartTimeUpdater');\r\n    \r\n    // 清理定时器\r\n    if (this.timeoutId !== null) {\r\n      clearTimeout(this.timeoutId);\r\n      this.timeoutId = null;\r\n    }\r\n    \r\n    if (this.recoveryIntervalId !== null) {\r\n      clearInterval(this.recoveryIntervalId);\r\n      this.recoveryIntervalId = null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 获取触发统计信息\r\n   */\r\n  getStats(): TriggerStats {\r\n    return { ...this.stats };\r\n  }\r\n\r\n  /**\r\n   * 重置统计信息\r\n   */\r\n  resetStats(): void {\r\n    this.stats = {\r\n      totalTriggers: 0,\r\n      accurateTriggers: 0,\r\n      averageDeviation: 0,\r\n      maxDeviation: 0,\r\n      lastTriggerTime: 0,\r\n      skippedTriggers: 0\r\n    };\r\n  }\r\n\r\n  /**\r\n   * 调度下一次更新\r\n   */\r\n  private scheduleNextUpdate(): void {\r\n    if (!this.isRunning) {\r\n      return;\r\n    }\r\n\r\n    // 计算到下一个边界的延迟\r\n    const delay = this.options.useTimeSlotBoundary \r\n      ? calculateNextTimeSlotDelay() \r\n      : calculateNextMinuteDelay();\r\n\r\n    // 记录期望的触发时间\r\n    this.lastExpectedTriggerTime = getHighPrecisionTimestamp() + delay;\r\n\r\n    this.log(`Scheduling next update in ${delay}ms`);\r\n\r\n    // 使用递归setTimeout实现精确调度\r\n    this.timeoutId = window.setTimeout(() => {\r\n      this.handleTrigger();\r\n    }, delay);\r\n  }\r\n\r\n  /**\r\n   * 处理时间触发\r\n   */\r\n  private handleTrigger(): void {\r\n    const actualTriggerTime = getHighPrecisionTimestamp();\r\n    \r\n    // 验证触发精度\r\n    if (this.lastExpectedTriggerTime > 0) {\r\n      const precision = validateTriggerPrecision(\r\n        this.lastExpectedTriggerTime, \r\n        actualTriggerTime\r\n      );\r\n      this.updateStats(precision);\r\n    }\r\n\r\n    // 如果页面不可见，跳过更新但继续调度\r\n    if (!this.isPageVisible) {\r\n      this.stats.skippedTriggers++;\r\n      this.log('Page not visible, skipping update');\r\n      this.scheduleNextUpdate();\r\n      return;\r\n    }\r\n\r\n    // 执行时间更新\r\n    this.options.onTimeUpdate();\r\n    this.stats.lastTriggerTime = actualTriggerTime;\r\n    \r\n    this.log('Time update triggered');\r\n    \r\n    // 调度下一次更新\r\n    this.scheduleNextUpdate();\r\n  }\r\n\r\n  /**\r\n   * 更新统计信息\r\n   */\r\n  private updateStats(precision: ReturnType<typeof validateTriggerPrecision>): void {\r\n    this.stats.totalTriggers++;\r\n    \r\n    if (precision.isAccurate) {\r\n      this.stats.accurateTriggers++;\r\n    }\r\n    \r\n    // 更新平均偏差\r\n    const totalDeviation = this.stats.averageDeviation * (this.stats.totalTriggers - 1) + precision.deviation;\r\n    this.stats.averageDeviation = totalDeviation / this.stats.totalTriggers;\r\n    \r\n    // 更新最大偏差\r\n    this.stats.maxDeviation = Math.max(this.stats.maxDeviation, precision.deviation);\r\n    \r\n    this.log(`Trigger precision: ${precision.deviation.toFixed(2)}ms deviation`);\r\n  }\r\n\r\n  /**\r\n   * 设置页面可见性监听器\r\n   */\r\n  private setupVisibilityListener(): void {\r\n    if (typeof document === 'undefined') {\r\n      return;\r\n    }\r\n\r\n    const handleVisibilityChange = () => {\r\n      const wasVisible = this.isPageVisible;\r\n      this.isPageVisible = !document.hidden;\r\n      \r\n      this.log(`Page visibility changed: ${this.isPageVisible ? 'visible' : 'hidden'}`);\r\n      \r\n      // 页面从隐藏变为可见时，重新同步\r\n      if (!wasVisible && this.isPageVisible && this.options.syncOnVisibilityChange) {\r\n        this.log('Page became visible, re-syncing...');\r\n        this.resync();\r\n      }\r\n    };\r\n\r\n    document.addEventListener('visibilitychange', handleVisibilityChange);\r\n  }\r\n\r\n  /**\r\n   * 启动错误恢复检查\r\n   */\r\n  private startRecoveryCheck(): void {\r\n    this.recoveryIntervalId = window.setInterval(() => {\r\n      if (!this.isRunning) {\r\n        return;\r\n      }\r\n\r\n      // 检查是否需要重新同步\r\n      const now = getHighPrecisionTimestamp();\r\n      const timeSinceLastTrigger = now - this.stats.lastTriggerTime;\r\n      \r\n      // 如果超过预期时间很久没有触发，重新同步\r\n      const maxExpectedInterval = this.options.useTimeSlotBoundary ? 35 * 60 * 1000 : 70 * 1000; // 35分钟或70秒\r\n      \r\n      if (timeSinceLastTrigger > maxExpectedInterval) {\r\n        this.log('Recovery check: re-syncing due to missed triggers');\r\n        this.resync();\r\n      }\r\n    }, this.options.recoveryCheckInterval);\r\n  }\r\n\r\n  /**\r\n   * 重新同步时间触发器\r\n   */\r\n  private resync(): void {\r\n    if (!this.isRunning) {\r\n      return;\r\n    }\r\n\r\n    this.log('Re-syncing time updater');\r\n    \r\n    // 清除当前的调度\r\n    if (this.timeoutId !== null) {\r\n      clearTimeout(this.timeoutId);\r\n      this.timeoutId = null;\r\n    }\r\n    \r\n    // 立即执行更新\r\n    this.options.onTimeUpdate();\r\n    \r\n    // 重新调度\r\n    this.scheduleNextUpdate();\r\n  }\r\n\r\n  /**\r\n   * 调试日志输出\r\n   */\r\n  private log(message: string): void {\r\n    if (this.options.debug) {\r\n      console.log(`[SmartTimeUpdater] ${message}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 销毁时间更新器\r\n   */\r\n  destroy(): void {\r\n    this.stop();\r\n    // 移除事件监听器等清理工作在这里进行\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;AAED;;AAuCO,MAAM;IACH,QAA2C;IAC3C,YAA2B,KAAK;IAChC,qBAAoC,KAAK;IACzC,YAAY,MAAM;IAClB,gBAAgB,KAAK;IACrB,MAAoB;IACpB,0BAA0B,EAAE;IAEpC,YAAY,OAAgC,CAAE;QAC5C,IAAI,CAAC,OAAO,GAAG;YACb,OAAO;YACP,qBAAqB;YACrB,uBAAuB,IAAI,KAAK;YAChC,wBAAwB;YACxB,GAAG,OAAO;QACZ;QAEA,IAAI,CAAC,KAAK,GAAG;YACX,eAAe;YACf,kBAAkB;YAClB,kBAAkB;YAClB,cAAc;YACd,iBAAiB;YACjB,iBAAiB;QACnB;QAEA,IAAI,CAAC,uBAAuB;IAC9B;IAEA;;GAEC,GACD,QAAc;QACZ,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,GAAG,CAAC;YACT;QACF;QAEA,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,GAAG,CAAC;QAET,WAAW;QACX,IAAI,CAAC,OAAO,CAAC,YAAY;QAEzB,YAAY;QACZ,IAAI,CAAC,kBAAkB;QAEvB,WAAW;QACX,IAAI,CAAC,kBAAkB;IACzB;IAEA;;GAEC,GACD,OAAa;QACX,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB;QACF;QAEA,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,GAAG,CAAC;QAET,QAAQ;QACR,IAAI,IAAI,CAAC,SAAS,KAAK,MAAM;YAC3B,aAAa,IAAI,CAAC,SAAS;YAC3B,IAAI,CAAC,SAAS,GAAG;QACnB;QAEA,IAAI,IAAI,CAAC,kBAAkB,KAAK,MAAM;YACpC,cAAc,IAAI,CAAC,kBAAkB;YACrC,IAAI,CAAC,kBAAkB,GAAG;QAC5B;IACF;IAEA;;GAEC,GACD,WAAyB;QACvB,OAAO;YAAE,GAAG,IAAI,CAAC,KAAK;QAAC;IACzB;IAEA;;GAEC,GACD,aAAmB;QACjB,IAAI,CAAC,KAAK,GAAG;YACX,eAAe;YACf,kBAAkB;YAClB,kBAAkB;YAClB,cAAc;YACd,iBAAiB;YACjB,iBAAiB;QACnB;IACF;IAEA;;GAEC,GACD,AAAQ,qBAA2B;QACjC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB;QACF;QAEA,cAAc;QACd,MAAM,QAAQ,IAAI,CAAC,OAAO,CAAC,mBAAmB,GAC1C,CAAA,GAAA,yHAAA,CAAA,6BAA0B,AAAD,MACzB,CAAA,GAAA,yHAAA,CAAA,2BAAwB,AAAD;QAE3B,YAAY;QACZ,IAAI,CAAC,uBAAuB,GAAG,CAAA,GAAA,yHAAA,CAAA,4BAAyB,AAAD,MAAM;QAE7D,IAAI,CAAC,GAAG,CAAC,CAAC,0BAA0B,EAAE,MAAM,EAAE,CAAC;QAE/C,uBAAuB;QACvB,IAAI,CAAC,SAAS,GAAG,OAAO,UAAU,CAAC;YACjC,IAAI,CAAC,aAAa;QACpB,GAAG;IACL;IAEA;;GAEC,GACD,AAAQ,gBAAsB;QAC5B,MAAM,oBAAoB,CAAA,GAAA,yHAAA,CAAA,4BAAyB,AAAD;QAElD,SAAS;QACT,IAAI,IAAI,CAAC,uBAAuB,GAAG,GAAG;YACpC,MAAM,YAAY,CAAA,GAAA,yHAAA,CAAA,2BAAwB,AAAD,EACvC,IAAI,CAAC,uBAAuB,EAC5B;YAEF,IAAI,CAAC,WAAW,CAAC;QACnB;QAEA,oBAAoB;QACpB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACvB,IAAI,CAAC,KAAK,CAAC,eAAe;YAC1B,IAAI,CAAC,GAAG,CAAC;YACT,IAAI,CAAC,kBAAkB;YACvB;QACF;QAEA,SAAS;QACT,IAAI,CAAC,OAAO,CAAC,YAAY;QACzB,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG;QAE7B,IAAI,CAAC,GAAG,CAAC;QAET,UAAU;QACV,IAAI,CAAC,kBAAkB;IACzB;IAEA;;GAEC,GACD,AAAQ,YAAY,SAAsD,EAAQ;QAChF,IAAI,CAAC,KAAK,CAAC,aAAa;QAExB,IAAI,UAAU,UAAU,EAAE;YACxB,IAAI,CAAC,KAAK,CAAC,gBAAgB;QAC7B;QAEA,SAAS;QACT,MAAM,iBAAiB,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,CAAC,IAAI,UAAU,SAAS;QACzG,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,iBAAiB,IAAI,CAAC,KAAK,CAAC,aAAa;QAEvE,SAAS;QACT,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,UAAU,SAAS;QAE/E,IAAI,CAAC,GAAG,CAAC,CAAC,mBAAmB,EAAE,UAAU,SAAS,CAAC,OAAO,CAAC,GAAG,YAAY,CAAC;IAC7E;IAEA;;GAEC,GACD,AAAQ,0BAAgC;QACtC,IAAI,OAAO,aAAa,aAAa;YACnC;QACF;QAEA,MAAM,yBAAyB;YAC7B,MAAM,aAAa,IAAI,CAAC,aAAa;YACrC,IAAI,CAAC,aAAa,GAAG,CAAC,SAAS,MAAM;YAErC,IAAI,CAAC,GAAG,CAAC,CAAC,yBAAyB,EAAE,IAAI,CAAC,aAAa,GAAG,YAAY,UAAU;YAEhF,kBAAkB;YAClB,IAAI,CAAC,cAAc,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,OAAO,CAAC,sBAAsB,EAAE;gBAC5E,IAAI,CAAC,GAAG,CAAC;gBACT,IAAI,CAAC,MAAM;YACb;QACF;QAEA,SAAS,gBAAgB,CAAC,oBAAoB;IAChD;IAEA;;GAEC,GACD,AAAQ,qBAA2B;QACjC,IAAI,CAAC,kBAAkB,GAAG,OAAO,WAAW,CAAC;YAC3C,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACnB;YACF;YAEA,aAAa;YACb,MAAM,MAAM,CAAA,GAAA,yHAAA,CAAA,4BAAyB,AAAD;YACpC,MAAM,uBAAuB,MAAM,IAAI,CAAC,KAAK,CAAC,eAAe;YAE7D,sBAAsB;YACtB,MAAM,sBAAsB,IAAI,CAAC,OAAO,CAAC,mBAAmB,GAAG,KAAK,KAAK,OAAO,KAAK,MAAM,WAAW;YAEtG,IAAI,uBAAuB,qBAAqB;gBAC9C,IAAI,CAAC,GAAG,CAAC;gBACT,IAAI,CAAC,MAAM;YACb;QACF,GAAG,IAAI,CAAC,OAAO,CAAC,qBAAqB;IACvC;IAEA;;GAEC,GACD,AAAQ,SAAe;QACrB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB;QACF;QAEA,IAAI,CAAC,GAAG,CAAC;QAET,UAAU;QACV,IAAI,IAAI,CAAC,SAAS,KAAK,MAAM;YAC3B,aAAa,IAAI,CAAC,SAAS;YAC3B,IAAI,CAAC,SAAS,GAAG;QACnB;QAEA,SAAS;QACT,IAAI,CAAC,OAAO,CAAC,YAAY;QAEzB,OAAO;QACP,IAAI,CAAC,kBAAkB;IACzB;IAEA;;GAEC,GACD,AAAQ,IAAI,OAAe,EAAQ;QACjC,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;YACtB,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,SAAS;QAC7C;IACF;IAEA;;GAEC,GACD,UAAgB;QACd,IAAI,CAAC,IAAI;IACT,oBAAoB;IACtB;AACF", "debugId": null}}, {"offset": {"line": 1143, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/utils/dataImport.ts"], "sourcesContent": ["/**\n * 数据导入工具函数\n * 支持JSON格式的数据导入和智能合并\n */\n\nimport type { Activity, TimeBlock, DailyStats } from '@/types';\nimport type { ExportData } from './dataExport';\n\n// 导入策略枚举\nexport enum ImportStrategy {\n  OVERWRITE = 'overwrite',    // 覆盖现有数据\n  MERGE = 'merge',           // 智能合并（默认）\n  ADD_ONLY = 'add_only'      // 仅添加新数据\n}\n\n// 导入结果\nexport interface ImportResult {\n  success: boolean;\n  message: string;\n  statistics: {\n    activitiesAdded: number;\n    activitiesUpdated: number;\n    timeBlocksAdded: number;\n    timeBlocksUpdated: number;\n    daysImported: number;\n  };\n  conflicts?: ImportConflict[];\n}\n\n// 导入冲突\nexport interface ImportConflict {\n  type: 'activity' | 'timeBlock';\n  id: string;\n  field: string;\n  existingValue: any;\n  newValue: any;\n  resolution: 'keep_existing' | 'use_new' | 'merge';\n}\n\n// 数据验证结果\nexport interface ValidationResult {\n  isValid: boolean;\n  errors: string[];\n  warnings: string[];\n  data?: ExportData;\n}\n\n/**\n * 验证导入文件格式\n */\nexport function validateImportData(content: string): ValidationResult {\n  const result: ValidationResult = {\n    isValid: false,\n    errors: [],\n    warnings: []\n  };\n\n  try {\n    const data = JSON.parse(content) as ExportData;\n    \n    // 检查基本结构\n    if (!data.version) {\n      result.errors.push('缺少版本信息');\n    }\n    \n    if (!data.data) {\n      result.errors.push('缺少数据部分');\n      return result;\n    }\n\n    // 检查活动数据\n    if (!Array.isArray(data.data.activities)) {\n      result.errors.push('活动数据格式错误');\n    } else {\n      data.data.activities.forEach((activity, index) => {\n        if (!activity.id || !activity.name || !activity.color) {\n          result.errors.push(`活动 ${index + 1} 缺少必要字段`);\n        }\n      });\n    }\n\n    // 检查时间块数据\n    if (typeof data.data.timeBlocks !== 'object') {\n      result.errors.push('时间块数据格式错误');\n    } else {\n      Object.entries(data.data.timeBlocks).forEach(([date, blocks]) => {\n        if (!Array.isArray(blocks)) {\n          result.errors.push(`日期 ${date} 的时间块数据格式错误`);\n        } else {\n          blocks.forEach((block, index) => {\n            if (!block.id || !block.date || typeof block.timeSlot !== 'number') {\n              result.errors.push(`日期 ${date} 的时间块 ${index + 1} 缺少必要字段`);\n            }\n          });\n        }\n      });\n    }\n\n    // 检查统计数据\n    if (data.data.dailyStats && typeof data.data.dailyStats !== 'object') {\n      result.warnings.push('统计数据格式可能有问题，将跳过导入');\n    }\n\n    // 版本兼容性检查\n    if (data.version !== '1.0.0') {\n      result.warnings.push(`数据版本 ${data.version} 可能不完全兼容当前版本`);\n    }\n\n    if (result.errors.length === 0) {\n      result.isValid = true;\n      result.data = data;\n    }\n\n  } catch (error) {\n    result.errors.push('JSON格式错误：' + (error as Error).message);\n  }\n\n  return result;\n}\n\n/**\n * 检测导入冲突\n */\nexport function detectConflicts(\n  importData: ExportData,\n  existingActivities: Activity[],\n  existingTimeBlocks: Record<string, TimeBlock[]>\n): ImportConflict[] {\n  const conflicts: ImportConflict[] = [];\n\n  // 检测活动冲突\n  const existingActivityMap = new Map(existingActivities.map(a => [a.id, a]));\n  \n  importData.data.activities.forEach(importActivity => {\n    const existing = existingActivityMap.get(importActivity.id);\n    if (existing) {\n      // 检查名称冲突\n      if (existing.name !== importActivity.name) {\n        conflicts.push({\n          type: 'activity',\n          id: importActivity.id,\n          field: 'name',\n          existingValue: existing.name,\n          newValue: importActivity.name,\n          resolution: 'keep_existing'\n        });\n      }\n      \n      // 检查颜色冲突\n      if (existing.color !== importActivity.color) {\n        conflicts.push({\n          type: 'activity',\n          id: importActivity.id,\n          field: 'color',\n          existingValue: existing.color,\n          newValue: importActivity.color,\n          resolution: 'keep_existing'\n        });\n      }\n    }\n  });\n\n  // 检测时间块冲突\n  Object.entries(importData.data.timeBlocks).forEach(([date, importBlocks]) => {\n    const existingBlocks = existingTimeBlocks[date] || [];\n    const existingBlockMap = new Map(existingBlocks.map(b => [b.timeSlot, b]));\n\n    importBlocks.forEach(importBlock => {\n      const existing = existingBlockMap.get(importBlock.timeSlot);\n      if (existing && existing.activityId !== importBlock.activityId) {\n        conflicts.push({\n          type: 'timeBlock',\n          id: `${date}-${importBlock.timeSlot}`,\n          field: 'activityId',\n          existingValue: existing.activityId,\n          newValue: importBlock.activityId,\n          resolution: 'keep_existing'\n        });\n      }\n    });\n  });\n\n  return conflicts;\n}\n\n/**\n * 执行数据导入\n */\nexport function executeImport(\n  importData: ExportData,\n  existingActivities: Activity[],\n  existingTimeBlocks: Record<string, TimeBlock[]>,\n  existingDailyStats: Record<string, DailyStats>,\n  strategy: ImportStrategy = ImportStrategy.MERGE\n): ImportResult {\n  const result: ImportResult = {\n    success: false,\n    message: '',\n    statistics: {\n      activitiesAdded: 0,\n      activitiesUpdated: 0,\n      timeBlocksAdded: 0,\n      timeBlocksUpdated: 0,\n      daysImported: 0\n    }\n  };\n\n  try {\n    let newActivities = [...existingActivities];\n    let newTimeBlocks = { ...existingTimeBlocks };\n    let newDailyStats = { ...existingDailyStats };\n\n    // 处理活动数据\n    const existingActivityMap = new Map(existingActivities.map(a => [a.id, a]));\n    \n    importData.data.activities.forEach(importActivity => {\n      const existing = existingActivityMap.get(importActivity.id);\n      \n      if (existing) {\n        if (strategy === ImportStrategy.OVERWRITE || strategy === ImportStrategy.MERGE) {\n          // 更新现有活动\n          const index = newActivities.findIndex(a => a.id === importActivity.id);\n          if (index !== -1) {\n            newActivities[index] = {\n              ...existing,\n              ...importActivity,\n              updatedAt: new Date().toISOString()\n            };\n            result.statistics.activitiesUpdated++;\n          }\n        }\n      } else {\n        // 添加新活动\n        newActivities.push(importActivity);\n        result.statistics.activitiesAdded++;\n      }\n    });\n\n    // 处理时间块数据\n    Object.entries(importData.data.timeBlocks).forEach(([date, importBlocks]) => {\n      const existingBlocks = newTimeBlocks[date] || [];\n      const existingBlockMap = new Map(existingBlocks.map(b => [b.timeSlot, b]));\n\n      if (strategy === ImportStrategy.OVERWRITE) {\n        // 覆盖整天的数据\n        newTimeBlocks[date] = importBlocks;\n        result.statistics.timeBlocksAdded += importBlocks.length;\n      } else {\n        // 合并或仅添加\n        const mergedBlocks = [...existingBlocks];\n        \n        importBlocks.forEach(importBlock => {\n          const existingIndex = mergedBlocks.findIndex(b => b.timeSlot === importBlock.timeSlot);\n          \n          if (existingIndex !== -1) {\n            if (strategy === ImportStrategy.MERGE) {\n              // 智能合并：如果现有块为空，则使用导入的数据\n              if (!mergedBlocks[existingIndex].activityId && importBlock.activityId) {\n                mergedBlocks[existingIndex] = importBlock;\n                result.statistics.timeBlocksUpdated++;\n              }\n            }\n          } else {\n            // 添加新的时间块\n            mergedBlocks.push(importBlock);\n            result.statistics.timeBlocksAdded++;\n          }\n        });\n\n        newTimeBlocks[date] = mergedBlocks.sort((a, b) => a.timeSlot - b.timeSlot);\n      }\n\n      result.statistics.daysImported++;\n    });\n\n    // 处理统计数据（如果存在）\n    if (importData.data.dailyStats) {\n      Object.entries(importData.data.dailyStats).forEach(([date, stats]) => {\n        if (strategy === ImportStrategy.OVERWRITE || !newDailyStats[date]) {\n          newDailyStats[date] = stats;\n        }\n      });\n    }\n\n    result.success = true;\n    result.message = '数据导入成功';\n\n    // 返回处理后的数据（通过闭包或其他方式传递给调用者）\n    (result as any).data = {\n      activities: newActivities,\n      timeBlocks: newTimeBlocks,\n      dailyStats: newDailyStats\n    };\n\n  } catch (error) {\n    result.success = false;\n    result.message = '导入过程中发生错误：' + (error as Error).message;\n  }\n\n  return result;\n}\n\n/**\n * 读取文件内容\n */\nexport function readFileContent(file: File): Promise<string> {\n  return new Promise((resolve, reject) => {\n    const reader = new FileReader();\n    \n    reader.onload = (event) => {\n      const content = event.target?.result as string;\n      resolve(content);\n    };\n    \n    reader.onerror = () => {\n      reject(new Error('文件读取失败'));\n    };\n    \n    reader.readAsText(file, 'utf-8');\n  });\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AAMM,IAAA,AAAK,wCAAA;;;6CAGiB,SAAS;WAH1B;;AAyCL,SAAS,mBAAmB,OAAe;IAChD,MAAM,SAA2B;QAC/B,SAAS;QACT,QAAQ,EAAE;QACV,UAAU,EAAE;IACd;IAEA,IAAI;QACF,MAAM,OAAO,KAAK,KAAK,CAAC;QAExB,SAAS;QACT,IAAI,CAAC,KAAK,OAAO,EAAE;YACjB,OAAO,MAAM,CAAC,IAAI,CAAC;QACrB;QAEA,IAAI,CAAC,KAAK,IAAI,EAAE;YACd,OAAO,MAAM,CAAC,IAAI,CAAC;YACnB,OAAO;QACT;QAEA,SAAS;QACT,IAAI,CAAC,MAAM,OAAO,CAAC,KAAK,IAAI,CAAC,UAAU,GAAG;YACxC,OAAO,MAAM,CAAC,IAAI,CAAC;QACrB,OAAO;YACL,KAAK,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,UAAU;gBACtC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,EAAE;oBACrD,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,QAAQ,EAAE,OAAO,CAAC;gBAC7C;YACF;QACF;QAEA,UAAU;QACV,IAAI,OAAO,KAAK,IAAI,CAAC,UAAU,KAAK,UAAU;YAC5C,OAAO,MAAM,CAAC,IAAI,CAAC;QACrB,OAAO;YACL,OAAO,OAAO,CAAC,KAAK,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC,MAAM,OAAO;gBAC1D,IAAI,CAAC,MAAM,OAAO,CAAC,SAAS;oBAC1B,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,KAAK,WAAW,CAAC;gBAC5C,OAAO;oBACL,OAAO,OAAO,CAAC,CAAC,OAAO;wBACrB,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,IAAI,IAAI,OAAO,MAAM,QAAQ,KAAK,UAAU;4BAClE,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,KAAK,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC;wBAC1D;oBACF;gBACF;YACF;QACF;QAEA,SAAS;QACT,IAAI,KAAK,IAAI,CAAC,UAAU,IAAI,OAAO,KAAK,IAAI,CAAC,UAAU,KAAK,UAAU;YACpE,OAAO,QAAQ,CAAC,IAAI,CAAC;QACvB;QAEA,UAAU;QACV,IAAI,KAAK,OAAO,KAAK,SAAS;YAC5B,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,OAAO,CAAC,YAAY,CAAC;QACzD;QAEA,IAAI,OAAO,MAAM,CAAC,MAAM,KAAK,GAAG;YAC9B,OAAO,OAAO,GAAG;YACjB,OAAO,IAAI,GAAG;QAChB;IAEF,EAAE,OAAO,OAAO;QACd,OAAO,MAAM,CAAC,IAAI,CAAC,cAAc,AAAC,MAAgB,OAAO;IAC3D;IAEA,OAAO;AACT;AAKO,SAAS,gBACd,UAAsB,EACtB,kBAA8B,EAC9B,kBAA+C;IAE/C,MAAM,YAA8B,EAAE;IAEtC,SAAS;IACT,MAAM,sBAAsB,IAAI,IAAI,mBAAmB,GAAG,CAAC,CAAA,IAAK;YAAC,EAAE,EAAE;YAAE;SAAE;IAEzE,WAAW,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAA;QACjC,MAAM,WAAW,oBAAoB,GAAG,CAAC,eAAe,EAAE;QAC1D,IAAI,UAAU;YACZ,SAAS;YACT,IAAI,SAAS,IAAI,KAAK,eAAe,IAAI,EAAE;gBACzC,UAAU,IAAI,CAAC;oBACb,MAAM;oBACN,IAAI,eAAe,EAAE;oBACrB,OAAO;oBACP,eAAe,SAAS,IAAI;oBAC5B,UAAU,eAAe,IAAI;oBAC7B,YAAY;gBACd;YACF;YAEA,SAAS;YACT,IAAI,SAAS,KAAK,KAAK,eAAe,KAAK,EAAE;gBAC3C,UAAU,IAAI,CAAC;oBACb,MAAM;oBACN,IAAI,eAAe,EAAE;oBACrB,OAAO;oBACP,eAAe,SAAS,KAAK;oBAC7B,UAAU,eAAe,KAAK;oBAC9B,YAAY;gBACd;YACF;QACF;IACF;IAEA,UAAU;IACV,OAAO,OAAO,CAAC,WAAW,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC,MAAM,aAAa;QACtE,MAAM,iBAAiB,kBAAkB,CAAC,KAAK,IAAI,EAAE;QACrD,MAAM,mBAAmB,IAAI,IAAI,eAAe,GAAG,CAAC,CAAA,IAAK;gBAAC,EAAE,QAAQ;gBAAE;aAAE;QAExE,aAAa,OAAO,CAAC,CAAA;YACnB,MAAM,WAAW,iBAAiB,GAAG,CAAC,YAAY,QAAQ;YAC1D,IAAI,YAAY,SAAS,UAAU,KAAK,YAAY,UAAU,EAAE;gBAC9D,UAAU,IAAI,CAAC;oBACb,MAAM;oBACN,IAAI,GAAG,KAAK,CAAC,EAAE,YAAY,QAAQ,EAAE;oBACrC,OAAO;oBACP,eAAe,SAAS,UAAU;oBAClC,UAAU,YAAY,UAAU;oBAChC,YAAY;gBACd;YACF;QACF;IACF;IAEA,OAAO;AACT;AAKO,SAAS,cACd,UAAsB,EACtB,kBAA8B,EAC9B,kBAA+C,EAC/C,kBAA8C,EAC9C,kBAA+C;IAE/C,MAAM,SAAuB;QAC3B,SAAS;QACT,SAAS;QACT,YAAY;YACV,iBAAiB;YACjB,mBAAmB;YACnB,iBAAiB;YACjB,mBAAmB;YACnB,cAAc;QAChB;IACF;IAEA,IAAI;QACF,IAAI,gBAAgB;eAAI;SAAmB;QAC3C,IAAI,gBAAgB;YAAE,GAAG,kBAAkB;QAAC;QAC5C,IAAI,gBAAgB;YAAE,GAAG,kBAAkB;QAAC;QAE5C,SAAS;QACT,MAAM,sBAAsB,IAAI,IAAI,mBAAmB,GAAG,CAAC,CAAA,IAAK;gBAAC,EAAE,EAAE;gBAAE;aAAE;QAEzE,WAAW,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAA;YACjC,MAAM,WAAW,oBAAoB,GAAG,CAAC,eAAe,EAAE;YAE1D,IAAI,UAAU;gBACZ,IAAI,4BAAyC,sBAAmC;oBAC9E,SAAS;oBACT,MAAM,QAAQ,cAAc,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,eAAe,EAAE;oBACrE,IAAI,UAAU,CAAC,GAAG;wBAChB,aAAa,CAAC,MAAM,GAAG;4BACrB,GAAG,QAAQ;4BACX,GAAG,cAAc;4BACjB,WAAW,IAAI,OAAO,WAAW;wBACnC;wBACA,OAAO,UAAU,CAAC,iBAAiB;oBACrC;gBACF;YACF,OAAO;gBACL,QAAQ;gBACR,cAAc,IAAI,CAAC;gBACnB,OAAO,UAAU,CAAC,eAAe;YACnC;QACF;QAEA,UAAU;QACV,OAAO,OAAO,CAAC,WAAW,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC,MAAM,aAAa;YACtE,MAAM,iBAAiB,aAAa,CAAC,KAAK,IAAI,EAAE;YAChD,MAAM,mBAAmB,IAAI,IAAI,eAAe,GAAG,CAAC,CAAA,IAAK;oBAAC,EAAE,QAAQ;oBAAE;iBAAE;YAExE,IAAI,0BAAuC;gBACzC,UAAU;gBACV,aAAa,CAAC,KAAK,GAAG;gBACtB,OAAO,UAAU,CAAC,eAAe,IAAI,aAAa,MAAM;YAC1D,OAAO;gBACL,SAAS;gBACT,MAAM,eAAe;uBAAI;iBAAe;gBAExC,aAAa,OAAO,CAAC,CAAA;oBACnB,MAAM,gBAAgB,aAAa,SAAS,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,YAAY,QAAQ;oBAErF,IAAI,kBAAkB,CAAC,GAAG;wBACxB,IAAI,sBAAmC;4BACrC,wBAAwB;4BACxB,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,UAAU,IAAI,YAAY,UAAU,EAAE;gCACrE,YAAY,CAAC,cAAc,GAAG;gCAC9B,OAAO,UAAU,CAAC,iBAAiB;4BACrC;wBACF;oBACF,OAAO;wBACL,UAAU;wBACV,aAAa,IAAI,CAAC;wBAClB,OAAO,UAAU,CAAC,eAAe;oBACnC;gBACF;gBAEA,aAAa,CAAC,KAAK,GAAG,aAAa,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;YAC3E;YAEA,OAAO,UAAU,CAAC,YAAY;QAChC;QAEA,eAAe;QACf,IAAI,WAAW,IAAI,CAAC,UAAU,EAAE;YAC9B,OAAO,OAAO,CAAC,WAAW,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC,MAAM,MAAM;gBAC/D,IAAI,4BAAyC,CAAC,aAAa,CAAC,KAAK,EAAE;oBACjE,aAAa,CAAC,KAAK,GAAG;gBACxB;YACF;QACF;QAEA,OAAO,OAAO,GAAG;QACjB,OAAO,OAAO,GAAG;QAEjB,4BAA4B;QAC3B,OAAe,IAAI,GAAG;YACrB,YAAY;YACZ,YAAY;YACZ,YAAY;QACd;IAEF,EAAE,OAAO,OAAO;QACd,OAAO,OAAO,GAAG;QACjB,OAAO,OAAO,GAAG,eAAe,AAAC,MAAgB,OAAO;IAC1D;IAEA,OAAO;AACT;AAKO,SAAS,gBAAgB,IAAU;IACxC,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,SAAS,IAAI;QAEnB,OAAO,MAAM,GAAG,CAAC;YACf,MAAM,UAAU,MAAM,MAAM,EAAE;YAC9B,QAAQ;QACV;QAEA,OAAO,OAAO,GAAG;YACf,OAAO,IAAI,MAAM;QACnB;QAEA,OAAO,UAAU,CAAC,MAAM;IAC1B;AACF", "debugId": null}}, {"offset": {"line": 1398, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/stores/useAppStore.ts"], "sourcesContent": ["import { create } from 'zustand';\r\nimport { persist, createJSONStorage } from 'zustand/middleware';\r\nimport { Activity, TimeBlock, UIState, DEFAULT_ACTIVITIES, DEFAULT_COLORS, DailyStats } from '@/types';\r\nimport { generateTimeBlocks, getCurrentDate, getAdjacentDate, getCurrentTimeSlot, cleanFutureTimeBlocks } from '@/utils/timeUtils';\r\nimport { calculateDailyStats } from '@/utils/statsUtils';\r\nimport { StatisticalLRUCache, DataPreloader, MemoryMonitor, getAdjacentDates } from '@/utils/cacheUtils';\r\nimport { LoadingManager, LoadingState } from '@/utils/loadingUtils';\r\nimport { SmartTimeUpdater, type SmartTimeUpdaterOptions } from '@/utils/smartTimeUpdater';\r\nimport { ImportStrategy, type ImportResult } from '@/utils/dataImport';\r\n\r\ninterface AppStore {\r\n  // 状态\r\n  currentDate: string;\r\n  activities: Activity[];\r\n  timeBlocks: Record<string, TimeBlock[]>;\r\n  ui: UIState;\r\n  dailyStats: Record<string, DailyStats>; // 按日期缓存的统计数据\r\n  loadingStates: Record<string, LoadingState>; // 加载状态管理\r\n  currentTimeSlot: number; // 当前时间槽，用于实时更新可编辑状态\r\n  currentTime: { date: string; hour: number; minute: number; second: number }; // 当前时间信息，用于实时显示\r\n\r\n  // 数据导入导出状态\r\n  dataManagement: {\r\n    isExporting: boolean;\r\n    isImporting: boolean;\r\n    showDropdown: boolean;\r\n    importProgress: number;\r\n    exportProgress: number;\r\n    lastImportResult?: ImportResult;\r\n  };\r\n\r\n  // 视图预加载状态\r\n  viewPreloader: {\r\n    preloadingViews: Set<string>; // 正在预加载的视图\r\n    preloadedViews: Set<string>; // 已预加载完成的视图\r\n    lastPreloadTime: Record<string, number>; // 最后预加载时间\r\n  };\r\n\r\n  // 日期相关操作\r\n  setCurrentDate: (date: string) => void;\r\n  goToPreviousDay: () => void;\r\n  goToNextDay: () => void;\r\n  goToToday: () => void;\r\n\r\n  // 活动相关操作\r\n  addActivity: (activity: Omit<Activity, 'id' | 'createdAt' | 'updatedAt'>) => void;\r\n  updateActivity: (id: string, updates: Partial<Activity>) => void;\r\n  deleteActivity: (id: string) => void;\r\n  getActivityById: (id: string) => Activity | undefined;\r\n\r\n  // 时间块相关操作\r\n  getTimeBlocksForDate: (date: string) => TimeBlock[];\r\n  updateTimeBlock: (date: string, timeSlot: number, activityId: string | null) => void;\r\n  updateMultipleTimeBlocks: (date: string, timeSlots: number[], activityId: string | null) => void;\r\n  clearTimeBlock: (date: string, timeSlot: number) => void;\r\n  clearMultipleTimeBlocks: (date: string, timeSlots: number[]) => void;\r\n\r\n  // 统计相关操作\r\n  getDailyStats: (date: string) => DailyStats;\r\n  getDailyStatsSync: (date: string) => DailyStats | null; // 纯读取函数，不触发状态更新\r\n  getDailyStatsAsync: (date: string) => Promise<DailyStats>; // 异步获取统计数据\r\n  invalidateStats: (date: string) => void; // 使指定日期的统计缓存失效\r\n  invalidateAllStats: () => void; // 使所有统计缓存失效\r\n  preloadAdjacentDates: (date: string) => void; // 预加载相邻日期数据\r\n\r\n  // 加载状态管理\r\n  setLoadingState: (key: string, state: LoadingState) => void;\r\n  getLoadingState: (key: string) => LoadingState;\r\n\r\n  // UI状态操作\r\n  setSelectedBlocks: (blocks: number[]) => void;\r\n  addSelectedBlock: (block: number) => void;\r\n  removeSelectedBlock: (block: number) => void;\r\n  clearSelectedBlocks: () => void;\r\n  setShowActivityPalette: (show: boolean) => void;\r\n  setCurrentView: (view: 'grid' | 'review') => void;\r\n  setDragging: (isDragging: boolean, startBlock?: number) => void;\r\n\r\n  // 视图预加载相关操作\r\n  setViewPreloading: (viewKey: string, isPreloading: boolean) => void;\r\n  setViewPreloaded: (viewKey: string, isPreloaded: boolean) => void;\r\n  isViewPreloaded: (viewKey: string) => boolean;\r\n  isViewPreloading: (viewKey: string) => boolean;\r\n\r\n  // 时间相关操作\r\n  updateCurrentTime: () => void;\r\n  startTimeUpdater: () => void;\r\n  stopTimeUpdater: () => void;\r\n  getTimeUpdaterStats: () => any; // 获取时间更新器统计信息\r\n\r\n  // 初始化操作\r\n  initializeApp: () => void;\r\n\r\n  // 数据导入导出操作\r\n  setDataManagementDropdown: (show: boolean) => void;\r\n  setExportProgress: (progress: number) => void;\r\n  setImportProgress: (progress: number) => void;\r\n  setIsExporting: (isExporting: boolean) => void;\r\n  setIsImporting: (isImporting: boolean) => void;\r\n  setLastImportResult: (result: ImportResult) => void;\r\n  exportData: (format: 'json' | 'csv', dateRange?: { start: string; end: string }) => Promise<void>;\r\n  importData: (file: File, strategy?: ImportStrategy) => Promise<ImportResult>;\r\n}\r\n\r\n// 生成唯一ID\r\nfunction generateId(): string {\r\n  return Date.now().toString(36) + Math.random().toString(36).substr(2);\r\n}\r\n\r\n// 创建默认活动\r\nfunction createDefaultActivities(): Activity[] {\r\n  const now = new Date().toISOString();\r\n  return DEFAULT_ACTIVITIES.map(activity => ({\r\n    ...activity,\r\n    id: generateId(),\r\n    createdAt: now,\r\n    updatedAt: now\r\n  }));\r\n}\r\n\r\n// 创建全局缓存和管理器实例\r\nconst statsCache = new StatisticalLRUCache<string, DailyStats>(100);\r\nconst dataPreloader = DataPreloader.getInstance();\r\nconst memoryMonitor = MemoryMonitor.getInstance();\r\nconst loadingManager = LoadingManager.getInstance();\r\n\r\n// 智能时间更新器实例（延迟初始化）\r\nlet smartTimeUpdater: SmartTimeUpdater | null = null;\r\n\r\nexport const useAppStore = create<AppStore>()(\r\n  persist(\r\n    (set, get) => ({\r\n      // 初始状态\r\n      currentDate: getCurrentDate(),\r\n      activities: [],\r\n      timeBlocks: {},\r\n      dailyStats: {},\r\n      loadingStates: {},\r\n      currentTimeSlot: typeof window !== 'undefined' ? getCurrentTimeSlot() : 0,\r\n      currentTime: (() => {\r\n        // 在SSR期间使用安全的默认值，避免hydration mismatch\r\n        if (typeof window === 'undefined') {\r\n          return {\r\n            date: '1970-01-01', // 安全的默认日期\r\n            hour: 0,\r\n            minute: 0,\r\n            second: 0\r\n          };\r\n        }\r\n        const now = new Date();\r\n        return {\r\n          date: now.toISOString().split('T')[0],\r\n          hour: now.getHours(),\r\n          minute: now.getMinutes(),\r\n          second: now.getSeconds()\r\n        };\r\n      })(),\r\n      ui: {\r\n        selectedBlocks: [],\r\n        showActivityPalette: false,\r\n        currentView: 'grid',\r\n        isDragging: false,\r\n        dragStartBlock: null\r\n      },\r\n      dataManagement: {\r\n        isExporting: false,\r\n        isImporting: false,\r\n        showDropdown: false,\r\n        importProgress: 0,\r\n        exportProgress: 0,\r\n        lastImportResult: undefined\r\n      },\r\n      viewPreloader: {\r\n        preloadingViews: new Set(),\r\n        preloadedViews: new Set(),\r\n        lastPreloadTime: {}\r\n      },\r\n\r\n      // 日期相关操作\r\n      setCurrentDate: (date: string) => {\r\n        set({ currentDate: date });\r\n\r\n        // 立即更新当前时间槽（用于正确计算可编辑状态）\r\n        get().updateCurrentTime();\r\n\r\n        // 确保该日期的时间块存在\r\n        const state = get();\r\n        if (!state.timeBlocks[date]) {\r\n          set(state => ({\r\n            timeBlocks: {\r\n              ...state.timeBlocks,\r\n              [date]: generateTimeBlocks(date)\r\n            }\r\n          }));\r\n        }\r\n\r\n        // 预加载相邻日期数据\r\n        get().preloadAdjacentDates(date);\r\n      },\r\n\r\n      goToPreviousDay: () => {\r\n        const currentDate = get().currentDate;\r\n        const prevDate = new Date(currentDate);\r\n        prevDate.setDate(prevDate.getDate() - 1);\r\n        get().setCurrentDate(prevDate.toISOString().split('T')[0]);\r\n      },\r\n\r\n      goToNextDay: () => {\r\n        const currentDate = get().currentDate;\r\n        const nextDate = new Date(currentDate);\r\n        nextDate.setDate(nextDate.getDate() + 1);\r\n        get().setCurrentDate(nextDate.toISOString().split('T')[0]);\r\n      },\r\n\r\n      goToToday: () => {\r\n        get().setCurrentDate(getCurrentDate());\r\n      },\r\n\r\n      // 活动相关操作\r\n      addActivity: (activityData) => {\r\n        const now = new Date().toISOString();\r\n        const newActivity: Activity = {\r\n          ...activityData,\r\n          id: generateId(),\r\n          createdAt: now,\r\n          updatedAt: now\r\n        };\r\n\r\n        set(state => ({\r\n          activities: [...state.activities, newActivity]\r\n        }));\r\n      },\r\n\r\n      updateActivity: (id: string, updates: Partial<Activity>) => {\r\n        set(state => ({\r\n          activities: state.activities.map(activity =>\r\n            activity.id === id\r\n              ? { ...activity, ...updates, updatedAt: new Date().toISOString() }\r\n              : activity\r\n          )\r\n        }));\r\n      },\r\n\r\n      deleteActivity: (id: string) => {\r\n        set(state => ({\r\n          activities: state.activities.filter(activity => activity.id !== id),\r\n          // 清除所有使用该活动的时间块\r\n          timeBlocks: Object.fromEntries(\r\n            Object.entries(state.timeBlocks).map(([date, blocks]) => [\r\n              date,\r\n              blocks.map(block =>\r\n                block.activityId === id ? { ...block, activityId: null } : block\r\n              )\r\n            ])\r\n          )\r\n        }));\r\n      },\r\n\r\n      getActivityById: (id: string) => {\r\n        return get().activities.find(activity => activity.id === id);\r\n      },\r\n\r\n      // 时间块相关操作\r\n      getTimeBlocksForDate: (date: string) => {\r\n        const state = get();\r\n        if (!state.timeBlocks[date]) {\r\n          const blocks = generateTimeBlocks(date);\r\n          set(state => ({\r\n            timeBlocks: {\r\n              ...state.timeBlocks,\r\n              [date]: blocks\r\n            }\r\n          }));\r\n          return blocks;\r\n        }\r\n\r\n        // 返回时清理未来时间的数据\r\n        const cleanedBlocks = cleanFutureTimeBlocks(state.timeBlocks[date], date);\r\n\r\n        // 如果数据被清理了，更新存储\r\n        if (JSON.stringify(cleanedBlocks) !== JSON.stringify(state.timeBlocks[date])) {\r\n          set(state => ({\r\n            timeBlocks: {\r\n              ...state.timeBlocks,\r\n              [date]: cleanedBlocks\r\n            }\r\n          }));\r\n        }\r\n\r\n        return cleanedBlocks;\r\n      },\r\n\r\n      updateTimeBlock: (date: string, timeSlot: number, activityId: string | null) => {\r\n        set(state => ({\r\n          timeBlocks: {\r\n            ...state.timeBlocks,\r\n            [date]: state.timeBlocks[date]?.map(block =>\r\n              block.timeSlot === timeSlot\r\n                ? { ...block, activityId }\r\n                : block\r\n            ) || generateTimeBlocks(date).map(block =>\r\n              block.timeSlot === timeSlot\r\n                ? { ...block, activityId }\r\n                : block\r\n            )\r\n          },\r\n          // 使该日期的统计缓存失效\r\n          dailyStats: (() => {\r\n            const newDailyStats = { ...state.dailyStats };\r\n            delete newDailyStats[date];\r\n            return newDailyStats;\r\n          })()\r\n        }));\r\n      },\r\n\r\n      updateMultipleTimeBlocks: (date: string, timeSlots: number[], activityId: string | null) => {\r\n        set(state => ({\r\n          timeBlocks: {\r\n            ...state.timeBlocks,\r\n            [date]: state.timeBlocks[date]?.map(block =>\r\n              timeSlots.includes(block.timeSlot)\r\n                ? { ...block, activityId }\r\n                : block\r\n            ) || generateTimeBlocks(date).map(block =>\r\n              timeSlots.includes(block.timeSlot)\r\n                ? { ...block, activityId }\r\n                : block\r\n            )\r\n          },\r\n          // 使该日期的统计缓存失效\r\n          dailyStats: (() => {\r\n            const newDailyStats = { ...state.dailyStats };\r\n            delete newDailyStats[date];\r\n            return newDailyStats;\r\n          })()\r\n        }));\r\n      },\r\n\r\n      clearTimeBlock: (date: string, timeSlot: number) => {\r\n        get().updateTimeBlock(date, timeSlot, null);\r\n      },\r\n\r\n      clearMultipleTimeBlocks: (date: string, timeSlots: number[]) => {\r\n        get().updateMultipleTimeBlocks(date, timeSlots, null);\r\n      },\r\n\r\n      // 统计相关操作\r\n      getDailyStats: (date: string) => {\r\n        const state = get();\r\n\r\n        // 先检查LRU缓存\r\n        const cachedStats = statsCache.get(date);\r\n        if (cachedStats) {\r\n          return cachedStats;\r\n        }\r\n\r\n        // 检查状态缓存\r\n        if (state.dailyStats[date]) {\r\n          statsCache.set(date, state.dailyStats[date]);\r\n          return state.dailyStats[date];\r\n        }\r\n\r\n        // 计算统计数据\r\n        const timeBlocks = state.timeBlocks[date] || generateTimeBlocks(date);\r\n        const stats = calculateDailyStats(timeBlocks, state.activities);\r\n\r\n        // 缓存到LRU缓存\r\n        statsCache.set(date, stats);\r\n\r\n        // 异步更新状态缓存，避免在渲染过程中同步更新状态\r\n        setTimeout(() => {\r\n          set(state => ({\r\n            dailyStats: {\r\n              ...state.dailyStats,\r\n              [date]: stats\r\n            }\r\n          }));\r\n        }, 0);\r\n\r\n        return stats;\r\n      },\r\n\r\n      // 纯读取函数，不触发任何状态更新\r\n      getDailyStatsSync: (date: string) => {\r\n        const state = get();\r\n\r\n        // 先检查LRU缓存\r\n        const cachedStats = statsCache.get(date);\r\n        if (cachedStats) {\r\n          return cachedStats;\r\n        }\r\n\r\n        // 检查状态缓存\r\n        if (state.dailyStats[date]) {\r\n          statsCache.set(date, state.dailyStats[date]);\r\n          return state.dailyStats[date];\r\n        }\r\n\r\n        // 如果没有缓存，返回null而不是计算\r\n        return null;\r\n      },\r\n\r\n      getDailyStatsAsync: async (date: string) => {\r\n        const loadingKey = `stats-${date}`;\r\n\r\n        try {\r\n          // 延迟状态更新到下一个事件循环，避免在渲染过程中更新状态\r\n          setTimeout(() => {\r\n            get().setLoadingState(loadingKey, 'loading');\r\n          }, 0);\r\n\r\n          // 模拟异步计算（对于复杂统计）\r\n          await new Promise(resolve => setTimeout(resolve, 50));\r\n\r\n          const stats = get().getDailyStats(date);\r\n\r\n          // 延迟成功状态更新\r\n          setTimeout(() => {\r\n            get().setLoadingState(loadingKey, 'success');\r\n          }, 0);\r\n\r\n          return stats;\r\n        } catch (error) {\r\n          // 延迟错误状态更新\r\n          setTimeout(() => {\r\n            get().setLoadingState(loadingKey, 'error');\r\n          }, 0);\r\n          throw error;\r\n        }\r\n      },\r\n\r\n      invalidateStats: (date: string) => {\r\n        // 从两个缓存中删除\r\n        statsCache.delete(date);\r\n        set(state => {\r\n          const newDailyStats = { ...state.dailyStats };\r\n          delete newDailyStats[date];\r\n          return { dailyStats: newDailyStats };\r\n        });\r\n      },\r\n\r\n      invalidateAllStats: () => {\r\n        // 清空所有缓存\r\n        statsCache.clear();\r\n        set({ dailyStats: {} });\r\n      },\r\n\r\n      preloadAdjacentDates: (date: string) => {\r\n        // 检查内存使用情况\r\n        if (memoryMonitor.checkMemoryUsage()) {\r\n          console.warn('内存使用过高，跳过预加载');\r\n          return;\r\n        }\r\n\r\n        const adjacentDates = getAdjacentDates(date, 2);\r\n        dataPreloader.addMultipleToQueue(adjacentDates);\r\n      },\r\n\r\n      // 加载状态管理\r\n      setLoadingState: (key: string, state: LoadingState) => {\r\n        set(prevState => ({\r\n          loadingStates: {\r\n            ...prevState.loadingStates,\r\n            [key]: state\r\n          }\r\n        }));\r\n        loadingManager.setLoadingState(key, state);\r\n      },\r\n\r\n      getLoadingState: (key: string) => {\r\n        const state = get();\r\n        return state.loadingStates[key] || 'idle';\r\n      },\r\n\r\n      // UI状态操作\r\n      setSelectedBlocks: (blocks: number[]) => {\r\n        set(state => ({\r\n          ui: { ...state.ui, selectedBlocks: blocks }\r\n        }));\r\n      },\r\n\r\n      addSelectedBlock: (block: number) => {\r\n        set(state => ({\r\n          ui: {\r\n            ...state.ui,\r\n            selectedBlocks: [...state.ui.selectedBlocks, block]\r\n          }\r\n        }));\r\n      },\r\n\r\n      removeSelectedBlock: (block: number) => {\r\n        set(state => ({\r\n          ui: {\r\n            ...state.ui,\r\n            selectedBlocks: state.ui.selectedBlocks.filter(b => b !== block)\r\n          }\r\n        }));\r\n      },\r\n\r\n      clearSelectedBlocks: () => {\r\n        set(state => ({\r\n          ui: { ...state.ui, selectedBlocks: [] }\r\n        }));\r\n      },\r\n\r\n      setShowActivityPalette: (show: boolean) => {\r\n        set(state => ({\r\n          ui: { ...state.ui, showActivityPalette: show }\r\n        }));\r\n      },\r\n\r\n      setCurrentView: (view: 'grid' | 'review') => {\r\n        set(state => ({\r\n          ui: { ...state.ui, currentView: view }\r\n        }));\r\n      },\r\n\r\n      // 视图预加载相关方法\r\n      setViewPreloading: (viewKey: string, isPreloading: boolean) => {\r\n        set(state => {\r\n          const newPreloadingViews = new Set(state.viewPreloader.preloadingViews);\r\n          if (isPreloading) {\r\n            newPreloadingViews.add(viewKey);\r\n          } else {\r\n            newPreloadingViews.delete(viewKey);\r\n          }\r\n          return {\r\n            viewPreloader: {\r\n              ...state.viewPreloader,\r\n              preloadingViews: newPreloadingViews\r\n            }\r\n          };\r\n        });\r\n      },\r\n\r\n      setViewPreloaded: (viewKey: string, isPreloaded: boolean) => {\r\n        set(state => {\r\n          const newPreloadedViews = new Set(state.viewPreloader.preloadedViews);\r\n          const newLastPreloadTime = { ...state.viewPreloader.lastPreloadTime };\r\n\r\n          if (isPreloaded) {\r\n            newPreloadedViews.add(viewKey);\r\n            newLastPreloadTime[viewKey] = Date.now();\r\n          } else {\r\n            newPreloadedViews.delete(viewKey);\r\n            delete newLastPreloadTime[viewKey];\r\n          }\r\n\r\n          return {\r\n            viewPreloader: {\r\n              ...state.viewPreloader,\r\n              preloadedViews: newPreloadedViews,\r\n              lastPreloadTime: newLastPreloadTime\r\n            }\r\n          };\r\n        });\r\n      },\r\n\r\n      isViewPreloaded: (viewKey: string) => {\r\n        const state = get();\r\n        return state.viewPreloader.preloadedViews.has(viewKey);\r\n      },\r\n\r\n      isViewPreloading: (viewKey: string) => {\r\n        const state = get();\r\n        return state.viewPreloader.preloadingViews.has(viewKey);\r\n      },\r\n\r\n      setDragging: (isDragging: boolean, startBlock?: number) => {\r\n        set(state => ({\r\n          ui: {\r\n            ...state.ui,\r\n            isDragging,\r\n            dragStartBlock: isDragging ? (startBlock ?? null) : null\r\n          }\r\n        }));\r\n      },\r\n\r\n      // 时间相关操作\r\n      updateCurrentTime: () => {\r\n        const now = new Date();\r\n        const newCurrentTime = {\r\n          date: now.toISOString().split('T')[0],\r\n          hour: now.getHours(),\r\n          minute: now.getMinutes(),\r\n          second: now.getSeconds()\r\n        };\r\n        const newTimeSlot = getCurrentTimeSlot();\r\n        const currentState = get();\r\n\r\n        // 总是更新currentTime以确保时间显示实时性\r\n        // 只有当时间槽发生变化时才更新其他状态\r\n        if (newTimeSlot !== currentState.currentTimeSlot) {\r\n          set({\r\n            currentTime: newCurrentTime,\r\n            currentTimeSlot: newTimeSlot\r\n          });\r\n\r\n          // 时间槽变化时，清理当前日期的未来时间数据\r\n          const currentDate = currentState.currentDate;\r\n          if (currentState.timeBlocks[currentDate]) {\r\n            const cleanedBlocks = cleanFutureTimeBlocks(currentState.timeBlocks[currentDate], currentDate);\r\n            if (JSON.stringify(cleanedBlocks) !== JSON.stringify(currentState.timeBlocks[currentDate])) {\r\n              set(state => ({\r\n                timeBlocks: {\r\n                  ...state.timeBlocks,\r\n                  [currentDate]: cleanedBlocks\r\n                }\r\n              }));\r\n            }\r\n          }\r\n        } else {\r\n          // 即使时间槽没变化，也要更新currentTime以保持时间显示的实时性\r\n          set({ currentTime: newCurrentTime });\r\n        }\r\n      },\r\n\r\n      startTimeUpdater: () => {\r\n        // 避免重复启动智能时间更新器\r\n        if (typeof window !== 'undefined' && !smartTimeUpdater) {\r\n          // 创建智能时间更新器实例\r\n          smartTimeUpdater = new SmartTimeUpdater({\r\n            onTimeUpdate: () => {\r\n              console.log('[SmartTimeUpdater] Triggering time update at:', new Date().toLocaleTimeString());\r\n              get().updateCurrentTime();\r\n            },\r\n            debug: true, // 启用调试模式查看触发情况\r\n            useTimeSlotBoundary: false, // 使用分钟边界触发，更精确\r\n            syncOnVisibilityChange: true, // 页面可见性变化时重新同步\r\n            recoveryCheckInterval: 5 * 60 * 1000 // 5分钟错误恢复检查\r\n          });\r\n\r\n          // 启动智能时间更新器\r\n          smartTimeUpdater.start();\r\n\r\n          // 在开发环境中添加全局调试函数\r\n          if (typeof window !== 'undefined') {\r\n            (window as any).getTimeUpdaterStats = () => {\r\n              return smartTimeUpdater ? smartTimeUpdater.getStats() : null;\r\n            };\r\n            console.log('[SmartTimeUpdater] Debug function available: window.getTimeUpdaterStats()');\r\n          }\r\n        }\r\n      },\r\n\r\n      stopTimeUpdater: () => {\r\n        if (smartTimeUpdater) {\r\n          smartTimeUpdater.stop();\r\n          smartTimeUpdater = null;\r\n        }\r\n      },\r\n\r\n      getTimeUpdaterStats: () => {\r\n        return smartTimeUpdater ? smartTimeUpdater.getStats() : null;\r\n      },\r\n\r\n      // 初始化操作\r\n      initializeApp: () => {\r\n        const state = get();\r\n\r\n        // 只在客户端环境中执行时间相关的初始化\r\n        if (typeof window !== 'undefined') {\r\n          // 延迟一帧确保hydration完成，避免hydration mismatch\r\n          requestAnimationFrame(() => {\r\n            // 立即更新正确的时间状态，覆盖SSR期间的默认值\r\n            get().updateCurrentTime();\r\n          });\r\n        }\r\n\r\n        // 如果没有活动，创建默认活动\r\n        if (state.activities.length === 0) {\r\n          set({ activities: createDefaultActivities() });\r\n        }\r\n\r\n        // 确保当前日期的时间块存在\r\n        const currentDate = state.currentDate;\r\n        if (!state.timeBlocks[currentDate]) {\r\n          set(state => ({\r\n            timeBlocks: {\r\n              ...state.timeBlocks,\r\n              [currentDate]: generateTimeBlocks(currentDate)\r\n            }\r\n          }));\r\n        }\r\n\r\n        // 设置预加载监听器\r\n        if (typeof window !== 'undefined') {\r\n          const handlePreload = (event: CustomEvent) => {\r\n            const { dates } = event.detail;\r\n            dates.forEach((date: string) => {\r\n              // 预加载时间块\r\n              if (!get().timeBlocks[date]) {\r\n                set(state => ({\r\n                  timeBlocks: {\r\n                    ...state.timeBlocks,\r\n                    [date]: generateTimeBlocks(date)\r\n                  }\r\n                }));\r\n              }\r\n\r\n              // 预计算统计数据\r\n              get().getDailyStats(date);\r\n            });\r\n          };\r\n\r\n          window.addEventListener('preloadDates', handlePreload as EventListener);\r\n        }\r\n\r\n        // 预加载当前日期的相邻日期\r\n        get().preloadAdjacentDates(currentDate);\r\n\r\n        // 启动时间更新器\r\n        get().startTimeUpdater();\r\n\r\n        // 添加页面卸载时的清理逻辑\r\n        if (typeof window !== 'undefined') {\r\n          const handleBeforeUnload = () => {\r\n            get().stopTimeUpdater();\r\n          };\r\n\r\n          window.addEventListener('beforeunload', handleBeforeUnload);\r\n        }\r\n      },\r\n\r\n      // 数据管理操作\r\n      setDataManagementDropdown: (show: boolean) => {\r\n        set(state => ({\r\n          dataManagement: {\r\n            ...state.dataManagement,\r\n            showDropdown: show\r\n          }\r\n        }));\r\n      },\r\n\r\n      setExportProgress: (progress: number) => {\r\n        set(state => ({\r\n          dataManagement: {\r\n            ...state.dataManagement,\r\n            exportProgress: progress\r\n          }\r\n        }));\r\n      },\r\n\r\n      setImportProgress: (progress: number) => {\r\n        set(state => ({\r\n          dataManagement: {\r\n            ...state.dataManagement,\r\n            importProgress: progress\r\n          }\r\n        }));\r\n      },\r\n\r\n      setIsExporting: (isExporting: boolean) => {\r\n        set(state => ({\r\n          dataManagement: {\r\n            ...state.dataManagement,\r\n            isExporting\r\n          }\r\n        }));\r\n      },\r\n\r\n      setIsImporting: (isImporting: boolean) => {\r\n        set(state => ({\r\n          dataManagement: {\r\n            ...state.dataManagement,\r\n            isImporting\r\n          }\r\n        }));\r\n      },\r\n\r\n      setLastImportResult: (result: ImportResult) => {\r\n        set(state => ({\r\n          dataManagement: {\r\n            ...state.dataManagement,\r\n            lastImportResult: result\r\n          }\r\n        }));\r\n      },\r\n\r\n      // 导出数据\r\n      exportData: async (format: 'json' | 'csv', dateRange?: { start: string; end: string }) => {\r\n        const state = get();\r\n\r\n        try {\r\n          state.setIsExporting(true);\r\n          state.setExportProgress(0);\r\n\r\n          // 动态导入导出工具函数\r\n          const { exportJSONFile, exportCSVFile } = await import('@/utils/dataExport');\r\n\r\n          state.setExportProgress(50);\r\n\r\n          if (format === 'json') {\r\n            exportJSONFile(\r\n              state.activities,\r\n              state.timeBlocks,\r\n              state.dailyStats,\r\n              dateRange\r\n            );\r\n          } else {\r\n            exportCSVFile(\r\n              state.activities,\r\n              state.timeBlocks,\r\n              dateRange\r\n            );\r\n          }\r\n\r\n          state.setExportProgress(100);\r\n\r\n          // 延迟重置状态，让用户看到完成状态\r\n          setTimeout(() => {\r\n            state.setIsExporting(false);\r\n            state.setExportProgress(0);\r\n          }, 1000);\r\n\r\n        } catch (error) {\r\n          console.error('导出失败:', error);\r\n          state.setIsExporting(false);\r\n          state.setExportProgress(0);\r\n          throw error;\r\n        }\r\n      },\r\n\r\n      // 导入数据\r\n      importData: async (file: File, strategy: ImportStrategy = ImportStrategy.MERGE) => {\r\n        const state = get();\r\n\r\n        try {\r\n          state.setIsImporting(true);\r\n          state.setImportProgress(0);\r\n\r\n          // 动态导入导入工具函数\r\n          const { readFileContent, validateImportData, executeImport } = await import('@/utils/dataImport');\r\n\r\n          state.setImportProgress(20);\r\n\r\n          // 读取文件内容\r\n          const content = await readFileContent(file);\r\n          state.setImportProgress(40);\r\n\r\n          // 验证数据格式\r\n          const validation = validateImportData(content);\r\n          if (!validation.isValid || !validation.data) {\r\n            const result: ImportResult = {\r\n              success: false,\r\n              message: '数据格式验证失败: ' + validation.errors.join(', '),\r\n              statistics: {\r\n                activitiesAdded: 0,\r\n                activitiesUpdated: 0,\r\n                timeBlocksAdded: 0,\r\n                timeBlocksUpdated: 0,\r\n                daysImported: 0\r\n              }\r\n            };\r\n            state.setLastImportResult(result);\r\n            return result;\r\n          }\r\n\r\n          state.setImportProgress(60);\r\n\r\n          // 执行导入\r\n          const importResult = executeImport(\r\n            validation.data,\r\n            state.activities,\r\n            state.timeBlocks,\r\n            state.dailyStats,\r\n            strategy\r\n          );\r\n\r\n          state.setImportProgress(80);\r\n\r\n          if (importResult.success && (importResult as any).data) {\r\n            // 更新状态\r\n            const newData = (importResult as any).data;\r\n            set({\r\n              activities: newData.activities,\r\n              timeBlocks: newData.timeBlocks,\r\n              dailyStats: newData.dailyStats\r\n            });\r\n\r\n            // 清除缓存，强制重新计算统计数据\r\n            statsCache.clear();\r\n            dataPreloader.clearQueue();\r\n          }\r\n\r\n          state.setImportProgress(100);\r\n          state.setLastImportResult(importResult);\r\n\r\n          // 延迟重置状态\r\n          setTimeout(() => {\r\n            state.setIsImporting(false);\r\n            state.setImportProgress(0);\r\n          }, 1000);\r\n\r\n          return importResult;\r\n\r\n        } catch (error) {\r\n          console.error('导入失败:', error);\r\n          const result: ImportResult = {\r\n            success: false,\r\n            message: '导入过程中发生错误: ' + (error as Error).message,\r\n            statistics: {\r\n              activitiesAdded: 0,\r\n              activitiesUpdated: 0,\r\n              timeBlocksAdded: 0,\r\n              timeBlocksUpdated: 0,\r\n              daysImported: 0\r\n            }\r\n          };\r\n\r\n          state.setIsImporting(false);\r\n          state.setImportProgress(0);\r\n          state.setLastImportResult(result);\r\n\r\n          return result;\r\n        }\r\n      }\r\n    }),\r\n    {\r\n      name: 'chronospect-storage',\r\n      // 只持久化核心数据，UI状态和时间状态不持久化\r\n      partialize: (state) => ({\r\n        currentDate: state.currentDate,\r\n        activities: state.activities,\r\n        timeBlocks: state.timeBlocks,\r\n        dailyStats: state.dailyStats,\r\n        // currentTimeSlot 和 currentTime 不持久化，每次启动时重新计算\r\n      }),\r\n      // 使用createJSONStorage确保在浏览器环境中才使用localStorage\r\n      storage: createJSONStorage(() => {\r\n        if (typeof window === 'undefined') {\r\n          // SSR环境下返回一个空的storage\r\n          return {\r\n            getItem: () => null,\r\n            setItem: () => {},\r\n            removeItem: () => {}\r\n          };\r\n        }\r\n        return localStorage;\r\n      })\r\n    }\r\n  )\r\n);\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAgGA,SAAS;AACT,SAAS;IACP,OAAO,KAAK,GAAG,GAAG,QAAQ,CAAC,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC;AACrE;AAEA,SAAS;AACT,SAAS;IACP,MAAM,MAAM,IAAI,OAAO,WAAW;IAClC,OAAO,qHAAA,CAAA,qBAAkB,CAAC,GAAG,CAAC,CAAA,WAAY,CAAC;YACzC,GAAG,QAAQ;YACX,IAAI;YACJ,WAAW;YACX,WAAW;QACb,CAAC;AACH;AAEA,eAAe;AACf,MAAM,aAAa,IAAI,0HAAA,CAAA,sBAAmB,CAAqB;AAC/D,MAAM,gBAAgB,0HAAA,CAAA,gBAAa,CAAC,WAAW;AAC/C,MAAM,gBAAgB,0HAAA,CAAA,gBAAa,CAAC,WAAW;AAC/C,MAAM,iBAAiB,4HAAA,CAAA,iBAAc,CAAC,WAAW;AAEjD,mBAAmB;AACnB,IAAI,mBAA4C;AAEzC,MAAM,cAAc,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC9B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,OAAO;QACP,aAAa,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD;QAC1B,YAAY,EAAE;QACd,YAAY,CAAC;QACb,YAAY,CAAC;QACb,eAAe,CAAC;QAChB,iBAAiB,sCAAgC,0BAAuB;QACxE,aAAa,CAAC;YACZ,sCAAsC;YACtC,wCAAmC;gBACjC,OAAO;oBACL,MAAM;oBACN,MAAM;oBACN,QAAQ;oBACR,QAAQ;gBACV;YACF;;;YACA,MAAM;QAOR,CAAC;QACD,IAAI;YACF,gBAAgB,EAAE;YAClB,qBAAqB;YACrB,aAAa;YACb,YAAY;YACZ,gBAAgB;QAClB;QACA,gBAAgB;YACd,aAAa;YACb,aAAa;YACb,cAAc;YACd,gBAAgB;YAChB,gBAAgB;YAChB,kBAAkB;QACpB;QACA,eAAe;YACb,iBAAiB,IAAI;YACrB,gBAAgB,IAAI;YACpB,iBAAiB,CAAC;QACpB;QAEA,SAAS;QACT,gBAAgB,CAAC;YACf,IAAI;gBAAE,aAAa;YAAK;YAExB,yBAAyB;YACzB,MAAM,iBAAiB;YAEvB,cAAc;YACd,MAAM,QAAQ;YACd,IAAI,CAAC,MAAM,UAAU,CAAC,KAAK,EAAE;gBAC3B,IAAI,CAAA,QAAS,CAAC;wBACZ,YAAY;4BACV,GAAG,MAAM,UAAU;4BACnB,CAAC,KAAK,EAAE,CAAA,GAAA,yHAAA,CAAA,qBAAkB,AAAD,EAAE;wBAC7B;oBACF,CAAC;YACH;YAEA,YAAY;YACZ,MAAM,oBAAoB,CAAC;QAC7B;QAEA,iBAAiB;YACf,MAAM,cAAc,MAAM,WAAW;YACrC,MAAM,WAAW,IAAI,KAAK;YAC1B,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK;YACtC,MAAM,cAAc,CAAC,SAAS,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC3D;QAEA,aAAa;YACX,MAAM,cAAc,MAAM,WAAW;YACrC,MAAM,WAAW,IAAI,KAAK;YAC1B,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK;YACtC,MAAM,cAAc,CAAC,SAAS,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC3D;QAEA,WAAW;YACT,MAAM,cAAc,CAAC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD;QACpC;QAEA,SAAS;QACT,aAAa,CAAC;YACZ,MAAM,MAAM,IAAI,OAAO,WAAW;YAClC,MAAM,cAAwB;gBAC5B,GAAG,YAAY;gBACf,IAAI;gBACJ,WAAW;gBACX,WAAW;YACb;YAEA,IAAI,CAAA,QAAS,CAAC;oBACZ,YAAY;2BAAI,MAAM,UAAU;wBAAE;qBAAY;gBAChD,CAAC;QACH;QAEA,gBAAgB,CAAC,IAAY;YAC3B,IAAI,CAAA,QAAS,CAAC;oBACZ,YAAY,MAAM,UAAU,CAAC,GAAG,CAAC,CAAA,WAC/B,SAAS,EAAE,KAAK,KACZ;4BAAE,GAAG,QAAQ;4BAAE,GAAG,OAAO;4BAAE,WAAW,IAAI,OAAO,WAAW;wBAAG,IAC/D;gBAER,CAAC;QACH;QAEA,gBAAgB,CAAC;YACf,IAAI,CAAA,QAAS,CAAC;oBACZ,YAAY,MAAM,UAAU,CAAC,MAAM,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK;oBAChE,gBAAgB;oBAChB,YAAY,OAAO,WAAW,CAC5B,OAAO,OAAO,CAAC,MAAM,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,OAAO,GAAK;4BACvD;4BACA,OAAO,GAAG,CAAC,CAAA,QACT,MAAM,UAAU,KAAK,KAAK;oCAAE,GAAG,KAAK;oCAAE,YAAY;gCAAK,IAAI;yBAE9D;gBAEL,CAAC;QACH;QAEA,iBAAiB,CAAC;YAChB,OAAO,MAAM,UAAU,CAAC,IAAI,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK;QAC3D;QAEA,UAAU;QACV,sBAAsB,CAAC;YACrB,MAAM,QAAQ;YACd,IAAI,CAAC,MAAM,UAAU,CAAC,KAAK,EAAE;gBAC3B,MAAM,SAAS,CAAA,GAAA,yHAAA,CAAA,qBAAkB,AAAD,EAAE;gBAClC,IAAI,CAAA,QAAS,CAAC;wBACZ,YAAY;4BACV,GAAG,MAAM,UAAU;4BACnB,CAAC,KAAK,EAAE;wBACV;oBACF,CAAC;gBACD,OAAO;YACT;YAEA,eAAe;YACf,MAAM,gBAAgB,CAAA,GAAA,yHAAA,CAAA,wBAAqB,AAAD,EAAE,MAAM,UAAU,CAAC,KAAK,EAAE;YAEpE,gBAAgB;YAChB,IAAI,KAAK,SAAS,CAAC,mBAAmB,KAAK,SAAS,CAAC,MAAM,UAAU,CAAC,KAAK,GAAG;gBAC5E,IAAI,CAAA,QAAS,CAAC;wBACZ,YAAY;4BACV,GAAG,MAAM,UAAU;4BACnB,CAAC,KAAK,EAAE;wBACV;oBACF,CAAC;YACH;YAEA,OAAO;QACT;QAEA,iBAAiB,CAAC,MAAc,UAAkB;YAChD,IAAI,CAAA,QAAS,CAAC;oBACZ,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,KAAK,EAAE,MAAM,UAAU,CAAC,KAAK,EAAE,IAAI,CAAA,QAClC,MAAM,QAAQ,KAAK,WACf;gCAAE,GAAG,KAAK;gCAAE;4BAAW,IACvB,UACD,CAAA,GAAA,yHAAA,CAAA,qBAAkB,AAAD,EAAE,MAAM,GAAG,CAAC,CAAA,QAChC,MAAM,QAAQ,KAAK,WACf;gCAAE,GAAG,KAAK;gCAAE;4BAAW,IACvB;oBAER;oBACA,cAAc;oBACd,YAAY,CAAC;wBACX,MAAM,gBAAgB;4BAAE,GAAG,MAAM,UAAU;wBAAC;wBAC5C,OAAO,aAAa,CAAC,KAAK;wBAC1B,OAAO;oBACT,CAAC;gBACH,CAAC;QACH;QAEA,0BAA0B,CAAC,MAAc,WAAqB;YAC5D,IAAI,CAAA,QAAS,CAAC;oBACZ,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,KAAK,EAAE,MAAM,UAAU,CAAC,KAAK,EAAE,IAAI,CAAA,QAClC,UAAU,QAAQ,CAAC,MAAM,QAAQ,IAC7B;gCAAE,GAAG,KAAK;gCAAE;4BAAW,IACvB,UACD,CAAA,GAAA,yHAAA,CAAA,qBAAkB,AAAD,EAAE,MAAM,GAAG,CAAC,CAAA,QAChC,UAAU,QAAQ,CAAC,MAAM,QAAQ,IAC7B;gCAAE,GAAG,KAAK;gCAAE;4BAAW,IACvB;oBAER;oBACA,cAAc;oBACd,YAAY,CAAC;wBACX,MAAM,gBAAgB;4BAAE,GAAG,MAAM,UAAU;wBAAC;wBAC5C,OAAO,aAAa,CAAC,KAAK;wBAC1B,OAAO;oBACT,CAAC;gBACH,CAAC;QACH;QAEA,gBAAgB,CAAC,MAAc;YAC7B,MAAM,eAAe,CAAC,MAAM,UAAU;QACxC;QAEA,yBAAyB,CAAC,MAAc;YACtC,MAAM,wBAAwB,CAAC,MAAM,WAAW;QAClD;QAEA,SAAS;QACT,eAAe,CAAC;YACd,MAAM,QAAQ;YAEd,WAAW;YACX,MAAM,cAAc,WAAW,GAAG,CAAC;YACnC,IAAI,aAAa;gBACf,OAAO;YACT;YAEA,SAAS;YACT,IAAI,MAAM,UAAU,CAAC,KAAK,EAAE;gBAC1B,WAAW,GAAG,CAAC,MAAM,MAAM,UAAU,CAAC,KAAK;gBAC3C,OAAO,MAAM,UAAU,CAAC,KAAK;YAC/B;YAEA,SAAS;YACT,MAAM,aAAa,MAAM,UAAU,CAAC,KAAK,IAAI,CAAA,GAAA,yHAAA,CAAA,qBAAkB,AAAD,EAAE;YAChE,MAAM,QAAQ,CAAA,GAAA,0HAAA,CAAA,sBAAmB,AAAD,EAAE,YAAY,MAAM,UAAU;YAE9D,WAAW;YACX,WAAW,GAAG,CAAC,MAAM;YAErB,0BAA0B;YAC1B,WAAW;gBACT,IAAI,CAAA,QAAS,CAAC;wBACZ,YAAY;4BACV,GAAG,MAAM,UAAU;4BACnB,CAAC,KAAK,EAAE;wBACV;oBACF,CAAC;YACH,GAAG;YAEH,OAAO;QACT;QAEA,kBAAkB;QAClB,mBAAmB,CAAC;YAClB,MAAM,QAAQ;YAEd,WAAW;YACX,MAAM,cAAc,WAAW,GAAG,CAAC;YACnC,IAAI,aAAa;gBACf,OAAO;YACT;YAEA,SAAS;YACT,IAAI,MAAM,UAAU,CAAC,KAAK,EAAE;gBAC1B,WAAW,GAAG,CAAC,MAAM,MAAM,UAAU,CAAC,KAAK;gBAC3C,OAAO,MAAM,UAAU,CAAC,KAAK;YAC/B;YAEA,qBAAqB;YACrB,OAAO;QACT;QAEA,oBAAoB,OAAO;YACzB,MAAM,aAAa,CAAC,MAAM,EAAE,MAAM;YAElC,IAAI;gBACF,8BAA8B;gBAC9B,WAAW;oBACT,MAAM,eAAe,CAAC,YAAY;gBACpC,GAAG;gBAEH,iBAAiB;gBACjB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,MAAM,QAAQ,MAAM,aAAa,CAAC;gBAElC,WAAW;gBACX,WAAW;oBACT,MAAM,eAAe,CAAC,YAAY;gBACpC,GAAG;gBAEH,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,WAAW;gBACX,WAAW;oBACT,MAAM,eAAe,CAAC,YAAY;gBACpC,GAAG;gBACH,MAAM;YACR;QACF;QAEA,iBAAiB,CAAC;YAChB,WAAW;YACX,WAAW,MAAM,CAAC;YAClB,IAAI,CAAA;gBACF,MAAM,gBAAgB;oBAAE,GAAG,MAAM,UAAU;gBAAC;gBAC5C,OAAO,aAAa,CAAC,KAAK;gBAC1B,OAAO;oBAAE,YAAY;gBAAc;YACrC;QACF;QAEA,oBAAoB;YAClB,SAAS;YACT,WAAW,KAAK;YAChB,IAAI;gBAAE,YAAY,CAAC;YAAE;QACvB;QAEA,sBAAsB,CAAC;YACrB,WAAW;YACX,IAAI,cAAc,gBAAgB,IAAI;gBACpC,QAAQ,IAAI,CAAC;gBACb;YACF;YAEA,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM;YAC7C,cAAc,kBAAkB,CAAC;QACnC;QAEA,SAAS;QACT,iBAAiB,CAAC,KAAa;YAC7B,IAAI,CAAA,YAAa,CAAC;oBAChB,eAAe;wBACb,GAAG,UAAU,aAAa;wBAC1B,CAAC,IAAI,EAAE;oBACT;gBACF,CAAC;YACD,eAAe,eAAe,CAAC,KAAK;QACtC;QAEA,iBAAiB,CAAC;YAChB,MAAM,QAAQ;YACd,OAAO,MAAM,aAAa,CAAC,IAAI,IAAI;QACrC;QAEA,SAAS;QACT,mBAAmB,CAAC;YAClB,IAAI,CAAA,QAAS,CAAC;oBACZ,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE,gBAAgB;oBAAO;gBAC5C,CAAC;QACH;QAEA,kBAAkB,CAAC;YACjB,IAAI,CAAA,QAAS,CAAC;oBACZ,IAAI;wBACF,GAAG,MAAM,EAAE;wBACX,gBAAgB;+BAAI,MAAM,EAAE,CAAC,cAAc;4BAAE;yBAAM;oBACrD;gBACF,CAAC;QACH;QAEA,qBAAqB,CAAC;YACpB,IAAI,CAAA,QAAS,CAAC;oBACZ,IAAI;wBACF,GAAG,MAAM,EAAE;wBACX,gBAAgB,MAAM,EAAE,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM;oBAC5D;gBACF,CAAC;QACH;QAEA,qBAAqB;YACnB,IAAI,CAAA,QAAS,CAAC;oBACZ,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE,gBAAgB,EAAE;oBAAC;gBACxC,CAAC;QACH;QAEA,wBAAwB,CAAC;YACvB,IAAI,CAAA,QAAS,CAAC;oBACZ,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE,qBAAqB;oBAAK;gBAC/C,CAAC;QACH;QAEA,gBAAgB,CAAC;YACf,IAAI,CAAA,QAAS,CAAC;oBACZ,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE,aAAa;oBAAK;gBACvC,CAAC;QACH;QAEA,YAAY;QACZ,mBAAmB,CAAC,SAAiB;YACnC,IAAI,CAAA;gBACF,MAAM,qBAAqB,IAAI,IAAI,MAAM,aAAa,CAAC,eAAe;gBACtE,IAAI,cAAc;oBAChB,mBAAmB,GAAG,CAAC;gBACzB,OAAO;oBACL,mBAAmB,MAAM,CAAC;gBAC5B;gBACA,OAAO;oBACL,eAAe;wBACb,GAAG,MAAM,aAAa;wBACtB,iBAAiB;oBACnB;gBACF;YACF;QACF;QAEA,kBAAkB,CAAC,SAAiB;YAClC,IAAI,CAAA;gBACF,MAAM,oBAAoB,IAAI,IAAI,MAAM,aAAa,CAAC,cAAc;gBACpE,MAAM,qBAAqB;oBAAE,GAAG,MAAM,aAAa,CAAC,eAAe;gBAAC;gBAEpE,IAAI,aAAa;oBACf,kBAAkB,GAAG,CAAC;oBACtB,kBAAkB,CAAC,QAAQ,GAAG,KAAK,GAAG;gBACxC,OAAO;oBACL,kBAAkB,MAAM,CAAC;oBACzB,OAAO,kBAAkB,CAAC,QAAQ;gBACpC;gBAEA,OAAO;oBACL,eAAe;wBACb,GAAG,MAAM,aAAa;wBACtB,gBAAgB;wBAChB,iBAAiB;oBACnB;gBACF;YACF;QACF;QAEA,iBAAiB,CAAC;YAChB,MAAM,QAAQ;YACd,OAAO,MAAM,aAAa,CAAC,cAAc,CAAC,GAAG,CAAC;QAChD;QAEA,kBAAkB,CAAC;YACjB,MAAM,QAAQ;YACd,OAAO,MAAM,aAAa,CAAC,eAAe,CAAC,GAAG,CAAC;QACjD;QAEA,aAAa,CAAC,YAAqB;YACjC,IAAI,CAAA,QAAS,CAAC;oBACZ,IAAI;wBACF,GAAG,MAAM,EAAE;wBACX;wBACA,gBAAgB,aAAc,cAAc,OAAQ;oBACtD;gBACF,CAAC;QACH;QAEA,SAAS;QACT,mBAAmB;YACjB,MAAM,MAAM,IAAI;YAChB,MAAM,iBAAiB;gBACrB,MAAM,IAAI,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBACrC,MAAM,IAAI,QAAQ;gBAClB,QAAQ,IAAI,UAAU;gBACtB,QAAQ,IAAI,UAAU;YACxB;YACA,MAAM,cAAc,CAAA,GAAA,yHAAA,CAAA,qBAAkB,AAAD;YACrC,MAAM,eAAe;YAErB,4BAA4B;YAC5B,qBAAqB;YACrB,IAAI,gBAAgB,aAAa,eAAe,EAAE;gBAChD,IAAI;oBACF,aAAa;oBACb,iBAAiB;gBACnB;gBAEA,uBAAuB;gBACvB,MAAM,cAAc,aAAa,WAAW;gBAC5C,IAAI,aAAa,UAAU,CAAC,YAAY,EAAE;oBACxC,MAAM,gBAAgB,CAAA,GAAA,yHAAA,CAAA,wBAAqB,AAAD,EAAE,aAAa,UAAU,CAAC,YAAY,EAAE;oBAClF,IAAI,KAAK,SAAS,CAAC,mBAAmB,KAAK,SAAS,CAAC,aAAa,UAAU,CAAC,YAAY,GAAG;wBAC1F,IAAI,CAAA,QAAS,CAAC;gCACZ,YAAY;oCACV,GAAG,MAAM,UAAU;oCACnB,CAAC,YAAY,EAAE;gCACjB;4BACF,CAAC;oBACH;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,IAAI;oBAAE,aAAa;gBAAe;YACpC;QACF;QAEA,kBAAkB;YAChB,gBAAgB;YAChB;;QAwBF;QAEA,iBAAiB;YACf,IAAI,kBAAkB;gBACpB,iBAAiB,IAAI;gBACrB,mBAAmB;YACrB;QACF;QAEA,qBAAqB;YACnB,OAAO,mBAAmB,iBAAiB,QAAQ,KAAK;QAC1D;QAEA,QAAQ;QACR,eAAe;YACb,MAAM,QAAQ;YAEd,qBAAqB;YACrB;;YAQA,gBAAgB;YAChB,IAAI,MAAM,UAAU,CAAC,MAAM,KAAK,GAAG;gBACjC,IAAI;oBAAE,YAAY;gBAA0B;YAC9C;YAEA,eAAe;YACf,MAAM,cAAc,MAAM,WAAW;YACrC,IAAI,CAAC,MAAM,UAAU,CAAC,YAAY,EAAE;gBAClC,IAAI,CAAA,QAAS,CAAC;wBACZ,YAAY;4BACV,GAAG,MAAM,UAAU;4BACnB,CAAC,YAAY,EAAE,CAAA,GAAA,yHAAA,CAAA,qBAAkB,AAAD,EAAE;wBACpC;oBACF,CAAC;YACH;YAEA,WAAW;YACX;;YAsBA,eAAe;YACf,MAAM,oBAAoB,CAAC;YAE3B,UAAU;YACV,MAAM,gBAAgB;YAEtB,eAAe;YACf;;QAOF;QAEA,SAAS;QACT,2BAA2B,CAAC;YAC1B,IAAI,CAAA,QAAS,CAAC;oBACZ,gBAAgB;wBACd,GAAG,MAAM,cAAc;wBACvB,cAAc;oBAChB;gBACF,CAAC;QACH;QAEA,mBAAmB,CAAC;YAClB,IAAI,CAAA,QAAS,CAAC;oBACZ,gBAAgB;wBACd,GAAG,MAAM,cAAc;wBACvB,gBAAgB;oBAClB;gBACF,CAAC;QACH;QAEA,mBAAmB,CAAC;YAClB,IAAI,CAAA,QAAS,CAAC;oBACZ,gBAAgB;wBACd,GAAG,MAAM,cAAc;wBACvB,gBAAgB;oBAClB;gBACF,CAAC;QACH;QAEA,gBAAgB,CAAC;YACf,IAAI,CAAA,QAAS,CAAC;oBACZ,gBAAgB;wBACd,GAAG,MAAM,cAAc;wBACvB;oBACF;gBACF,CAAC;QACH;QAEA,gBAAgB,CAAC;YACf,IAAI,CAAA,QAAS,CAAC;oBACZ,gBAAgB;wBACd,GAAG,MAAM,cAAc;wBACvB;oBACF;gBACF,CAAC;QACH;QAEA,qBAAqB,CAAC;YACpB,IAAI,CAAA,QAAS,CAAC;oBACZ,gBAAgB;wBACd,GAAG,MAAM,cAAc;wBACvB,kBAAkB;oBACpB;gBACF,CAAC;QACH;QAEA,OAAO;QACP,YAAY,OAAO,QAAwB;YACzC,MAAM,QAAQ;YAEd,IAAI;gBACF,MAAM,cAAc,CAAC;gBACrB,MAAM,iBAAiB,CAAC;gBAExB,aAAa;gBACb,MAAM,EAAE,cAAc,EAAE,aAAa,EAAE,GAAG;gBAE1C,MAAM,iBAAiB,CAAC;gBAExB,IAAI,WAAW,QAAQ;oBACrB,eACE,MAAM,UAAU,EAChB,MAAM,UAAU,EAChB,MAAM,UAAU,EAChB;gBAEJ,OAAO;oBACL,cACE,MAAM,UAAU,EAChB,MAAM,UAAU,EAChB;gBAEJ;gBAEA,MAAM,iBAAiB,CAAC;gBAExB,mBAAmB;gBACnB,WAAW;oBACT,MAAM,cAAc,CAAC;oBACrB,MAAM,iBAAiB,CAAC;gBAC1B,GAAG;YAEL,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,SAAS;gBACvB,MAAM,cAAc,CAAC;gBACrB,MAAM,iBAAiB,CAAC;gBACxB,MAAM;YACR;QACF;QAEA,OAAO;QACP,YAAY,OAAO,MAAY,WAA2B,0HAAA,CAAA,iBAAc,CAAC,KAAK;YAC5E,MAAM,QAAQ;YAEd,IAAI;gBACF,MAAM,cAAc,CAAC;gBACrB,MAAM,iBAAiB,CAAC;gBAExB,aAAa;gBACb,MAAM,EAAE,eAAe,EAAE,kBAAkB,EAAE,aAAa,EAAE,GAAG;gBAE/D,MAAM,iBAAiB,CAAC;gBAExB,SAAS;gBACT,MAAM,UAAU,MAAM,gBAAgB;gBACtC,MAAM,iBAAiB,CAAC;gBAExB,SAAS;gBACT,MAAM,aAAa,mBAAmB;gBACtC,IAAI,CAAC,WAAW,OAAO,IAAI,CAAC,WAAW,IAAI,EAAE;oBAC3C,MAAM,SAAuB;wBAC3B,SAAS;wBACT,SAAS,eAAe,WAAW,MAAM,CAAC,IAAI,CAAC;wBAC/C,YAAY;4BACV,iBAAiB;4BACjB,mBAAmB;4BACnB,iBAAiB;4BACjB,mBAAmB;4BACnB,cAAc;wBAChB;oBACF;oBACA,MAAM,mBAAmB,CAAC;oBAC1B,OAAO;gBACT;gBAEA,MAAM,iBAAiB,CAAC;gBAExB,OAAO;gBACP,MAAM,eAAe,cACnB,WAAW,IAAI,EACf,MAAM,UAAU,EAChB,MAAM,UAAU,EAChB,MAAM,UAAU,EAChB;gBAGF,MAAM,iBAAiB,CAAC;gBAExB,IAAI,aAAa,OAAO,IAAI,AAAC,aAAqB,IAAI,EAAE;oBACtD,OAAO;oBACP,MAAM,UAAU,AAAC,aAAqB,IAAI;oBAC1C,IAAI;wBACF,YAAY,QAAQ,UAAU;wBAC9B,YAAY,QAAQ,UAAU;wBAC9B,YAAY,QAAQ,UAAU;oBAChC;oBAEA,kBAAkB;oBAClB,WAAW,KAAK;oBAChB,cAAc,UAAU;gBAC1B;gBAEA,MAAM,iBAAiB,CAAC;gBACxB,MAAM,mBAAmB,CAAC;gBAE1B,SAAS;gBACT,WAAW;oBACT,MAAM,cAAc,CAAC;oBACrB,MAAM,iBAAiB,CAAC;gBAC1B,GAAG;gBAEH,OAAO;YAET,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,SAAS;gBACvB,MAAM,SAAuB;oBAC3B,SAAS;oBACT,SAAS,gBAAgB,AAAC,MAAgB,OAAO;oBACjD,YAAY;wBACV,iBAAiB;wBACjB,mBAAmB;wBACnB,iBAAiB;wBACjB,mBAAmB;wBACnB,cAAc;oBAChB;gBACF;gBAEA,MAAM,cAAc,CAAC;gBACrB,MAAM,iBAAiB,CAAC;gBACxB,MAAM,mBAAmB,CAAC;gBAE1B,OAAO;YACT;QACF;IACF,CAAC,GACD;IACE,MAAM;IACN,yBAAyB;IACzB,YAAY,CAAC,QAAU,CAAC;YACtB,aAAa,MAAM,WAAW;YAC9B,YAAY,MAAM,UAAU;YAC5B,YAAY,MAAM,UAAU;YAC5B,YAAY,MAAM,UAAU;QAE9B,CAAC;IACD,8CAA8C;IAC9C,SAAS,CAAA,GAAA,6IAAA,CAAA,oBAAiB,AAAD,EAAE;QACzB,wCAAmC;YACjC,sBAAsB;YACtB,OAAO;gBACL,SAAS,IAAM;gBACf,SAAS,KAAO;gBAChB,YAAY,KAAO;YACrB;QACF;;;IAEF;AACF", "debugId": null}}, {"offset": {"line": 2112, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/CustomDatePicker.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"customDayPicker\": \"CustomDatePicker-module__5xmwsa__customDayPicker\",\n  \"rdpCaptionCustom\": \"CustomDatePicker-module__5xmwsa__rdpCaptionCustom\",\n  \"rdpChevronCustom\": \"CustomDatePicker-module__5xmwsa__rdpChevronCustom\",\n  \"rdpDayButtonCustom\": \"CustomDatePicker-module__5xmwsa__rdpDayButtonCustom\",\n  \"rdpRootCustom\": \"CustomDatePicker-module__5xmwsa__rdpRootCustom\",\n  \"rdpSelectedCustom\": \"CustomDatePicker-module__5xmwsa__rdpSelectedCustom\",\n  \"rdpTodayCustom\": \"CustomDatePicker-module__5xmwsa__rdpTodayCustom\",\n  \"rdpWeekdaysCustom\": \"CustomDatePicker-module__5xmwsa__rdpWeekdaysCustom\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 2126, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/hooks/useIsClient.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useState } from 'react';\r\n\r\n/**\r\n * Hook to detect if code is running on the client side\r\n * Helps prevent SSR hydration mismatches for client-only features\r\n * \r\n * @returns boolean - true if running on client, false during SSR\r\n */\r\nexport function useIsClient(): boolean {\r\n  const [isClient, setIsClient] = useState(false);\r\n\r\n  useEffect(() => {\r\n    // This effect only runs on the client side\r\n    setIsClient(true);\r\n  }, []);\r\n\r\n  return isClient;\r\n}\r\n\r\n/**\r\n * Hook to safely get current time only on client side\r\n * Returns null during SSR to prevent hydration mismatches\r\n * \r\n * @returns Date | null - current date on client, null on server\r\n */\r\nexport function useClientTime(): Date | null {\r\n  const isClient = useIsClient();\r\n  const [currentTime, setCurrentTime] = useState<Date | null>(null);\r\n\r\n  useEffect(() => {\r\n    if (isClient) {\r\n      setCurrentTime(new Date());\r\n    }\r\n  }, [isClient]);\r\n\r\n  return currentTime;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;AAUO,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,2CAA2C;QAC3C,YAAY;IACd,GAAG,EAAE;IAEL,OAAO;AACT;AAQO,SAAS;IACd,MAAM,WAAW;IACjB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAE5D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ,eAAe,IAAI;QACrB;IACF,GAAG;QAAC;KAAS;IAEb,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2157, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/components/CustomDatePicker.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { DayPicker, getDefaultClassNames } from 'react-day-picker';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport { Calendar, X } from 'lucide-react';\r\nimport 'react-day-picker/style.css';\r\nimport styles from './CustomDatePicker.module.css';\r\nimport { useIsClient } from '@/hooks/useIsClient';\r\n\r\ninterface CustomDatePickerProps {\r\n  value: string;\r\n  onChange: (date: string) => void;\r\n  className?: string;\r\n}\r\n\r\nexport function CustomDatePicker({ value, onChange, className = '' }: CustomDatePickerProps) {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [selectedDate, setSelectedDate] = useState<Date | undefined>(\r\n    value ? new Date(value) : undefined\r\n  );\r\n  const isClient = useIsClient();\r\n\r\n  const defaultClassNames = getDefaultClassNames();\r\n\r\n  // 同步外部value变化到内部状态\r\n  useEffect(() => {\r\n    if (value) {\r\n      setSelectedDate(new Date(value));\r\n    } else {\r\n      setSelectedDate(undefined);\r\n    }\r\n  }, [value]);\r\n\r\n  // 格式化显示日期 - 添加SSR安全检查\r\n  const formatDisplayDate = (date: Date) => {\r\n    // 在SSR期间返回安全的默认值\r\n    if (!isClient) {\r\n      return '1970年1月1日星期四';\r\n    }\r\n\r\n    return date.toLocaleDateString('zh-CN', {\r\n      year: 'numeric',\r\n      month: 'long',\r\n      day: 'numeric',\r\n      weekday: 'long'\r\n    });\r\n  };\r\n\r\n  // 处理日期选择\r\n  const handleDateSelect = (date: Date | undefined) => {\r\n    if (date) {\r\n      setSelectedDate(date);\r\n      const formattedDate = date.toISOString().split('T')[0];\r\n      onChange(formattedDate);\r\n      setIsOpen(false);\r\n    }\r\n  };\r\n\r\n  // 处理输入框点击\r\n  const handleInputClick = () => {\r\n    setIsOpen(!isOpen);\r\n  };\r\n\r\n  // 处理关闭\r\n  const handleClose = () => {\r\n    setIsOpen(false);\r\n  };\r\n\r\n  return (\r\n    <div className={`relative ${className}`}>\r\n      {/* 日期输入框 */}\r\n      <motion.div\r\n        className=\"card flex items-center cursor-pointer\"\r\n        style={{\r\n          gap: 'var(--spacing-2)',\r\n          padding: 'var(--spacing-4)',\r\n          background: 'white',\r\n          borderRadius: 'var(--radius-xl)',\r\n          boxShadow: 'var(--shadow-md)',\r\n          border: '1px solid var(--neutral-200)',\r\n          minWidth: '200px',\r\n          justifyContent: 'center'\r\n        }}\r\n        onClick={handleInputClick}\r\n        whileHover={{ \r\n          scale: 1.02,\r\n          boxShadow: 'var(--shadow-lg)',\r\n          borderColor: 'var(--neutral-300)'\r\n        }}\r\n        whileTap={{ scale: 0.98 }}\r\n        transition={{ duration: 0.15 }}\r\n      >\r\n        <Calendar size={18} style={{ color: 'var(--neutral-500)' }} />\r\n        <span style={{ \r\n          fontWeight: 'var(--font-weight-medium)', \r\n          color: 'var(--neutral-900)',\r\n          fontSize: 'var(--font-size-base)'\r\n        }}>\r\n          {selectedDate ? formatDisplayDate(selectedDate) : '选择日期'}\r\n        </span>\r\n      </motion.div>\r\n\r\n      {/* 日期选择器弹窗 */}\r\n      <AnimatePresence>\r\n        {isOpen && (\r\n          <>\r\n            {/* 遮罩层 */}\r\n            <motion.div\r\n              className=\"fixed inset-0 z-40\"\r\n              style={{ background: 'rgba(0, 0, 0, 0.1)' }}\r\n              initial={{ opacity: 0 }}\r\n              animate={{ opacity: 1 }}\r\n              exit={{ opacity: 0 }}\r\n              onClick={handleClose}\r\n            />\r\n\r\n            {/* 日期选择器容器 */}\r\n            <motion.div\r\n              className=\"absolute z-50\"\r\n              style={{\r\n                top: '100%',\r\n                left: '50%',\r\n                marginTop: 'var(--spacing-2)',\r\n                background: 'white',\r\n                borderRadius: 'var(--radius-xl)',\r\n                boxShadow: 'var(--shadow-2xl)',\r\n                border: '1px solid var(--neutral-200)',\r\n                padding: 'var(--spacing-6)',\r\n                minWidth: '320px'\r\n              }}\r\n              initial={{ \r\n                opacity: 0, \r\n                scale: 0.9, \r\n                y: -10,\r\n                x: '-50%'\r\n              }}\r\n              animate={{ \r\n                opacity: 1, \r\n                scale: 1, \r\n                y: 0,\r\n                x: '-50%'\r\n              }}\r\n              exit={{ \r\n                opacity: 0, \r\n                scale: 0.9, \r\n                y: -10,\r\n                x: '-50%'\r\n              }}\r\n              transition={{ \r\n                duration: 0.2, \r\n                ease: [0.4, 0, 0.2, 1] \r\n              }}\r\n            >\r\n              {/* 关闭按钮 */}\r\n              <motion.button\r\n                onClick={handleClose}\r\n                className=\"absolute top-3 right-3 p-1\"\r\n                style={{\r\n                  background: 'var(--neutral-100)',\r\n                  borderRadius: 'var(--radius-full)',\r\n                  border: 'none',\r\n                  cursor: 'pointer'\r\n                }}\r\n                whileHover={{ \r\n                  background: 'var(--neutral-200)',\r\n                  scale: 1.1\r\n                }}\r\n                whileTap={{ scale: 0.9 }}\r\n              >\r\n                <X size={16} style={{ color: 'var(--neutral-600)' }} />\r\n              </motion.button>\r\n\r\n              {/* DayPicker组件 */}\r\n              <div className={styles.customDayPicker}>\r\n                <DayPicker\r\n                  mode=\"single\"\r\n                  selected={selectedDate}\r\n                  onSelect={handleDateSelect}\r\n                  showOutsideDays\r\n                  className={defaultClassNames.root}\r\n                  classNames={{\r\n                    ...defaultClassNames,\r\n                    today: `${defaultClassNames.today} ${styles.rdpTodayCustom}`,\r\n                    selected: `${defaultClassNames.selected} ${styles.rdpSelectedCustom}`,\r\n                    root: `${defaultClassNames.root} ${styles.rdpRootCustom}`,\r\n                    chevron: `${defaultClassNames.chevron} ${styles.rdpChevronCustom}`,\r\n                    month_caption: `${defaultClassNames.month_caption} ${styles.rdpCaptionCustom}`,\r\n                    weekdays: `${defaultClassNames.weekdays} ${styles.rdpWeekdaysCustom}`,\r\n                    day_button: `${defaultClassNames.day_button} ${styles.rdpDayButtonCustom}`\r\n                  }}\r\n                />\r\n              </div>\r\n            </motion.div>\r\n          </>\r\n        )}\r\n      </AnimatePresence>\r\n\r\n\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAEA;AACA;AARA;;;;;;;;;AAgBO,SAAS,iBAAiB,EAAE,KAAK,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAyB;IACzF,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC7C,QAAQ,IAAI,KAAK,SAAS;IAE5B,MAAM,WAAW,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,oBAAoB,CAAA,GAAA,wLAAA,CAAA,uBAAoB,AAAD;IAE7C,mBAAmB;IACnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO;YACT,gBAAgB,IAAI,KAAK;QAC3B,OAAO;YACL,gBAAgB;QAClB;IACF,GAAG;QAAC;KAAM;IAEV,sBAAsB;IACtB,MAAM,oBAAoB,CAAC;QACzB,iBAAiB;QACjB,IAAI,CAAC,UAAU;YACb,OAAO;QACT;QAEA,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;YACP,KAAK;YACL,SAAS;QACX;IACF;IAEA,SAAS;IACT,MAAM,mBAAmB,CAAC;QACxB,IAAI,MAAM;YACR,gBAAgB;YAChB,MAAM,gBAAgB,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YACtD,SAAS;YACT,UAAU;QACZ;IACF;IAEA,UAAU;IACV,MAAM,mBAAmB;QACvB,UAAU,CAAC;IACb;IAEA,OAAO;IACP,MAAM,cAAc;QAClB,UAAU;IACZ;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;;0BAErC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBACL,KAAK;oBACL,SAAS;oBACT,YAAY;oBACZ,cAAc;oBACd,WAAW;oBACX,QAAQ;oBACR,UAAU;oBACV,gBAAgB;gBAClB;gBACA,SAAS;gBACT,YAAY;oBACV,OAAO;oBACP,WAAW;oBACX,aAAa;gBACf;gBACA,UAAU;oBAAE,OAAO;gBAAK;gBACxB,YAAY;oBAAE,UAAU;gBAAK;;kCAE7B,8OAAC,0MAAA,CAAA,WAAQ;wBAAC,MAAM;wBAAI,OAAO;4BAAE,OAAO;wBAAqB;;;;;;kCACzD,8OAAC;wBAAK,OAAO;4BACX,YAAY;4BACZ,OAAO;4BACP,UAAU;wBACZ;kCACG,eAAe,kBAAkB,gBAAgB;;;;;;;;;;;;0BAKtD,8OAAC,yLAAA,CAAA,kBAAe;0BACb,wBACC;;sCAEE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,OAAO;gCAAE,YAAY;4BAAqB;4BAC1C,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,MAAM;gCAAE,SAAS;4BAAE;4BACnB,SAAS;;;;;;sCAIX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,OAAO;gCACL,KAAK;gCACL,MAAM;gCACN,WAAW;gCACX,YAAY;gCACZ,cAAc;gCACd,WAAW;gCACX,QAAQ;gCACR,SAAS;gCACT,UAAU;4BACZ;4BACA,SAAS;gCACP,SAAS;gCACT,OAAO;gCACP,GAAG,CAAC;gCACJ,GAAG;4BACL;4BACA,SAAS;gCACP,SAAS;gCACT,OAAO;gCACP,GAAG;gCACH,GAAG;4BACL;4BACA,MAAM;gCACJ,SAAS;gCACT,OAAO;gCACP,GAAG,CAAC;gCACJ,GAAG;4BACL;4BACA,YAAY;gCACV,UAAU;gCACV,MAAM;oCAAC;oCAAK;oCAAG;oCAAK;iCAAE;4BACxB;;8CAGA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS;oCACT,WAAU;oCACV,OAAO;wCACL,YAAY;wCACZ,cAAc;wCACd,QAAQ;wCACR,QAAQ;oCACV;oCACA,YAAY;wCACV,YAAY;wCACZ,OAAO;oCACT;oCACA,UAAU;wCAAE,OAAO;oCAAI;8CAEvB,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,MAAM;wCAAI,OAAO;4CAAE,OAAO;wCAAqB;;;;;;;;;;;8CAIpD,8OAAC;oCAAI,WAAW,iJAAA,CAAA,UAAM,CAAC,eAAe;8CACpC,cAAA,8OAAC,kKAAA,CAAA,YAAS;wCACR,MAAK;wCACL,UAAU;wCACV,UAAU;wCACV,eAAe;wCACf,WAAW,kBAAkB,IAAI;wCACjC,YAAY;4CACV,GAAG,iBAAiB;4CACpB,OAAO,GAAG,kBAAkB,KAAK,CAAC,CAAC,EAAE,iJAAA,CAAA,UAAM,CAAC,cAAc,EAAE;4CAC5D,UAAU,GAAG,kBAAkB,QAAQ,CAAC,CAAC,EAAE,iJAAA,CAAA,UAAM,CAAC,iBAAiB,EAAE;4CACrE,MAAM,GAAG,kBAAkB,IAAI,CAAC,CAAC,EAAE,iJAAA,CAAA,UAAM,CAAC,aAAa,EAAE;4CACzD,SAAS,GAAG,kBAAkB,OAAO,CAAC,CAAC,EAAE,iJAAA,CAAA,UAAM,CAAC,gBAAgB,EAAE;4CAClE,eAAe,GAAG,kBAAkB,aAAa,CAAC,CAAC,EAAE,iJAAA,CAAA,UAAM,CAAC,gBAAgB,EAAE;4CAC9E,UAAU,GAAG,kBAAkB,QAAQ,CAAC,CAAC,EAAE,iJAAA,CAAA,UAAM,CAAC,iBAAiB,EAAE;4CACrE,YAAY,GAAG,kBAAkB,UAAU,CAAC,CAAC,EAAE,iJAAA,CAAA,UAAM,CAAC,kBAAkB,EAAE;wCAC5E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWlB", "debugId": null}}, {"offset": {"line": 2427, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/components/DateNavigator.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { ChevronLeft, ChevronRight, Home, Settings } from 'lucide-react';\r\nimport { useAppStore } from '@/stores/useAppStore';\r\nimport { formatDate } from '@/utils/timeUtils';\r\nimport { CustomDatePicker } from './CustomDatePicker';\r\nimport { useIsClient } from '@/hooks/useIsClient';\r\n\r\ninterface DateNavigatorProps {\r\n  onManageActivities?: () => void;\r\n}\r\n\r\nexport function DateNavigator({ onManageActivities }: DateNavigatorProps = {}) {\r\n  const {\r\n    currentDate,\r\n    currentTime,\r\n    goToPreviousDay,\r\n    goToNextDay,\r\n    goToToday,\r\n    setCurrentDate\r\n  } = useAppStore();\r\n\r\n  const isClient = useIsClient();\r\n\r\n  // 使用store中的响应式时间状态，避免SSR hydration mismatch\r\n  const isToday = isClient ? currentDate === currentTime.date : false;\r\n  const formattedDate = formatDate(currentDate);\r\n\r\n  // 处理日期输入变化\r\n  const handleDateChange = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n    const newDate = event.target.value;\r\n    if (newDate) {\r\n      setCurrentDate(newDate);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex items-center justify-center mb-8\" style={{ gap: 'var(--spacing-4)' }}>\r\n      {/* 上一天按钮 */}\r\n      <motion.button\r\n        onClick={goToPreviousDay}\r\n        className=\"btn-secondary\"\r\n        style={{\r\n          padding: 'var(--spacing-3)',\r\n          borderRadius: 'var(--radius-full)',\r\n          background: 'white',\r\n          boxShadow: 'var(--shadow-md)',\r\n          border: '1px solid var(--neutral-200)'\r\n        }}\r\n        whileHover={{\r\n          scale: 1.05,\r\n          boxShadow: 'var(--shadow-lg)',\r\n          borderColor: 'var(--neutral-300)'\r\n        }}\r\n        whileTap={{ scale: 0.95 }}\r\n        transition={{ duration: 0.15 }}\r\n      >\r\n        <ChevronLeft size={20} style={{ color: 'var(--neutral-600)' }} />\r\n      </motion.button>\r\n\r\n      {/* 日期显示和选择器 */}\r\n      <div className=\"flex items-center\" style={{ gap: 'var(--spacing-3)' }}>\r\n        {/* 美观的日期选择器 */}\r\n        <CustomDatePicker\r\n          value={currentDate}\r\n          onChange={setCurrentDate}\r\n        />\r\n\r\n        {/* 回到今天按钮 */}\r\n        {!isToday && (\r\n          <motion.button\r\n            onClick={goToToday}\r\n            className=\"btn-primary flex items-center\"\r\n            style={{\r\n              gap: 'var(--spacing-2)',\r\n              padding: 'var(--spacing-3) var(--spacing-4)',\r\n              background: 'var(--primary-500)',\r\n              color: 'white',\r\n              borderRadius: 'var(--radius-lg)',\r\n              fontSize: 'var(--font-size-sm)',\r\n              fontWeight: 'var(--font-weight-medium)'\r\n            }}\r\n            whileHover={{\r\n              scale: 1.05,\r\n              background: 'var(--primary-600)',\r\n              boxShadow: 'var(--shadow-md)'\r\n            }}\r\n            whileTap={{ scale: 0.95 }}\r\n            initial={{ opacity: 0, x: 10 }}\r\n            animate={{ opacity: 1, x: 0 }}\r\n            exit={{ opacity: 0, x: 10 }}\r\n            transition={{ duration: 0.15 }}\r\n          >\r\n            <Home size={16} />\r\n            <span>今天</span>\r\n          </motion.button>\r\n        )}\r\n\r\n        {/* 活动管理按钮 */}\r\n        {onManageActivities && (\r\n          <motion.button\r\n            onClick={onManageActivities}\r\n            className=\"btn-secondary flex items-center\"\r\n            style={{\r\n              gap: 'var(--spacing-2)',\r\n              padding: 'var(--spacing-3) var(--spacing-4)',\r\n              background: 'white',\r\n              color: 'var(--neutral-700)',\r\n              borderRadius: 'var(--radius-lg)',\r\n              fontSize: 'var(--font-size-sm)',\r\n              fontWeight: 'var(--font-weight-medium)',\r\n              border: '1px solid var(--neutral-200)',\r\n              boxShadow: 'var(--shadow-md)'\r\n            }}\r\n            whileHover={{\r\n              scale: 1.05,\r\n              background: 'var(--neutral-50)',\r\n              borderColor: 'var(--neutral-300)',\r\n              boxShadow: 'var(--shadow-lg)'\r\n            }}\r\n            whileTap={{ scale: 0.95 }}\r\n            initial={{ opacity: 0, x: 10 }}\r\n            animate={{ opacity: 1, x: 0 }}\r\n            exit={{ opacity: 0, x: 10 }}\r\n            transition={{ duration: 0.15 }}\r\n            title=\"管理活动\"\r\n          >\r\n            <Settings size={16} />\r\n            <span>活动管理</span>\r\n          </motion.button>\r\n        )}\r\n      </div>\r\n\r\n      {/* 下一天按钮 */}\r\n      <motion.button\r\n        onClick={goToNextDay}\r\n        className=\"btn-secondary\"\r\n        style={{\r\n          padding: 'var(--spacing-3)',\r\n          borderRadius: 'var(--radius-full)',\r\n          background: 'white',\r\n          boxShadow: 'var(--shadow-md)',\r\n          border: '1px solid var(--neutral-200)'\r\n        }}\r\n        whileHover={{\r\n          scale: 1.05,\r\n          boxShadow: 'var(--shadow-lg)',\r\n          borderColor: 'var(--neutral-300)'\r\n        }}\r\n        whileTap={{ scale: 0.95 }}\r\n        transition={{ duration: 0.15 }}\r\n      >\r\n        <ChevronRight size={20} style={{ color: 'var(--neutral-600)' }} />\r\n      </motion.button>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AARA;;;;;;;;AAcO,SAAS,cAAc,EAAE,kBAAkB,EAAsB,GAAG,CAAC,CAAC;IAC3E,MAAM,EACJ,WAAW,EACX,WAAW,EACX,eAAe,EACf,WAAW,EACX,SAAS,EACT,cAAc,EACf,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IAEd,MAAM,WAAW,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IAE3B,4CAA4C;IAC5C,MAAM,UAAU,WAAW,gBAAgB,YAAY,IAAI,GAAG;IAC9D,MAAM,gBAAgB,CAAA,GAAA,yHAAA,CAAA,aAAU,AAAD,EAAE;IAEjC,WAAW;IACX,MAAM,mBAAmB,CAAC;QACxB,MAAM,UAAU,MAAM,MAAM,CAAC,KAAK;QAClC,IAAI,SAAS;YACX,eAAe;QACjB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;QAAwC,OAAO;YAAE,KAAK;QAAmB;;0BAEtF,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS;gBACT,WAAU;gBACV,OAAO;oBACL,SAAS;oBACT,cAAc;oBACd,YAAY;oBACZ,WAAW;oBACX,QAAQ;gBACV;gBACA,YAAY;oBACV,OAAO;oBACP,WAAW;oBACX,aAAa;gBACf;gBACA,UAAU;oBAAE,OAAO;gBAAK;gBACxB,YAAY;oBAAE,UAAU;gBAAK;0BAE7B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,MAAM;oBAAI,OAAO;wBAAE,OAAO;oBAAqB;;;;;;;;;;;0BAI9D,8OAAC;gBAAI,WAAU;gBAAoB,OAAO;oBAAE,KAAK;gBAAmB;;kCAElE,8OAAC,sIAAA,CAAA,mBAAgB;wBACf,OAAO;wBACP,UAAU;;;;;;oBAIX,CAAC,yBACA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,SAAS;wBACT,WAAU;wBACV,OAAO;4BACL,KAAK;4BACL,SAAS;4BACT,YAAY;4BACZ,OAAO;4BACP,cAAc;4BACd,UAAU;4BACV,YAAY;wBACd;wBACA,YAAY;4BACV,OAAO;4BACP,YAAY;4BACZ,WAAW;wBACb;wBACA,UAAU;4BAAE,OAAO;wBAAK;wBACxB,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC1B,YAAY;4BAAE,UAAU;wBAAK;;0CAE7B,8OAAC,mMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;0CACZ,8OAAC;0CAAK;;;;;;;;;;;;oBAKT,oCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,SAAS;wBACT,WAAU;wBACV,OAAO;4BACL,KAAK;4BACL,SAAS;4BACT,YAAY;4BACZ,OAAO;4BACP,cAAc;4BACd,UAAU;4BACV,YAAY;4BACZ,QAAQ;4BACR,WAAW;wBACb;wBACA,YAAY;4BACV,OAAO;4BACP,YAAY;4BACZ,aAAa;4BACb,WAAW;wBACb;wBACA,UAAU;4BAAE,OAAO;wBAAK;wBACxB,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC1B,YAAY;4BAAE,UAAU;wBAAK;wBAC7B,OAAM;;0CAEN,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,MAAM;;;;;;0CAChB,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;0BAMZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS;gBACT,WAAU;gBACV,OAAO;oBACL,SAAS;oBACT,cAAc;oBACd,YAAY;oBACZ,WAAW;oBACX,QAAQ;gBACV;gBACA,YAAY;oBACV,OAAO;oBACP,WAAW;oBACX,aAAa;gBACf;gBACA,UAAU;oBAAE,OAAO;gBAAK;gBACxB,YAAY;oBAAE,UAAU;gBAAK;0BAE7B,cAAA,8OAAC,sNAAA,CAAA,eAAY;oBAAC,MAAM;oBAAI,OAAO;wBAAE,OAAO;oBAAqB;;;;;;;;;;;;;;;;;AAIrE", "debugId": null}}, {"offset": {"line": 2686, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/utils/dragUtils.ts"], "sourcesContent": ["/**\r\n * 拖拽工具函数\r\n * 为6列×8行时间轴流式布局提供智能拖拽支持\r\n */\r\n\r\nexport interface DragDirection {\r\n  type: 'vertical' | 'diagonal';\r\n  isColumnPrimary: boolean; // 是否为列优先拖拽\r\n  suggestion?: string; // 拖拽建议\r\n}\r\n\r\nexport interface DragPath {\r\n  startSlot: number;\r\n  endSlot: number;\r\n  direction: DragDirection;\r\n  selectedSlots: number[];\r\n  timeRange: {\r\n    start: string;\r\n    end: string;\r\n    duration: number; // 分钟数\r\n  };\r\n}\r\n\r\n/**\r\n * 检测拖拽方向\r\n * @param startSlot 起始时间槽\r\n * @param currentSlot 当前时间槽\r\n * @returns 拖拽方向信息\r\n */\r\nexport function detectDragDirection(startSlot: number, currentSlot: number): DragDirection {\r\n  // 新布局：6列×8行，每列代表4小时时间段\r\n  // timeSlot到列的映射：col = Math.floor(timeSlot / 8)\r\n  const startCol = Math.floor(startSlot / 8);\r\n  const currentCol = Math.floor(currentSlot / 8);\r\n\r\n  // 垂直拖拽（同一列内）\r\n  if (startCol === currentCol) {\r\n    return {\r\n      type: 'vertical',\r\n      isColumnPrimary: true,\r\n      suggestion: '垂直拖拽 - 选择同一时间段内的连续时间'\r\n    };\r\n  }\r\n\r\n  // 跨列拖拽（列优先选择）\r\n  return {\r\n    type: 'diagonal',\r\n    isColumnPrimary: true,\r\n    suggestion: '跨列拖拽 - 列优先选择连续时间'\r\n  };\r\n}\r\n\r\n/**\r\n * 计算拖拽路径和选中的时间槽\r\n * @param startSlot 起始时间槽\r\n * @param endSlot 结束时间槽\r\n * @returns 拖拽路径信息\r\n */\r\nexport function calculateDragPath(startSlot: number, endSlot: number): DragPath {\r\n  const direction = detectDragDirection(startSlot, endSlot);\r\n  const selectedSlots = getSelectedTimeSlots(startSlot, endSlot);\r\n  const timeRange = calculateTimeRange(selectedSlots);\r\n\r\n  return {\r\n    startSlot,\r\n    endSlot,\r\n    direction,\r\n    selectedSlots,\r\n    timeRange\r\n  };\r\n}\r\n\r\n/**\r\n * 获取选中的时间槽数组（列优先选择算法）\r\n * @param startSlot 起始时间槽\r\n * @param endSlot 结束时间槽\r\n * @returns 选中的时间槽数组\r\n */\r\nexport function getSelectedTimeSlots(startSlot: number, endSlot: number): number[] {\r\n  const direction = detectDragDirection(startSlot, endSlot);\r\n\r\n  if (direction.type === 'vertical') {\r\n    return getVerticalSelection(startSlot, endSlot);\r\n  } else {\r\n    return getColumnPrioritySelection(startSlot, endSlot);\r\n  }\r\n}\r\n\r\n/**\r\n * 垂直拖拽选择（同一列内）\r\n * @param startSlot 起始时间槽\r\n * @param endSlot 结束时间槽\r\n * @returns 同一列内的时间槽数组\r\n */\r\nfunction getVerticalSelection(startSlot: number, endSlot: number): number[] {\r\n  // 新布局：6列×8行，每列代表4小时时间段\r\n  // 同一列内的timeSlot：col * 8 + row (row: 0-7)\r\n  const column = Math.floor(startSlot / 8);\r\n  const startRow = startSlot % 8;\r\n  const endRow = endSlot % 8;\r\n\r\n  const minRow = Math.min(startRow, endRow);\r\n  const maxRow = Math.max(startRow, endRow);\r\n\r\n  const selectedSlots: number[] = [];\r\n\r\n  for (let row = minRow; row <= maxRow; row++) {\r\n    const timeSlot = column * 8 + row;\r\n    if (timeSlot < 48) { // 确保不超出48个时间槽的范围\r\n      selectedSlots.push(timeSlot);\r\n    }\r\n  }\r\n\r\n  return selectedSlots;\r\n}\r\n\r\n/**\r\n * 跨列拖拽选择算法（保持真实时间连续性）\r\n * @param startSlot 起始时间槽\r\n * @param endSlot 结束时间槽\r\n * @returns 真实时间连续的时间槽数组\r\n */\r\nfunction getColumnPrioritySelection(startSlot: number, endSlot: number): number[] {\r\n  // 将timeSlot转换为实际时间\r\n  const startTime = timeSlotToTime(startSlot);\r\n  const endTime = timeSlotToTime(endSlot);\r\n\r\n  // 确定时间范围的起始和结束\r\n  const earlierTime = compareTime(startTime, endTime) <= 0 ? startTime : endTime;\r\n  const laterTime = compareTime(startTime, endTime) <= 0 ? endTime : startTime;\r\n\r\n  // 生成从起始时间到结束时间的所有30分钟间隔\r\n  const selectedSlots: number[] = [];\r\n  let currentHour = earlierTime.hour;\r\n  let currentMinute = earlierTime.minute;\r\n\r\n  while (compareTime({ hour: currentHour, minute: currentMinute }, laterTime) <= 0) {\r\n    const timeSlot = timeToTimeSlot(currentHour, currentMinute);\r\n    selectedSlots.push(timeSlot);\r\n\r\n    // 移动到下一个30分钟间隔\r\n    currentMinute += 30;\r\n    if (currentMinute >= 60) {\r\n      currentMinute = 0;\r\n      currentHour++;\r\n    }\r\n\r\n    // 防止无限循环，确保不超过24小时\r\n    if (currentHour >= 24) {\r\n      break;\r\n    }\r\n  }\r\n\r\n  return selectedSlots;\r\n}\r\n\r\n/**\r\n * 将timeSlot转换为实际时间（小时:分钟）\r\n * @param timeSlot 时间槽索引 (0-47)\r\n * @returns {hour, minute} 时间对象\r\n */\r\nfunction timeSlotToTime(timeSlot: number): { hour: number; minute: number } {\r\n  const hour = Math.floor(timeSlot / 2);\r\n  const minute = (timeSlot % 2) * 30;\r\n  return { hour, minute };\r\n}\r\n\r\n/**\r\n * 将时间（小时:分钟）转换为timeSlot\r\n * @param hour 小时 (0-23)\r\n * @param minute 分钟 (0, 30)\r\n * @returns timeSlot索引\r\n */\r\nfunction timeToTimeSlot(hour: number, minute: number): number {\r\n  return hour * 2 + (minute === 30 ? 1 : 0);\r\n}\r\n\r\n/**\r\n * 比较两个时间的大小\r\n * @param time1 时间1\r\n * @param time2 时间2\r\n * @returns -1: time1 < time2, 0: time1 = time2, 1: time1 > time2\r\n */\r\nfunction compareTime(time1: { hour: number; minute: number }, time2: { hour: number; minute: number }): number {\r\n  if (time1.hour !== time2.hour) {\r\n    return time1.hour - time2.hour;\r\n  }\r\n  return time1.minute - time2.minute;\r\n}\r\n\r\n/**\r\n * 计算时间范围\r\n * @param timeSlots 时间槽数组\r\n * @returns 时间范围信息\r\n */\r\nexport function calculateTimeRange(timeSlots: number[]): {\r\n  start: string;\r\n  end: string;\r\n  duration: number;\r\n} {\r\n  if (timeSlots.length === 0) {\r\n    return { start: '', end: '', duration: 0 };\r\n  }\r\n\r\n  const sortedSlots = [...timeSlots].sort((a, b) => a - b);\r\n  const startSlot = sortedSlots[0];\r\n  const endSlot = sortedSlots[sortedSlots.length - 1];\r\n\r\n  // 计算开始时间\r\n  const startHour = Math.floor(startSlot / 2);\r\n  const startMinute = (startSlot % 2) * 30;\r\n  const startTime = `${startHour.toString().padStart(2, '0')}:${startMinute.toString().padStart(2, '0')}`;\r\n\r\n  // 计算结束时间（下一个时间槽的开始时间）\r\n  const endSlotNext = endSlot + 1;\r\n  const endHour = Math.floor(endSlotNext / 2);\r\n  const endMinute = (endSlotNext % 2) * 30;\r\n  const endTime = `${endHour.toString().padStart(2, '0')}:${endMinute.toString().padStart(2, '0')}`;\r\n\r\n  // 计算持续时间（分钟）\r\n  const duration = timeSlots.length * 30;\r\n\r\n  return {\r\n    start: startTime,\r\n    end: endTime,\r\n    duration\r\n  };\r\n}\r\n\r\n/**\r\n * 检查拖拽是否为推荐的列优先方式\r\n * @param startSlot 起始时间槽\r\n * @param endSlot 结束时间槽\r\n * @returns 是否为推荐方式（现在所有拖拽都是列优先）\r\n */\r\nexport function isRecommendedDragPattern(startSlot: number, endSlot: number): boolean {\r\n  // 所有拖拽都是列优先，因为我们移除了水平拖拽\r\n  return true;\r\n}\r\n\r\n/**\r\n * 获取拖拽建议文本\r\n * @param startSlot 起始时间槽\r\n * @param endSlot 结束时间槽\r\n * @returns 建议文本\r\n */\r\nexport function getDragSuggestion(startSlot: number, endSlot: number): string {\r\n  const direction = detectDragDirection(startSlot, endSlot);\r\n  return direction.suggestion || '';\r\n}\r\n\r\n/**\r\n * 计算网格位置信息\r\n * @param timeSlot 时间槽\r\n * @returns 网格位置\r\n */\r\nexport function getGridPosition(timeSlot: number): { row: number; col: number } {\r\n  return {\r\n    row: Math.floor(timeSlot / 6),\r\n    col: timeSlot % 6\r\n  };\r\n}\r\n\r\n/**\r\n * 检查两个时间槽是否在同一列\r\n * @param slot1 时间槽1\r\n * @param slot2 时间槽2\r\n * @returns 是否在同一列\r\n */\r\nexport function isSameColumn(slot1: number, slot2: number): boolean {\r\n  return (slot1 % 6) === (slot2 % 6);\r\n}\r\n\r\n/**\r\n * 检查两个时间槽是否在同一行\r\n * @param slot1 时间槽1\r\n * @param slot2 时间槽2\r\n * @returns 是否在同一行\r\n */\r\nexport function isSameRow(slot1: number, slot2: number): boolean {\r\n  return Math.floor(slot1 / 6) === Math.floor(slot2 / 6);\r\n}\r\n\r\n/**\r\n * 获取列优先选择的描述文本\r\n * @param startSlot 起始时间槽\r\n * @param endSlot 结束时间槽\r\n * @returns 描述文本\r\n */\r\nexport function getSelectionDescription(startSlot: number, endSlot: number): string {\r\n  const direction = detectDragDirection(startSlot, endSlot);\r\n  const selectedSlots = getSelectedTimeSlots(startSlot, endSlot);\r\n\r\n  if (direction.type === 'vertical') {\r\n    const column = Math.floor(startSlot / 8);\r\n    const timeSegments = ['深夜', '黎明', '上午', '下午', '傍晚', '夜晚'];\r\n    const segmentName = timeSegments[column] || `第${column + 1}列`;\r\n    return `${segmentName}时间段内的垂直选择`;\r\n  } else {\r\n    const startCol = Math.floor(startSlot / 8);\r\n    const endCol = Math.floor(endSlot / 8);\r\n    const colCount = Math.abs(endCol - startCol) + 1;\r\n    return `跨${colCount}个时间段的列优先选择`;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;AA0BM,SAAS,oBAAoB,SAAiB,EAAE,WAAmB;IACxE,uBAAuB;IACvB,+CAA+C;IAC/C,MAAM,WAAW,KAAK,KAAK,CAAC,YAAY;IACxC,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAE5C,aAAa;IACb,IAAI,aAAa,YAAY;QAC3B,OAAO;YACL,MAAM;YACN,iBAAiB;YACjB,YAAY;QACd;IACF;IAEA,cAAc;IACd,OAAO;QACL,MAAM;QACN,iBAAiB;QACjB,YAAY;IACd;AACF;AAQO,SAAS,kBAAkB,SAAiB,EAAE,OAAe;IAClE,MAAM,YAAY,oBAAoB,WAAW;IACjD,MAAM,gBAAgB,qBAAqB,WAAW;IACtD,MAAM,YAAY,mBAAmB;IAErC,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;AAQO,SAAS,qBAAqB,SAAiB,EAAE,OAAe;IACrE,MAAM,YAAY,oBAAoB,WAAW;IAEjD,IAAI,UAAU,IAAI,KAAK,YAAY;QACjC,OAAO,qBAAqB,WAAW;IACzC,OAAO;QACL,OAAO,2BAA2B,WAAW;IAC/C;AACF;AAEA;;;;;CAKC,GACD,SAAS,qBAAqB,SAAiB,EAAE,OAAe;IAC9D,uBAAuB;IACvB,yCAAyC;IACzC,MAAM,SAAS,KAAK,KAAK,CAAC,YAAY;IACtC,MAAM,WAAW,YAAY;IAC7B,MAAM,SAAS,UAAU;IAEzB,MAAM,SAAS,KAAK,GAAG,CAAC,UAAU;IAClC,MAAM,SAAS,KAAK,GAAG,CAAC,UAAU;IAElC,MAAM,gBAA0B,EAAE;IAElC,IAAK,IAAI,MAAM,QAAQ,OAAO,QAAQ,MAAO;QAC3C,MAAM,WAAW,SAAS,IAAI;QAC9B,IAAI,WAAW,IAAI;YACjB,cAAc,IAAI,CAAC;QACrB;IACF;IAEA,OAAO;AACT;AAEA;;;;;CAKC,GACD,SAAS,2BAA2B,SAAiB,EAAE,OAAe;IACpE,mBAAmB;IACnB,MAAM,YAAY,eAAe;IACjC,MAAM,UAAU,eAAe;IAE/B,eAAe;IACf,MAAM,cAAc,YAAY,WAAW,YAAY,IAAI,YAAY;IACvE,MAAM,YAAY,YAAY,WAAW,YAAY,IAAI,UAAU;IAEnE,wBAAwB;IACxB,MAAM,gBAA0B,EAAE;IAClC,IAAI,cAAc,YAAY,IAAI;IAClC,IAAI,gBAAgB,YAAY,MAAM;IAEtC,MAAO,YAAY;QAAE,MAAM;QAAa,QAAQ;IAAc,GAAG,cAAc,EAAG;QAChF,MAAM,WAAW,eAAe,aAAa;QAC7C,cAAc,IAAI,CAAC;QAEnB,eAAe;QACf,iBAAiB;QACjB,IAAI,iBAAiB,IAAI;YACvB,gBAAgB;YAChB;QACF;QAEA,mBAAmB;QACnB,IAAI,eAAe,IAAI;YACrB;QACF;IACF;IAEA,OAAO;AACT;AAEA;;;;CAIC,GACD,SAAS,eAAe,QAAgB;IACtC,MAAM,OAAO,KAAK,KAAK,CAAC,WAAW;IACnC,MAAM,SAAS,AAAC,WAAW,IAAK;IAChC,OAAO;QAAE;QAAM;IAAO;AACxB;AAEA;;;;;CAKC,GACD,SAAS,eAAe,IAAY,EAAE,MAAc;IAClD,OAAO,OAAO,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC;AAC1C;AAEA;;;;;CAKC,GACD,SAAS,YAAY,KAAuC,EAAE,KAAuC;IACnG,IAAI,MAAM,IAAI,KAAK,MAAM,IAAI,EAAE;QAC7B,OAAO,MAAM,IAAI,GAAG,MAAM,IAAI;IAChC;IACA,OAAO,MAAM,MAAM,GAAG,MAAM,MAAM;AACpC;AAOO,SAAS,mBAAmB,SAAmB;IAKpD,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,OAAO;YAAE,OAAO;YAAI,KAAK;YAAI,UAAU;QAAE;IAC3C;IAEA,MAAM,cAAc;WAAI;KAAU,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;IACtD,MAAM,YAAY,WAAW,CAAC,EAAE;IAChC,MAAM,UAAU,WAAW,CAAC,YAAY,MAAM,GAAG,EAAE;IAEnD,SAAS;IACT,MAAM,YAAY,KAAK,KAAK,CAAC,YAAY;IACzC,MAAM,cAAc,AAAC,YAAY,IAAK;IACtC,MAAM,YAAY,GAAG,UAAU,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,YAAY,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IAEvG,sBAAsB;IACtB,MAAM,cAAc,UAAU;IAC9B,MAAM,UAAU,KAAK,KAAK,CAAC,cAAc;IACzC,MAAM,YAAY,AAAC,cAAc,IAAK;IACtC,MAAM,UAAU,GAAG,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,UAAU,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IAEjG,aAAa;IACb,MAAM,WAAW,UAAU,MAAM,GAAG;IAEpC,OAAO;QACL,OAAO;QACP,KAAK;QACL;IACF;AACF;AAQO,SAAS,yBAAyB,SAAiB,EAAE,OAAe;IACzE,wBAAwB;IACxB,OAAO;AACT;AAQO,SAAS,kBAAkB,SAAiB,EAAE,OAAe;IAClE,MAAM,YAAY,oBAAoB,WAAW;IACjD,OAAO,UAAU,UAAU,IAAI;AACjC;AAOO,SAAS,gBAAgB,QAAgB;IAC9C,OAAO;QACL,KAAK,KAAK,KAAK,CAAC,WAAW;QAC3B,KAAK,WAAW;IAClB;AACF;AAQO,SAAS,aAAa,KAAa,EAAE,KAAa;IACvD,OAAO,AAAC,QAAQ,MAAQ,QAAQ;AAClC;AAQO,SAAS,UAAU,KAAa,EAAE,KAAa;IACpD,OAAO,KAAK,KAAK,CAAC,QAAQ,OAAO,KAAK,KAAK,CAAC,QAAQ;AACtD;AAQO,SAAS,wBAAwB,SAAiB,EAAE,OAAe;IACxE,MAAM,YAAY,oBAAoB,WAAW;IACjD,MAAM,gBAAgB,qBAAqB,WAAW;IAEtD,IAAI,UAAU,IAAI,KAAK,YAAY;QACjC,MAAM,SAAS,KAAK,KAAK,CAAC,YAAY;QACtC,MAAM,eAAe;YAAC;YAAM;YAAM;YAAM;YAAM;YAAM;SAAK;QACzD,MAAM,cAAc,YAAY,CAAC,OAAO,IAAI,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC;QAC7D,OAAO,GAAG,YAAY,SAAS,CAAC;IAClC,OAAO;QACL,MAAM,WAAW,KAAK,KAAK,CAAC,YAAY;QACxC,MAAM,SAAS,KAAK,KAAK,CAAC,UAAU;QACpC,MAAM,WAAW,KAAK,GAAG,CAAC,SAAS,YAAY;QAC/C,OAAO,CAAC,CAAC,EAAE,SAAS,UAAU,CAAC;IACjC;AACF", "debugId": null}}, {"offset": {"line": 2905, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/utils/colorUtils.ts"], "sourcesContent": ["/**\r\n * 颜色工具函数\r\n * 用于处理颜色相关的计算和转换\r\n */\r\n\r\n/**\r\n * 判断颜色是否为浅色（用于调整图标颜色和选中边框颜色）\r\n * @param color 十六进制颜色值，如 \"#3B82F6\"\r\n * @returns 是否为浅色\r\n */\r\nexport function isLightColor(color: string): boolean {\r\n  // 移除 # 符号\r\n  const hex = color.replace('#', '');\r\n  \r\n  // 转换为 RGB\r\n  const r = parseInt(hex.substr(0, 2), 16);\r\n  const g = parseInt(hex.substr(2, 2), 16);\r\n  const b = parseInt(hex.substr(4, 2), 16);\r\n  \r\n  // 计算亮度 (使用相对亮度公式)\r\n  const brightness = (r * 299 + g * 587 + b * 114) / 1000;\r\n  \r\n  // 亮度大于 128 认为是浅色\r\n  return brightness > 128;\r\n}\r\n\r\n/**\r\n * 根据背景色获取最佳对比度的边框颜色\r\n * @param backgroundColor 背景颜色\r\n * @returns 边框颜色\r\n */\r\nexport function getContrastBorderColor(backgroundColor: string): string {\r\n  return isLightColor(backgroundColor) ? '#1f2937' : '#ffffff';\r\n}\r\n\r\n/**\r\n * 获取智能选中边框样式\r\n * @param isSelected 是否选中\r\n * @param isEditable 是否可编辑\r\n * @param activityColor 活动颜色（可选）\r\n * @returns 边框样式对象\r\n */\r\nexport function getSmartSelectionBorder(\r\n  isSelected: boolean,\r\n  isEditable: boolean,\r\n  activityColor?: string\r\n): {\r\n  borderColor: string;\r\n  borderWidth: string;\r\n  boxShadow: string;\r\n} {\r\n  if (!isSelected || !isEditable) {\r\n    return {\r\n      borderColor: 'transparent',\r\n      borderWidth: '2px',\r\n      boxShadow: 'none'\r\n    };\r\n  }\r\n\r\n  // 如果有活动颜色，使用智能对比边框\r\n  if (activityColor) {\r\n    const borderColor = getContrastBorderColor(activityColor);\r\n    return {\r\n      borderColor,\r\n      borderWidth: '3px',\r\n      boxShadow: `0 0 0 1px ${borderColor}20` // 20% 透明度的外阴影\r\n    };\r\n  }\r\n\r\n  // 空白时间块使用主题色边框\r\n  return {\r\n    borderColor: 'var(--primary-500)',\r\n    borderWidth: '3px',\r\n    boxShadow: '0 0 0 1px rgba(48, 128, 255, 0.2)' // 主题色外阴影\r\n  };\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;;CAIC;;;;;AACM,SAAS,aAAa,KAAa;IACxC,UAAU;IACV,MAAM,MAAM,MAAM,OAAO,CAAC,KAAK;IAE/B,UAAU;IACV,MAAM,IAAI,SAAS,IAAI,MAAM,CAAC,GAAG,IAAI;IACrC,MAAM,IAAI,SAAS,IAAI,MAAM,CAAC,GAAG,IAAI;IACrC,MAAM,IAAI,SAAS,IAAI,MAAM,CAAC,GAAG,IAAI;IAErC,kBAAkB;IAClB,MAAM,aAAa,CAAC,IAAI,MAAM,IAAI,MAAM,IAAI,GAAG,IAAI;IAEnD,iBAAiB;IACjB,OAAO,aAAa;AACtB;AAOO,SAAS,uBAAuB,eAAuB;IAC5D,OAAO,aAAa,mBAAmB,YAAY;AACrD;AASO,SAAS,wBACd,UAAmB,EACnB,UAAmB,EACnB,aAAsB;IAMtB,IAAI,CAAC,cAAc,CAAC,YAAY;QAC9B,OAAO;YACL,aAAa;YACb,aAAa;YACb,WAAW;QACb;IACF;IAEA,mBAAmB;IACnB,IAAI,eAAe;QACjB,MAAM,cAAc,uBAAuB;QAC3C,OAAO;YACL;YACA,aAAa;YACb,WAAW,CAAC,UAAU,EAAE,YAAY,EAAE,CAAC,CAAC,cAAc;QACxD;IACF;IAEA,eAAe;IACf,OAAO;QACL,aAAa;QACb,aAAa;QACb,WAAW,oCAAoC,SAAS;IAC1D;AACF", "debugId": null}}, {"offset": {"line": 2960, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/components/TimeBlock.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useMemo } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { useAppStore } from '@/stores/useAppStore';\r\nimport { TimeBlock as TimeBlockType } from '@/types';\r\nimport { isTimeSlotEditable } from '@/utils/timeUtils';\r\nimport { getSmartSelectionBorder } from '@/utils/colorUtils';\r\nimport { useIsClient } from '@/hooks/useIsClient';\r\nimport * as LucideIcons from 'lucide-react';\r\n\r\ninterface TimeBlockProps {\r\n  block: TimeBlockType;\r\n  isSelected: boolean;\r\n  onClick: (event: React.MouseEvent) => void;\r\n  onMouseDown: (event: React.MouseEvent) => void;\r\n  onMouseEnter: () => void;\r\n}\r\n\r\nexport function TimeBlock({\r\n  block,\r\n  isSelected,\r\n  onClick,\r\n  onMouseDown,\r\n  onMouseEnter\r\n}: TimeBlockProps) {\r\n  const { getActivityById, currentTimeSlot } = useAppStore();\r\n  const [showTooltip, setShowTooltip] = useState(false);\r\n  const isClient = useIsClient();\r\n\r\n  // 记忆化计算是否可编辑（响应式，依赖currentTimeSlot状态）\r\n  const isEditable = useMemo(() => {\r\n    return isTimeSlotEditable(block.date, block.timeSlot);\r\n  }, [block.date, block.timeSlot, currentTimeSlot]);\r\n\r\n  // 只有可编辑的时间槽才显示活动，未来时间的活动数据不应该显示\r\n  const activity = (block.activityId && isEditable) ? getActivityById(block.activityId) : null;\r\n  \r\n  // 获取图标组件\r\n  const getIconComponent = (iconName?: string) => {\r\n    if (!iconName) return null;\r\n    \r\n    const IconComponent = (LucideIcons as any)[iconName];\r\n    return IconComponent ? <IconComponent size={16} /> : null;\r\n  };\r\n\r\n  // 记忆化计算样式\r\n  const blockStyle = useMemo(() => {\r\n    const baseStyle = {\r\n      opacity: isEditable ? 1 : 0.4, // 不可编辑时降低透明度\r\n      cursor: isEditable ? 'pointer' : 'not-allowed', // 不可编辑时显示禁用光标\r\n    };\r\n\r\n    // 获取智能选中边框样式\r\n    const selectionBorder = getSmartSelectionBorder(isSelected, isEditable, activity?.color);\r\n\r\n    if (activity) {\r\n      return {\r\n        ...baseStyle,\r\n        backgroundColor: activity.color,\r\n        borderColor: selectionBorder.borderColor !== 'transparent' ? selectionBorder.borderColor : activity.color,\r\n        borderWidth: selectionBorder.borderWidth,\r\n        boxShadow: selectionBorder.boxShadow !== 'none' ? selectionBorder.boxShadow : (isEditable ? 'var(--shadow-sm)' : 'none'),\r\n      };\r\n    }\r\n\r\n    if (isSelected && isEditable) {\r\n      return {\r\n        ...baseStyle,\r\n        backgroundColor: 'var(--primary-500)',\r\n        borderColor: selectionBorder.borderColor,\r\n        borderWidth: selectionBorder.borderWidth,\r\n        boxShadow: selectionBorder.boxShadow,\r\n      };\r\n    }\r\n\r\n    return {\r\n      ...baseStyle,\r\n      backgroundColor: isEditable ? 'var(--neutral-50)' : 'var(--neutral-100)',\r\n      borderColor: isEditable ? 'var(--neutral-200)' : 'var(--neutral-300)',\r\n      borderWidth: '2px',\r\n      boxShadow: isEditable ? 'var(--shadow-sm)' : 'none',\r\n    };\r\n  }, [isEditable, activity, isSelected]);\r\n  const isActive = !!activity;\r\n  const textColor = isActive || isSelected ? 'white' : 'var(--neutral-400)';\r\n\r\n  return (\r\n    <motion.div\r\n      className=\"relative flex items-center justify-center\"\r\n      style={{\r\n        height: '60px',\r\n        border: `${blockStyle.borderWidth || '2px'} solid`,\r\n        borderRadius: 'var(--radius-lg)',\r\n        transition: 'all var(--duration-fast) var(--ease-out)',\r\n        ...blockStyle,\r\n      }}\r\n      whileHover={isEditable ? {\r\n        scale: 1.02,\r\n        y: -1,\r\n        boxShadow: 'var(--shadow-md)'\r\n      } : {}}\r\n      whileTap={isEditable ? { scale: 0.98 } : {}}\r\n      tabIndex={isClient && isEditable ? 0 : undefined}\r\n      onClick={isEditable ? onClick : undefined}\r\n      onMouseDown={isEditable ? onMouseDown : undefined}\r\n      onMouseEnter={() => {\r\n        if (isEditable) {\r\n          onMouseEnter();\r\n        } else {\r\n          setShowTooltip(true);\r\n        }\r\n      }}\r\n      onMouseLeave={() => {\r\n        setShowTooltip(false);\r\n      }}\r\n      initial={{ opacity: 0, scale: 0.9 }}\r\n      animate={{\r\n        scale: 1,\r\n        // 添加平滑的可编辑状态过渡\r\n        ...blockStyle,\r\n        opacity: 1\r\n      }}\r\n      transition={{\r\n        duration: 0.15,\r\n        ease: [0.4, 0, 0.2, 1],\r\n        // 为透明度和颜色变化添加更长的过渡时间\r\n        opacity: { duration: 0.3 },\r\n        backgroundColor: { duration: 0.3 },\r\n        borderColor: { duration: 0.3 }\r\n      }}\r\n    >\r\n      {/* 活动内容 */}\r\n      {activity && (\r\n        <motion.div\r\n          className=\"flex flex-col items-center justify-center text-center\"\r\n          style={{ padding: '0 var(--spacing-1)' }}\r\n          initial={{ opacity: 0, scale: 0.8 }}\r\n          animate={{ opacity: 1, scale: 1 }}\r\n          transition={{ delay: 0.1, duration: 0.2 }}\r\n        >\r\n          {/* 图标 */}\r\n          {activity.icon && (\r\n            <div style={{\r\n              color: textColor,\r\n              marginBottom: 'var(--spacing-1)'\r\n            }}>\r\n              {getIconComponent(activity.icon)}\r\n            </div>\r\n          )}\r\n\r\n          {/* 活动名称 */}\r\n          <div style={{\r\n            fontSize: 'var(--font-size-xs)',\r\n            fontWeight: 'var(--font-weight-medium)',\r\n            color: textColor,\r\n            overflow: 'hidden',\r\n            textOverflow: 'ellipsis',\r\n            whiteSpace: 'nowrap',\r\n            maxWidth: '100%'\r\n          }}>\r\n            {activity.name}\r\n          </div>\r\n        </motion.div>\r\n      )}\r\n\r\n      {/* 选中状态指示器已移除，现在使用智能边框样式 */}\r\n\r\n      {/* 时间显示（仅在hover时显示） */}\r\n      <div\r\n        className=\"absolute pointer-events-none\"\r\n        style={{\r\n          bottom: '-24px',\r\n          left: '50%',\r\n          transform: 'translateX(-50%)',\r\n          opacity: 0,\r\n          transition: 'opacity var(--duration-fast) var(--ease-out)',\r\n          zIndex: 10\r\n        }}\r\n        onMouseEnter={(e) => {\r\n          e.currentTarget.style.opacity = '1';\r\n        }}\r\n        onMouseLeave={(e) => {\r\n          e.currentTarget.style.opacity = '0';\r\n        }}\r\n      >\r\n        <div style={{\r\n          background: 'var(--neutral-800)',\r\n          color: 'white',\r\n          fontSize: 'var(--font-size-xs)',\r\n          padding: 'var(--spacing-1) var(--spacing-2)',\r\n          borderRadius: 'var(--radius-md)',\r\n          boxShadow: 'var(--shadow-lg)',\r\n          whiteSpace: 'nowrap'\r\n        }}>\r\n          {block.startTime} - {block.endTime}\r\n        </div>\r\n      </div>\r\n\r\n      {/* 活动详情提示已移除，现在使用右侧固定信息面板 */}\r\n\r\n      {/* 不可编辑状态的提示 */}\r\n      {!isEditable && showTooltip && (\r\n        <motion.div\r\n          className=\"absolute pointer-events-none\"\r\n          style={{\r\n            bottom: '-36px',\r\n            left: '50%',\r\n            transform: 'translateX(-50%)',\r\n            zIndex: 20\r\n          }}\r\n          initial={{ opacity: 0, y: -5 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          exit={{ opacity: 0, y: -5 }}\r\n          transition={{ duration: 0.2 }}\r\n        >\r\n          <div style={{\r\n            background: 'var(--neutral-800)',\r\n            color: 'white',\r\n            fontSize: 'var(--font-size-xs)',\r\n            padding: 'var(--spacing-2) var(--spacing-3)',\r\n            borderRadius: 'var(--radius-md)',\r\n            boxShadow: 'var(--shadow-lg)',\r\n            whiteSpace: 'nowrap'\r\n          }}>\r\n            只能记录已经过去的时间\r\n          </div>\r\n        </motion.div>\r\n      )}\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AATA;;;;;;;;;AAmBO,SAAS,UAAU,EACxB,KAAK,EACL,UAAU,EACV,OAAO,EACP,WAAW,EACX,YAAY,EACG;IACf,MAAM,EAAE,eAAe,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,WAAW,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IAE3B,sCAAsC;IACtC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACzB,OAAO,CAAA,GAAA,yHAAA,CAAA,qBAAkB,AAAD,EAAE,MAAM,IAAI,EAAE,MAAM,QAAQ;IACtD,GAAG;QAAC,MAAM,IAAI;QAAE,MAAM,QAAQ;QAAE;KAAgB;IAEhD,gCAAgC;IAChC,MAAM,WAAW,AAAC,MAAM,UAAU,IAAI,aAAc,gBAAgB,MAAM,UAAU,IAAI;IAExF,SAAS;IACT,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,UAAU,OAAO;QAEtB,MAAM,gBAAgB,AAAC,iKAAmB,CAAC,SAAS;QACpD,OAAO,8BAAgB,8OAAC;YAAc,MAAM;;;;;mBAAS;IACvD;IAEA,UAAU;IACV,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACzB,MAAM,YAAY;YAChB,SAAS,aAAa,IAAI;YAC1B,QAAQ,aAAa,YAAY;QACnC;QAEA,aAAa;QACb,MAAM,kBAAkB,CAAA,GAAA,0HAAA,CAAA,0BAAuB,AAAD,EAAE,YAAY,YAAY,UAAU;QAElF,IAAI,UAAU;YACZ,OAAO;gBACL,GAAG,SAAS;gBACZ,iBAAiB,SAAS,KAAK;gBAC/B,aAAa,gBAAgB,WAAW,KAAK,gBAAgB,gBAAgB,WAAW,GAAG,SAAS,KAAK;gBACzG,aAAa,gBAAgB,WAAW;gBACxC,WAAW,gBAAgB,SAAS,KAAK,SAAS,gBAAgB,SAAS,GAAI,aAAa,qBAAqB;YACnH;QACF;QAEA,IAAI,cAAc,YAAY;YAC5B,OAAO;gBACL,GAAG,SAAS;gBACZ,iBAAiB;gBACjB,aAAa,gBAAgB,WAAW;gBACxC,aAAa,gBAAgB,WAAW;gBACxC,WAAW,gBAAgB,SAAS;YACtC;QACF;QAEA,OAAO;YACL,GAAG,SAAS;YACZ,iBAAiB,aAAa,sBAAsB;YACpD,aAAa,aAAa,uBAAuB;YACjD,aAAa;YACb,WAAW,aAAa,qBAAqB;QAC/C;IACF,GAAG;QAAC;QAAY;QAAU;KAAW;IACrC,MAAM,WAAW,CAAC,CAAC;IACnB,MAAM,YAAY,YAAY,aAAa,UAAU;IAErD,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,OAAO;YACL,QAAQ;YACR,QAAQ,GAAG,WAAW,WAAW,IAAI,MAAM,MAAM,CAAC;YAClD,cAAc;YACd,YAAY;YACZ,GAAG,UAAU;QACf;QACA,YAAY,aAAa;YACvB,OAAO;YACP,GAAG,CAAC;YACJ,WAAW;QACb,IAAI,CAAC;QACL,UAAU,aAAa;YAAE,OAAO;QAAK,IAAI,CAAC;QAC1C,UAAU,YAAY,aAAa,IAAI;QACvC,SAAS,aAAa,UAAU;QAChC,aAAa,aAAa,cAAc;QACxC,cAAc;YACZ,IAAI,YAAY;gBACd;YACF,OAAO;gBACL,eAAe;YACjB;QACF;QACA,cAAc;YACZ,eAAe;QACjB;QACA,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,SAAS;YACP,OAAO;YACP,eAAe;YACf,GAAG,UAAU;YACb,SAAS;QACX;QACA,YAAY;YACV,UAAU;YACV,MAAM;gBAAC;gBAAK;gBAAG;gBAAK;aAAE;YACtB,qBAAqB;YACrB,SAAS;gBAAE,UAAU;YAAI;YACzB,iBAAiB;gBAAE,UAAU;YAAI;YACjC,aAAa;gBAAE,UAAU;YAAI;QAC/B;;YAGC,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBAAE,SAAS;gBAAqB;gBACvC,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAI;gBAClC,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAE;gBAChC,YAAY;oBAAE,OAAO;oBAAK,UAAU;gBAAI;;oBAGvC,SAAS,IAAI,kBACZ,8OAAC;wBAAI,OAAO;4BACV,OAAO;4BACP,cAAc;wBAChB;kCACG,iBAAiB,SAAS,IAAI;;;;;;kCAKnC,8OAAC;wBAAI,OAAO;4BACV,UAAU;4BACV,YAAY;4BACZ,OAAO;4BACP,UAAU;4BACV,cAAc;4BACd,YAAY;4BACZ,UAAU;wBACZ;kCACG,SAAS,IAAI;;;;;;;;;;;;0BAQpB,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,QAAQ;oBACR,MAAM;oBACN,WAAW;oBACX,SAAS;oBACT,YAAY;oBACZ,QAAQ;gBACV;gBACA,cAAc,CAAC;oBACb,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;gBAClC;gBACA,cAAc,CAAC;oBACb,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;gBAClC;0BAEA,cAAA,8OAAC;oBAAI,OAAO;wBACV,YAAY;wBACZ,OAAO;wBACP,UAAU;wBACV,SAAS;wBACT,cAAc;wBACd,WAAW;wBACX,YAAY;oBACd;;wBACG,MAAM,SAAS;wBAAC;wBAAI,MAAM,OAAO;;;;;;;;;;;;YAOrC,CAAC,cAAc,6BACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBACL,QAAQ;oBACR,MAAM;oBACN,WAAW;oBACX,QAAQ;gBACV;gBACA,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAE;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,MAAM;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAE;gBAC1B,YAAY;oBAAE,UAAU;gBAAI;0BAE5B,cAAA,8OAAC;oBAAI,OAAO;wBACV,YAAY;wBACZ,OAAO;wBACP,UAAU;wBACV,SAAS;wBACT,cAAc;wBACd,WAAW;wBACX,YAAY;oBACd;8BAAG;;;;;;;;;;;;;;;;;AAOb", "debugId": null}}, {"offset": {"line": 3253, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/components/ActivityPalette.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { X, Plus, Trash2, Eraser } from 'lucide-react';\nimport { useAppStore } from '@/stores/useAppStore';\nimport * as LucideIcons from 'lucide-react';\n\ninterface ActivityPaletteProps {\n  onActivitySelect: (activityId: string | null) => void;\n  onClose: () => void;\n  selectedBlocks: number[];\n}\n\nexport function ActivityPalette({ onActivitySelect, onClose, selectedBlocks }: ActivityPaletteProps) {\n  const { activities } = useAppStore();\n\n  // 获取图标组件\n  const getIconComponent = (iconName?: string) => {\n    if (!iconName) return null;\n\n    const IconComponent = (LucideIcons as any)[iconName];\n    return IconComponent ? <IconComponent size={18} /> : null;\n  };\n\n  // 处理活动选择\n  const handleActivityClick = (activityId: string) => {\n    onActivitySelect(activityId);\n  };\n\n  // 处理清除选择\n  const handleClearClick = () => {\n    onActivitySelect(null);\n  };\n\n  // 阻止事件冒泡\n  const handlePaletteClick = (event: React.MouseEvent) => {\n    event.stopPropagation();\n  };\n\n  // 添加ESC键监听和点击外部关闭\n  useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (event.key === 'Escape') {\n        onClose();\n      }\n    };\n\n    const handleClickOutside = (event: MouseEvent) => {\n      const target = event.target as Element;\n      // 检查点击是否在工具栏外部\n      if (!target.closest('[data-activity-palette]')) {\n        onClose();\n      }\n    };\n\n    document.addEventListener('keydown', handleKeyDown);\n    document.addEventListener('mousedown', handleClickOutside);\n\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, [onClose]);\n\n  return (\n    <AnimatePresence>\n      {/* 极简水平工具栏 - 定位在时间网格下方 */}\n      <motion.div\n        className=\"absolute left-0 right-0 z-50 mx-auto max-w-4xl px-4\"\n        style={{ top: '100%', marginTop: '20px' }} // 定位在时间网格下方\n        initial={{ y: 20, opacity: 0 }}\n        animate={{ y: 0, opacity: 1 }}\n        exit={{ y: 20, opacity: 0 }}\n        transition={{ type: \"spring\", stiffness: 400, damping: 25 }}\n        onClick={handlePaletteClick}\n        data-activity-palette\n      >\n        {/* 轻量化工具栏背景 */}\n        <div className=\"bg-white rounded-xl shadow-lg border border-gray-200 p-3\">\n          {/* 轻量化活动选择器 */}\n          <div className=\"flex flex-wrap items-center justify-center gap-3\">\n            {/* 活动列表 */}\n            {activities.map((activity) => (\n              <motion.button\n                key={activity.id}\n                onClick={() => handleActivityClick(activity.id)}\n                className=\"flex items-center gap-2 px-4 py-2.5 bg-gray-50 hover:bg-gray-100 border border-gray-200 hover:border-gray-300 rounded-lg transition-all duration-200 group shadow-sm hover:shadow-md\"\n                whileHover={{ scale: 1.02, y: -1 }}\n                whileTap={{ scale: 0.98 }}\n              >\n                {/* 颜色圆点 */}\n                <div\n                  className=\"w-3 h-3 rounded-full flex-shrink-0 ring-1 ring-white\"\n                  style={{ backgroundColor: activity.color }}\n                />\n\n                {/* 活动名称 */}\n                <span className=\"text-sm font-medium text-gray-700 group-hover:text-gray-900 whitespace-nowrap\">\n                  {activity.name}\n                </span>\n              </motion.button>\n            ))}\n\n\n\n            {/* 简化清除按钮 */}\n            <motion.button\n              onClick={handleClearClick}\n              className=\"flex items-center justify-center w-10 h-10 bg-gray-50 hover:bg-red-50 border border-gray-200 hover:border-red-300 rounded-lg transition-all duration-200 group shadow-sm hover:shadow-md\"\n              whileHover={{ scale: 1.02, y: -1 }}\n              whileTap={{ scale: 0.98 }}\n              title=\"清除选择\"\n            >\n              <Eraser size={16} className=\"text-gray-500 group-hover:text-red-500\" />\n            </motion.button>\n          </div>\n        </div>\n      </motion.div>\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAcO,SAAS,gBAAgB,EAAE,gBAAgB,EAAE,OAAO,EAAE,cAAc,EAAwB;IACjG,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IAEjC,SAAS;IACT,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,UAAU,OAAO;QAEtB,MAAM,gBAAgB,AAAC,iKAAmB,CAAC,SAAS;QACpD,OAAO,8BAAgB,8OAAC;YAAc,MAAM;;;;;mBAAS;IACvD;IAEA,SAAS;IACT,MAAM,sBAAsB,CAAC;QAC3B,iBAAiB;IACnB;IAEA,SAAS;IACT,MAAM,mBAAmB;QACvB,iBAAiB;IACnB;IAEA,SAAS;IACT,MAAM,qBAAqB,CAAC;QAC1B,MAAM,eAAe;IACvB;IAEA,kBAAkB;IAClB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,IAAI,MAAM,GAAG,KAAK,UAAU;gBAC1B;YACF;QACF;QAEA,MAAM,qBAAqB,CAAC;YAC1B,MAAM,SAAS,MAAM,MAAM;YAC3B,eAAe;YACf,IAAI,CAAC,OAAO,OAAO,CAAC,4BAA4B;gBAC9C;YACF;QACF;QAEA,SAAS,gBAAgB,CAAC,WAAW;QACrC,SAAS,gBAAgB,CAAC,aAAa;QAEvC,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;YACxC,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG;QAAC;KAAQ;IAEZ,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBAEd,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,OAAO;gBAAE,KAAK;gBAAQ,WAAW;YAAO;YACxC,SAAS;gBAAE,GAAG;gBAAI,SAAS;YAAE;YAC7B,SAAS;gBAAE,GAAG;gBAAG,SAAS;YAAE;YAC5B,MAAM;gBAAE,GAAG;gBAAI,SAAS;YAAE;YAC1B,YAAY;gBAAE,MAAM;gBAAU,WAAW;gBAAK,SAAS;YAAG;YAC1D,SAAS;YACT,uBAAqB;sBAGrB,cAAA,8OAAC;gBAAI,WAAU;0BAEb,cAAA,8OAAC;oBAAI,WAAU;;wBAEZ,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCAEZ,SAAS,IAAM,oBAAoB,SAAS,EAAE;gCAC9C,WAAU;gCACV,YAAY;oCAAE,OAAO;oCAAM,GAAG,CAAC;gCAAE;gCACjC,UAAU;oCAAE,OAAO;gCAAK;;kDAGxB,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB,SAAS,KAAK;wCAAC;;;;;;kDAI3C,8OAAC;wCAAK,WAAU;kDACb,SAAS,IAAI;;;;;;;+BAdX,SAAS,EAAE;;;;;sCAsBpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,SAAS;4BACT,WAAU;4BACV,YAAY;gCAAE,OAAO;gCAAM,GAAG,CAAC;4BAAE;4BACjC,UAAU;gCAAE,OAAO;4BAAK;4BACxB,OAAM;sCAEN,cAAA,8OAAC,sMAAA,CAAA,SAAM;gCAAC,MAAM;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1C", "debugId": null}}, {"offset": {"line": 3435, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/components/TimelineIndicator.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { useAppStore } from '@/stores/useAppStore';\r\nimport { formatTime } from '@/utils/timeUtils';\r\nimport { useIsClient } from '@/hooks/useIsClient';\r\n\r\nexport function TimelineIndicator() {\r\n  const { currentDate, currentTimeSlot, currentTime } = useAppStore();\r\n  const isClient = useIsClient();\r\n\r\n  // 只在客户端渲染，避免SSR hydration mismatch\r\n  if (!isClient) {\r\n    return null;\r\n  }\r\n\r\n  // 只在当天显示时间线指示器\r\n  const isToday = currentDate === currentTime.date;\r\n  \r\n  if (!isToday) {\r\n    return null;\r\n  }\r\n  \r\n  // 计算当前时间在网格中的精确位置\r\n  const calculateTimelinePosition = () => {\r\n    const { hour, minute } = currentTime;\r\n\r\n    // 计算当前时间对应的timeSlot (0-47)\r\n    const currentTimeSlot = hour * 2 + (minute >= 30 ? 1 : 0);\r\n\r\n    // 统一坐标系统：6列×8行，按列优先布局\r\n    // 第1列：00:00-04:00 (timeSlot 0-7), 第2列：04:00-08:00 (timeSlot 8-15)\r\n    // 第3列：08:00-12:00 (timeSlot 16-23), 第4列：12:00-16:00 (timeSlot 24-31)\r\n    // 第5列：16:00-20:00 (timeSlot 32-39), 第6列：20:00-24:00 (timeSlot 40-47)\r\n    // timeSlot转行列：row = timeSlot % 8, col = Math.floor(timeSlot / 8)\r\n    const row = currentTimeSlot % 8;\r\n    const column = Math.floor(currentTimeSlot / 8);\r\n\r\n    // 计算在当前时间槽内的精确位置 (0-1)\r\n    const minutesInSlot = minute % 30; // 在30分钟时间槽内的分钟数\r\n    const positionInSlot = minutesInSlot / 30; // 在时间槽内的位置比例\r\n\r\n    return {\r\n      row: Math.min(row, 7), // 确保不超出边界 (0-7)\r\n      column: Math.min(column, 5), // 确保不超出边界 (0-5)\r\n      positionInSlot: Math.min(positionInSlot, 1) // 在时间槽内的位置 (0-1)\r\n    };\r\n  };\r\n  \r\n  const position = calculateTimelinePosition();\r\n  \r\n  // 计算时间线的CSS位置\r\n  const getTimelineStyle = () => {\r\n    // 网格间距 - 更新为新的间距值\r\n    const gap = 8; // var(--spacing-2) = 8px\r\n\r\n    // 每个区块的宽度和高度 - 适配6列布局\r\n    const blockWidth = `calc((100% - ${gap * 5}px) / 6)`;\r\n    const blockHeight = 60; // 60px\r\n\r\n    // 计算左边距：到达指定列 + 列内的精确位置\r\n    const leftOffset = `calc(${position.column} * (${blockWidth} + ${gap}px) + ${position.positionInSlot} * ${blockWidth})`;\r\n\r\n    // 计算顶部位置：行位置 * (区块高度 + 间距)\r\n    const topOffset = position.row * (blockHeight + gap);\r\n\r\n    return {\r\n      left: leftOffset,\r\n      top: `${topOffset}px`,\r\n      height: `${blockHeight}px`\r\n    };\r\n  };\r\n  \r\n  const timelineStyle = getTimelineStyle();\r\n  const currentTimeString = `${currentTime.hour.toString().padStart(2, '0')}:${currentTime.minute.toString().padStart(2, '0')}`;\r\n  \r\n  return (\r\n    <motion.div\r\n      className=\"absolute pointer-events-none\"\r\n      style={{\r\n        ...timelineStyle,\r\n        width: '2px',\r\n        backgroundColor: 'var(--primary-500)',\r\n        borderRadius: '1px',\r\n        boxShadow: '0 0 8px rgba(59, 130, 246, 0.5)',\r\n        zIndex: 10\r\n      }}\r\n      initial={{ opacity: 0, scaleY: 0 }}\r\n      animate={{ opacity: 1, scaleY: 1 }}\r\n      transition={{\r\n        duration: 0.5,\r\n        ease: [0.4, 0, 0.2, 1]\r\n      }}\r\n    >\r\n      {/* 时间标签 */}\r\n      <motion.div\r\n        className=\"absolute\"\r\n        style={{\r\n          top: '-28px',\r\n          left: '50%',\r\n          transform: 'translateX(-50%)',\r\n          background: 'var(--primary-500)',\r\n          color: 'white',\r\n          fontSize: 'var(--font-size-xs)',\r\n          fontWeight: 'var(--font-weight-medium)',\r\n          padding: 'var(--spacing-1) var(--spacing-2)',\r\n          borderRadius: 'var(--radius-md)',\r\n          boxShadow: 'var(--shadow-md)',\r\n          whiteSpace: 'nowrap'\r\n        }}\r\n        initial={{ opacity: 0, y: 5 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{\r\n          duration: 0.3,\r\n          delay: 0.2,\r\n          ease: [0.4, 0, 0.2, 1]\r\n        }}\r\n      >\r\n        {currentTimeString}\r\n      </motion.div>\r\n      \r\n      {/* 顶部指示点 */}\r\n      <motion.div\r\n        className=\"absolute\"\r\n        style={{\r\n          top: '-4px',\r\n          left: '50%',\r\n          transform: 'translateX(-50%)',\r\n          width: '8px',\r\n          height: '8px',\r\n          backgroundColor: 'var(--primary-500)',\r\n          borderRadius: '50%',\r\n          border: '2px solid white',\r\n          boxShadow: 'var(--shadow-sm)'\r\n        }}\r\n        initial={{ scale: 0 }}\r\n        animate={{ scale: 1 }}\r\n        transition={{\r\n          duration: 0.3,\r\n          delay: 0.1,\r\n          ease: [0.68, -0.55, 0.265, 1.55]\r\n        }}\r\n      />\r\n      \r\n      {/* 底部指示点 */}\r\n      <motion.div\r\n        className=\"absolute\"\r\n        style={{\r\n          bottom: '-4px',\r\n          left: '50%',\r\n          transform: 'translateX(-50%)',\r\n          width: '8px',\r\n          height: '8px',\r\n          backgroundColor: 'var(--primary-500)',\r\n          borderRadius: '50%',\r\n          border: '2px solid white',\r\n          boxShadow: 'var(--shadow-sm)'\r\n        }}\r\n        initial={{ scale: 0 }}\r\n        animate={{ scale: 1 }}\r\n        transition={{\r\n          duration: 0.3,\r\n          delay: 0.15,\r\n          ease: [0.68, -0.55, 0.265, 1.55]\r\n        }}\r\n      />\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQO,SAAS;IACd,MAAM,EAAE,WAAW,EAAE,eAAe,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IAChE,MAAM,WAAW,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IAE3B,mCAAmC;IACnC,IAAI,CAAC,UAAU;QACb,OAAO;IACT;IAEA,eAAe;IACf,MAAM,UAAU,gBAAgB,YAAY,IAAI;IAEhD,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,kBAAkB;IAClB,MAAM,4BAA4B;QAChC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG;QAEzB,2BAA2B;QAC3B,MAAM,kBAAkB,OAAO,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC;QAExD,sBAAsB;QACtB,kEAAkE;QAClE,qEAAqE;QACrE,qEAAqE;QACrE,iEAAiE;QACjE,MAAM,MAAM,kBAAkB;QAC9B,MAAM,SAAS,KAAK,KAAK,CAAC,kBAAkB;QAE5C,uBAAuB;QACvB,MAAM,gBAAgB,SAAS,IAAI,gBAAgB;QACnD,MAAM,iBAAiB,gBAAgB,IAAI,aAAa;QAExD,OAAO;YACL,KAAK,KAAK,GAAG,CAAC,KAAK;YACnB,QAAQ,KAAK,GAAG,CAAC,QAAQ;YACzB,gBAAgB,KAAK,GAAG,CAAC,gBAAgB,GAAG,iBAAiB;QAC/D;IACF;IAEA,MAAM,WAAW;IAEjB,cAAc;IACd,MAAM,mBAAmB;QACvB,kBAAkB;QAClB,MAAM,MAAM,GAAG,yBAAyB;QAExC,sBAAsB;QACtB,MAAM,aAAa,CAAC,aAAa,EAAE,MAAM,EAAE,QAAQ,CAAC;QACpD,MAAM,cAAc,IAAI,OAAO;QAE/B,wBAAwB;QACxB,MAAM,aAAa,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,IAAI,EAAE,WAAW,GAAG,EAAE,IAAI,MAAM,EAAE,SAAS,cAAc,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;QAEvH,2BAA2B;QAC3B,MAAM,YAAY,SAAS,GAAG,GAAG,CAAC,cAAc,GAAG;QAEnD,OAAO;YACL,MAAM;YACN,KAAK,GAAG,UAAU,EAAE,CAAC;YACrB,QAAQ,GAAG,YAAY,EAAE,CAAC;QAC5B;IACF;IAEA,MAAM,gBAAgB;IACtB,MAAM,oBAAoB,GAAG,YAAY,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,YAAY,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IAE7H,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,OAAO;YACL,GAAG,aAAa;YAChB,OAAO;YACP,iBAAiB;YACjB,cAAc;YACd,WAAW;YACX,QAAQ;QACV;QACA,SAAS;YAAE,SAAS;YAAG,QAAQ;QAAE;QACjC,SAAS;YAAE,SAAS;YAAG,QAAQ;QAAE;QACjC,YAAY;YACV,UAAU;YACV,MAAM;gBAAC;gBAAK;gBAAG;gBAAK;aAAE;QACxB;;0BAGA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBACL,KAAK;oBACL,MAAM;oBACN,WAAW;oBACX,YAAY;oBACZ,OAAO;oBACP,UAAU;oBACV,YAAY;oBACZ,SAAS;oBACT,cAAc;oBACd,WAAW;oBACX,YAAY;gBACd;gBACA,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBACV,UAAU;oBACV,OAAO;oBACP,MAAM;wBAAC;wBAAK;wBAAG;wBAAK;qBAAE;gBACxB;0BAEC;;;;;;0BAIH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBACL,KAAK;oBACL,MAAM;oBACN,WAAW;oBACX,OAAO;oBACP,QAAQ;oBACR,iBAAiB;oBACjB,cAAc;oBACd,QAAQ;oBACR,WAAW;gBACb;gBACA,SAAS;oBAAE,OAAO;gBAAE;gBACpB,SAAS;oBAAE,OAAO;gBAAE;gBACpB,YAAY;oBACV,UAAU;oBACV,OAAO;oBACP,MAAM;wBAAC;wBAAM,CAAC;wBAAM;wBAAO;qBAAK;gBAClC;;;;;;0BAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBACL,QAAQ;oBACR,MAAM;oBACN,WAAW;oBACX,OAAO;oBACP,QAAQ;oBACR,iBAAiB;oBACjB,cAAc;oBACd,QAAQ;oBACR,WAAW;gBACb;gBACA,SAAS;oBAAE,OAAO;gBAAE;gBACpB,SAAS;oBAAE,OAAO;gBAAE;gBACpB,YAAY;oBACV,UAAU;oBACV,OAAO;oBACP,MAAM;wBAAC;wBAAM,CAAC;wBAAM;wBAAO;qBAAK;gBAClC;;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 3646, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/components/DragPreview.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport { type DragPath } from '@/utils/dragUtils';\r\n\r\ninterface DragPreviewProps {\r\n  dragPath: DragPath | null;\r\n  isVisible: boolean;\r\n}\r\n\r\nexport function DragPreview({ dragPath, isVisible }: DragPreviewProps) {\r\n  if (!isVisible || !dragPath) {\r\n    return null;\r\n  }\r\n\r\n  const { direction, timeRange, selectedSlots } = dragPath;\r\n\r\n  // 计算预览位置（基于第一个选中的时间槽）\r\n  const firstSlot = selectedSlots[0];\r\n  const row = Math.floor(firstSlot / 6);\r\n  const col = firstSlot % 6;\r\n\r\n  // 计算预览框的位置\r\n  const getPreviewStyle = () => {\r\n    const gap = 8; // var(--spacing-2)\r\n    const blockWidth = `calc((100% - ${gap * 5}px) / 6)`;\r\n    const blockHeight = 60;\r\n\r\n    const leftOffset = `calc(${col} * (${blockWidth} + ${gap}px))`;\r\n    const topOffset = row * (blockHeight + gap) - 40; // 在时间块上方显示\r\n\r\n    return {\r\n      left: leftOffset,\r\n      top: `${topOffset}px`,\r\n      position: 'absolute' as const,\r\n      zIndex: 20\r\n    };\r\n  };\r\n\r\n  // 获取方向指示颜色\r\n  const getDirectionColor = () => {\r\n    switch (direction.type) {\r\n      case 'vertical':\r\n        return 'var(--success-500)'; // 绿色 - 垂直选择\r\n      case 'diagonal':\r\n        return 'var(--info-500)'; // 蓝色 - 跨列选择\r\n      default:\r\n        return 'var(--success-500)';\r\n    }\r\n  };\r\n\r\n  // 获取方向图标\r\n  const getDirectionIcon = () => {\r\n    switch (direction.type) {\r\n      case 'vertical':\r\n        return '↕️';\r\n      case 'diagonal':\r\n        return '↗️';\r\n      default:\r\n        return '↕️';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      <motion.div\r\n        style={getPreviewStyle()}\r\n        initial={{ opacity: 0, scale: 0.8, y: -10 }}\r\n        animate={{ opacity: 1, scale: 1, y: 0 }}\r\n        exit={{ opacity: 0, scale: 0.8, y: -10 }}\r\n        transition={{ duration: 0.2, ease: [0.4, 0, 0.2, 1] }}\r\n      >\r\n        {/* 主预览卡片 */}\r\n        <div\r\n          style={{\r\n            background: 'white',\r\n            borderRadius: 'var(--radius-lg)',\r\n            boxShadow: 'var(--shadow-lg)',\r\n            border: `2px solid ${getDirectionColor()}`,\r\n            padding: 'var(--spacing-3)',\r\n            minWidth: '200px',\r\n            fontSize: 'var(--font-size-sm)'\r\n          }}\r\n        >\r\n          {/* 方向指示 */}\r\n          <div\r\n            style={{\r\n              display: 'flex',\r\n              alignItems: 'center',\r\n              gap: 'var(--spacing-2)',\r\n              marginBottom: 'var(--spacing-2)',\r\n              color: getDirectionColor(),\r\n              fontWeight: 'var(--font-weight-semibold)'\r\n            }}\r\n          >\r\n            <span style={{ fontSize: 'var(--font-size-base)' }}>\r\n              {getDirectionIcon()}\r\n            </span>\r\n            <span>{direction.type === 'vertical' ? '垂直选择' : '跨列选择'}</span>\r\n          </div>\r\n\r\n          {/* 时间范围 */}\r\n          <div\r\n            style={{\r\n              marginBottom: 'var(--spacing-2)'\r\n            }}\r\n          >\r\n            <div\r\n              style={{\r\n                fontFamily: 'var(--font-mono)',\r\n                fontWeight: 'var(--font-weight-bold)',\r\n                color: 'var(--neutral-900)',\r\n                fontSize: 'var(--font-size-base)'\r\n              }}\r\n            >\r\n              {timeRange.start} - {timeRange.end}\r\n            </div>\r\n            <div\r\n              style={{\r\n                color: 'var(--neutral-600)',\r\n                fontSize: 'var(--font-size-xs)'\r\n              }}\r\n            >\r\n              {timeRange.duration} 分钟 · {selectedSlots.length} 个时间块\r\n            </div>\r\n          </div>\r\n\r\n          {/* 建议提示 */}\r\n          {direction.suggestion && (\r\n            <div\r\n              style={{\r\n                fontSize: 'var(--font-size-xs)',\r\n                color: 'var(--success-600)',\r\n                fontStyle: 'italic',\r\n                borderTop: `1px solid var(--neutral-200)`,\r\n                paddingTop: 'var(--spacing-2)',\r\n                marginTop: 'var(--spacing-2)'\r\n              }}\r\n            >\r\n              💡 {direction.suggestion}\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* 连接线指示器（对于跨行选择） */}\r\n        {direction.type === 'diagonal' && selectedSlots.length > 1 && (\r\n          <motion.div\r\n            style={{\r\n              position: 'absolute',\r\n              top: '100%',\r\n              left: '50%',\r\n              transform: 'translateX(-50%)',\r\n              marginTop: 'var(--spacing-1)'\r\n            }}\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            transition={{ delay: 0.1 }}\r\n          >\r\n            <div\r\n              style={{\r\n                width: '2px',\r\n                height: '20px',\r\n                background: `linear-gradient(to bottom, ${getDirectionColor()}, transparent)`,\r\n                borderRadius: '1px'\r\n              }}\r\n            />\r\n          </motion.div>\r\n        )}\r\n      </motion.div>\r\n    </AnimatePresence>\r\n  );\r\n}\r\n\r\n/**\r\n * 拖拽路径可视化组件\r\n * 在时间块网格上显示拖拽路径\r\n */\r\ninterface DragPathVisualizerProps {\r\n  dragPath: DragPath | null;\r\n  isVisible: boolean;\r\n}\r\n\r\nexport function DragPathVisualizer({ dragPath, isVisible }: DragPathVisualizerProps) {\r\n  if (!isVisible || !dragPath || dragPath.selectedSlots.length <= 1) {\r\n    return null;\r\n  }\r\n\r\n  const { selectedSlots, direction } = dragPath;\r\n\r\n  return (\r\n    <div\r\n      style={{\r\n        position: 'absolute',\r\n        inset: 0,\r\n        pointerEvents: 'none',\r\n        zIndex: 5\r\n      }}\r\n    >\r\n      {/* 为每个选中的时间槽添加路径指示 */}\r\n      {selectedSlots.map((slot, index) => {\r\n        // 统一坐标系统：按列优先布局 (6列×8行)\r\n        // timeSlot转行列：row = timeSlot % 8, col = Math.floor(timeSlot / 8)\r\n        const row = slot % 8;\r\n        const col = Math.floor(slot / 8);\r\n        const isFirst = index === 0;\r\n        const isLast = index === selectedSlots.length - 1;\r\n\r\n        const gap = 8;\r\n        const blockWidth = `calc((100% - ${gap * 5}px) / 6)`;\r\n        const blockHeight = 60;\r\n\r\n        const leftOffset = `calc(${col} * (${blockWidth} + ${gap}px))`;\r\n        const topOffset = row * (blockHeight + gap);\r\n\r\n        return (\r\n          <motion.div\r\n            key={`path-${slot}`}\r\n            style={{\r\n              position: 'absolute',\r\n              left: leftOffset,\r\n              top: `${topOffset}px`,\r\n              width: blockWidth,\r\n              height: `${blockHeight}px`,\r\n              border: `2px solid ${direction.isColumnPrimary ? 'var(--success-400)' : 'var(--warning-400)'}`,\r\n              borderRadius: 'var(--radius-lg)',\r\n              backgroundColor: direction.isColumnPrimary ? 'var(--success-50)' : 'var(--warning-50)',\r\n              opacity: 0.8\r\n            }}\r\n            initial={{ opacity: 0, scale: 0.8 }}\r\n            animate={{ opacity: 0.8, scale: 1 }}\r\n            transition={{ duration: 0.15, delay: index * 0.02 }}\r\n          >\r\n            {/* 起始和结束标记 */}\r\n            {(isFirst || isLast) && (\r\n              <div\r\n                style={{\r\n                  position: 'absolute',\r\n                  top: '50%',\r\n                  left: '50%',\r\n                  transform: 'translate(-50%, -50%)',\r\n                  width: '12px',\r\n                  height: '12px',\r\n                  borderRadius: '50%',\r\n                  backgroundColor: direction.isColumnPrimary ? 'var(--success-500)' : 'var(--warning-500)',\r\n                  border: '2px solid white',\r\n                  boxShadow: 'var(--shadow-sm)'\r\n                }}\r\n              />\r\n            )}\r\n          </motion.div>\r\n        );\r\n      })}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAGA;AAAA;AAHA;;;AAWO,SAAS,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAoB;IACnE,IAAI,CAAC,aAAa,CAAC,UAAU;QAC3B,OAAO;IACT;IAEA,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,aAAa,EAAE,GAAG;IAEhD,sBAAsB;IACtB,MAAM,YAAY,aAAa,CAAC,EAAE;IAClC,MAAM,MAAM,KAAK,KAAK,CAAC,YAAY;IACnC,MAAM,MAAM,YAAY;IAExB,WAAW;IACX,MAAM,kBAAkB;QACtB,MAAM,MAAM,GAAG,mBAAmB;QAClC,MAAM,aAAa,CAAC,aAAa,EAAE,MAAM,EAAE,QAAQ,CAAC;QACpD,MAAM,cAAc;QAEpB,MAAM,aAAa,CAAC,KAAK,EAAE,IAAI,IAAI,EAAE,WAAW,GAAG,EAAE,IAAI,IAAI,CAAC;QAC9D,MAAM,YAAY,MAAM,CAAC,cAAc,GAAG,IAAI,IAAI,WAAW;QAE7D,OAAO;YACL,MAAM;YACN,KAAK,GAAG,UAAU,EAAE,CAAC;YACrB,UAAU;YACV,QAAQ;QACV;IACF;IAEA,WAAW;IACX,MAAM,oBAAoB;QACxB,OAAQ,UAAU,IAAI;YACpB,KAAK;gBACH,OAAO,sBAAsB,YAAY;YAC3C,KAAK;gBACH,OAAO,mBAAmB,YAAY;YACxC;gBACE,OAAO;QACX;IACF;IAEA,SAAS;IACT,MAAM,mBAAmB;QACvB,OAAQ,UAAU,IAAI;YACpB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACd,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,OAAO;YACP,SAAS;gBAAE,SAAS;gBAAG,OAAO;gBAAK,GAAG,CAAC;YAAG;YAC1C,SAAS;gBAAE,SAAS;gBAAG,OAAO;gBAAG,GAAG;YAAE;YACtC,MAAM;gBAAE,SAAS;gBAAG,OAAO;gBAAK,GAAG,CAAC;YAAG;YACvC,YAAY;gBAAE,UAAU;gBAAK,MAAM;oBAAC;oBAAK;oBAAG;oBAAK;iBAAE;YAAC;;8BAGpD,8OAAC;oBACC,OAAO;wBACL,YAAY;wBACZ,cAAc;wBACd,WAAW;wBACX,QAAQ,CAAC,UAAU,EAAE,qBAAqB;wBAC1C,SAAS;wBACT,UAAU;wBACV,UAAU;oBACZ;;sCAGA,8OAAC;4BACC,OAAO;gCACL,SAAS;gCACT,YAAY;gCACZ,KAAK;gCACL,cAAc;gCACd,OAAO;gCACP,YAAY;4BACd;;8CAEA,8OAAC;oCAAK,OAAO;wCAAE,UAAU;oCAAwB;8CAC9C;;;;;;8CAEH,8OAAC;8CAAM,UAAU,IAAI,KAAK,aAAa,SAAS;;;;;;;;;;;;sCAIlD,8OAAC;4BACC,OAAO;gCACL,cAAc;4BAChB;;8CAEA,8OAAC;oCACC,OAAO;wCACL,YAAY;wCACZ,YAAY;wCACZ,OAAO;wCACP,UAAU;oCACZ;;wCAEC,UAAU,KAAK;wCAAC;wCAAI,UAAU,GAAG;;;;;;;8CAEpC,8OAAC;oCACC,OAAO;wCACL,OAAO;wCACP,UAAU;oCACZ;;wCAEC,UAAU,QAAQ;wCAAC;wCAAO,cAAc,MAAM;wCAAC;;;;;;;;;;;;;wBAKnD,UAAU,UAAU,kBACnB,8OAAC;4BACC,OAAO;gCACL,UAAU;gCACV,OAAO;gCACP,WAAW;gCACX,WAAW,CAAC,4BAA4B,CAAC;gCACzC,YAAY;gCACZ,WAAW;4BACb;;gCACD;gCACK,UAAU,UAAU;;;;;;;;;;;;;gBAM7B,UAAU,IAAI,KAAK,cAAc,cAAc,MAAM,GAAG,mBACvD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,OAAO;wBACL,UAAU;wBACV,KAAK;wBACL,MAAM;wBACN,WAAW;wBACX,WAAW;oBACb;oBACA,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,YAAY;wBAAE,OAAO;oBAAI;8BAEzB,cAAA,8OAAC;wBACC,OAAO;4BACL,OAAO;4BACP,QAAQ;4BACR,YAAY,CAAC,2BAA2B,EAAE,oBAAoB,cAAc,CAAC;4BAC7E,cAAc;wBAChB;;;;;;;;;;;;;;;;;;;;;;AAOd;AAWO,SAAS,mBAAmB,EAAE,QAAQ,EAAE,SAAS,EAA2B;IACjF,IAAI,CAAC,aAAa,CAAC,YAAY,SAAS,aAAa,CAAC,MAAM,IAAI,GAAG;QACjE,OAAO;IACT;IAEA,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG;IAErC,qBACE,8OAAC;QACC,OAAO;YACL,UAAU;YACV,OAAO;YACP,eAAe;YACf,QAAQ;QACV;kBAGC,cAAc,GAAG,CAAC,CAAC,MAAM;YACxB,wBAAwB;YACxB,iEAAiE;YACjE,MAAM,MAAM,OAAO;YACnB,MAAM,MAAM,KAAK,KAAK,CAAC,OAAO;YAC9B,MAAM,UAAU,UAAU;YAC1B,MAAM,SAAS,UAAU,cAAc,MAAM,GAAG;YAEhD,MAAM,MAAM;YACZ,MAAM,aAAa,CAAC,aAAa,EAAE,MAAM,EAAE,QAAQ,CAAC;YACpD,MAAM,cAAc;YAEpB,MAAM,aAAa,CAAC,KAAK,EAAE,IAAI,IAAI,EAAE,WAAW,GAAG,EAAE,IAAI,IAAI,CAAC;YAC9D,MAAM,YAAY,MAAM,CAAC,cAAc,GAAG;YAE1C,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAET,OAAO;oBACL,UAAU;oBACV,MAAM;oBACN,KAAK,GAAG,UAAU,EAAE,CAAC;oBACrB,OAAO;oBACP,QAAQ,GAAG,YAAY,EAAE,CAAC;oBAC1B,QAAQ,CAAC,UAAU,EAAE,UAAU,eAAe,GAAG,uBAAuB,sBAAsB;oBAC9F,cAAc;oBACd,iBAAiB,UAAU,eAAe,GAAG,sBAAsB;oBACnE,SAAS;gBACX;gBACA,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAI;gBAClC,SAAS;oBAAE,SAAS;oBAAK,OAAO;gBAAE;gBAClC,YAAY;oBAAE,UAAU;oBAAM,OAAO,QAAQ;gBAAK;0BAGjD,CAAC,WAAW,MAAM,mBACjB,8OAAC;oBACC,OAAO;wBACL,UAAU;wBACV,KAAK;wBACL,MAAM;wBACN,WAAW;wBACX,OAAO;wBACP,QAAQ;wBACR,cAAc;wBACd,iBAAiB,UAAU,eAAe,GAAG,uBAAuB;wBACpE,QAAQ;wBACR,WAAW;oBACb;;;;;;eA9BC,CAAC,KAAK,EAAE,MAAM;;;;;QAmCzB;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 3969, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/components/DragGuide.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport { X, ArrowDown, RotateCcw } from 'lucide-react';\r\n\r\ninterface DragGuideProps {\r\n  isVisible: boolean;\r\n  onClose: () => void;\r\n}\r\n\r\nexport function DragGuide({ isVisible, onClose }: DragGuideProps) {\r\n  const [currentStep, setCurrentStep] = useState(0);\r\n\r\n  const steps = [\r\n    {\r\n      title: '列优先拖拽方式',\r\n      description: '在6列时间轴布局中，使用列优先的垂直拖拽来选择连续时间',\r\n      icon: <ArrowDown size={24} />,\r\n      color: 'var(--success-500)',\r\n      demo: 'vertical'\r\n    },\r\n    {\r\n      title: '垂直拖拽',\r\n      description: '在同一列内从上到下拖拽，选择同一时间段内的连续时间（如：深夜 00:00 → 04:00）',\r\n      icon: <ArrowDown size={24} />,\r\n      color: 'var(--success-500)',\r\n      demo: 'vertical-demo'\r\n    },\r\n    {\r\n      title: '跨列选择',\r\n      description: '跨列拖拽时采用列优先算法，先填满当前列再跳到下一列，保持时间连续性',\r\n      icon: <RotateCcw size={24} />,\r\n      color: 'var(--info-500)',\r\n      demo: 'diagonal'\r\n    }\r\n  ];\r\n\r\n  const currentStepData = steps[currentStep];\r\n\r\n  const nextStep = () => {\r\n    if (currentStep < steps.length - 1) {\r\n      setCurrentStep(currentStep + 1);\r\n    } else {\r\n      onClose();\r\n    }\r\n  };\r\n\r\n  const prevStep = () => {\r\n    if (currentStep > 0) {\r\n      setCurrentStep(currentStep - 1);\r\n    }\r\n  };\r\n\r\n  // 自动播放演示动画\r\n  useEffect(() => {\r\n    if (!isVisible) return;\r\n\r\n    const timer = setTimeout(() => {\r\n      // 这里可以添加演示动画逻辑\r\n    }, 1000);\r\n\r\n    return () => clearTimeout(timer);\r\n  }, [currentStep, isVisible]);\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      {isVisible && (\r\n        <>\r\n          {/* 背景遮罩 */}\r\n          <motion.div\r\n            className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-50\"\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            exit={{ opacity: 0 }}\r\n            onClick={onClose}\r\n          />\r\n\r\n          {/* 引导卡片 */}\r\n          <motion.div\r\n            className=\"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50\"\r\n            initial={{ opacity: 0, scale: 0.8, y: 20 }}\r\n            animate={{ opacity: 1, scale: 1, y: 0 }}\r\n            exit={{ opacity: 0, scale: 0.8, y: 20 }}\r\n            transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}\r\n          >\r\n            <div\r\n              style={{\r\n                background: 'white',\r\n                borderRadius: 'var(--radius-2xl)',\r\n                boxShadow: 'var(--shadow-2xl)',\r\n                border: '1px solid var(--neutral-200)',\r\n                padding: 'var(--spacing-8)',\r\n                maxWidth: '480px',\r\n                width: '90vw'\r\n              }}\r\n            >\r\n              {/* 关闭按钮 */}\r\n              <button\r\n                onClick={onClose}\r\n                style={{\r\n                  position: 'absolute',\r\n                  top: 'var(--spacing-4)',\r\n                  right: 'var(--spacing-4)',\r\n                  background: 'none',\r\n                  border: 'none',\r\n                  cursor: 'pointer',\r\n                  color: 'var(--neutral-500)',\r\n                  padding: 'var(--spacing-2)',\r\n                  borderRadius: 'var(--radius-md)',\r\n                  transition: 'all var(--duration-fast) var(--ease-out)'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  e.currentTarget.style.backgroundColor = 'var(--neutral-100)';\r\n                  e.currentTarget.style.color = 'var(--neutral-700)';\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  e.currentTarget.style.backgroundColor = 'transparent';\r\n                  e.currentTarget.style.color = 'var(--neutral-500)';\r\n                }}\r\n              >\r\n                <X size={20} />\r\n              </button>\r\n\r\n              {/* 步骤指示器 */}\r\n              <div\r\n                style={{\r\n                  display: 'flex',\r\n                  gap: 'var(--spacing-2)',\r\n                  marginBottom: 'var(--spacing-6)',\r\n                  justifyContent: 'center'\r\n                }}\r\n              >\r\n                {steps.map((_, index) => (\r\n                  <div\r\n                    key={index}\r\n                    style={{\r\n                      width: '8px',\r\n                      height: '8px',\r\n                      borderRadius: '50%',\r\n                      backgroundColor: index === currentStep ? currentStepData.color : 'var(--neutral-300)',\r\n                      transition: 'all var(--duration-fast) var(--ease-out)'\r\n                    }}\r\n                  />\r\n                ))}\r\n              </div>\r\n\r\n              {/* 图标 */}\r\n              <div\r\n                style={{\r\n                  display: 'flex',\r\n                  justifyContent: 'center',\r\n                  marginBottom: 'var(--spacing-4)'\r\n                }}\r\n              >\r\n                <div\r\n                  style={{\r\n                    width: '64px',\r\n                    height: '64px',\r\n                    borderRadius: '50%',\r\n                    backgroundColor: `${currentStepData.color}20`,\r\n                    display: 'flex',\r\n                    alignItems: 'center',\r\n                    justifyContent: 'center',\r\n                    color: currentStepData.color\r\n                  }}\r\n                >\r\n                  {currentStepData.icon}\r\n                </div>\r\n              </div>\r\n\r\n              {/* 标题 */}\r\n              <h3\r\n                style={{\r\n                  fontSize: 'var(--font-size-xl)',\r\n                  fontWeight: 'var(--font-weight-bold)',\r\n                  color: 'var(--neutral-900)',\r\n                  textAlign: 'center',\r\n                  marginBottom: 'var(--spacing-3)'\r\n                }}\r\n              >\r\n                {currentStepData.title}\r\n              </h3>\r\n\r\n              {/* 描述 */}\r\n              <p\r\n                style={{\r\n                  fontSize: 'var(--font-size-base)',\r\n                  color: 'var(--neutral-600)',\r\n                  textAlign: 'center',\r\n                  lineHeight: 'var(--line-height-relaxed)',\r\n                  marginBottom: 'var(--spacing-6)'\r\n                }}\r\n              >\r\n                {currentStepData.description}\r\n              </p>\r\n\r\n              {/* 演示区域 */}\r\n              <div\r\n                style={{\r\n                  background: 'var(--neutral-50)',\r\n                  borderRadius: 'var(--radius-lg)',\r\n                  padding: 'var(--spacing-4)',\r\n                  marginBottom: 'var(--spacing-6)',\r\n                  minHeight: '120px',\r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  justifyContent: 'center'\r\n                }}\r\n              >\r\n                <DragDemo type={currentStepData.demo} color={currentStepData.color} />\r\n              </div>\r\n\r\n              {/* 按钮组 */}\r\n              <div\r\n                style={{\r\n                  display: 'flex',\r\n                  gap: 'var(--spacing-3)',\r\n                  justifyContent: 'space-between'\r\n                }}\r\n              >\r\n                <button\r\n                  onClick={prevStep}\r\n                  disabled={currentStep === 0}\r\n                  style={{\r\n                    padding: 'var(--spacing-2) var(--spacing-4)',\r\n                    borderRadius: 'var(--radius-md)',\r\n                    border: '1px solid var(--neutral-300)',\r\n                    background: 'white',\r\n                    color: currentStep === 0 ? 'var(--neutral-400)' : 'var(--neutral-700)',\r\n                    cursor: currentStep === 0 ? 'not-allowed' : 'pointer',\r\n                    fontSize: 'var(--font-size-sm)',\r\n                    fontWeight: 'var(--font-weight-medium)',\r\n                    transition: 'all var(--duration-fast) var(--ease-out)'\r\n                  }}\r\n                >\r\n                  上一步\r\n                </button>\r\n\r\n                <button\r\n                  onClick={nextStep}\r\n                  style={{\r\n                    padding: 'var(--spacing-2) var(--spacing-6)',\r\n                    borderRadius: 'var(--radius-md)',\r\n                    border: 'none',\r\n                    background: currentStepData.color,\r\n                    color: 'white',\r\n                    cursor: 'pointer',\r\n                    fontSize: 'var(--font-size-sm)',\r\n                    fontWeight: 'var(--font-weight-medium)',\r\n                    transition: 'all var(--duration-fast) var(--ease-out)'\r\n                  }}\r\n                >\r\n                  {currentStep === steps.length - 1 ? '开始使用' : '下一步'}\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n        </>\r\n      )}\r\n    </AnimatePresence>\r\n  );\r\n}\r\n\r\n// 拖拽演示组件\r\nfunction DragDemo({ type, color }: { type: string; color: string }) {\r\n  return (\r\n    <div\r\n      style={{\r\n        display: 'grid',\r\n        gridTemplateColumns: 'repeat(3, 1fr)',\r\n        gap: '4px',\r\n        width: '120px',\r\n        height: '80px'\r\n      }}\r\n    >\r\n      {Array.from({ length: 6 }, (_, i) => (\r\n        <motion.div\r\n          key={i}\r\n          style={{\r\n            background: 'white',\r\n            border: '2px solid var(--neutral-200)',\r\n            borderRadius: '4px',\r\n            position: 'relative'\r\n          }}\r\n          animate={\r\n            type === 'vertical-demo' && (i === 0 || i === 3)\r\n              ? { borderColor: color, backgroundColor: `${color}20` }\r\n              : type === 'diagonal' && (i === 0 || i === 3 || i === 1)\r\n              ? { borderColor: color, backgroundColor: `${color}20` }\r\n              : {}\r\n          }\r\n          transition={{ duration: 0.5, delay: i * 0.1 }}\r\n        />\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAJA;;;;;AAWO,SAAS,UAAU,EAAE,SAAS,EAAE,OAAO,EAAkB;IAC9D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,QAAQ;QACZ;YACE,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,gNAAA,CAAA,YAAS;gBAAC,MAAM;;;;;;YACvB,OAAO;YACP,MAAM;QACR;QACA;YACE,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,gNAAA,CAAA,YAAS;gBAAC,MAAM;;;;;;YACvB,OAAO;YACP,MAAM;QACR;QACA;YACE,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,gNAAA,CAAA,YAAS;gBAAC,MAAM;;;;;;YACvB,OAAO;YACP,MAAM;QACR;KACD;IAED,MAAM,kBAAkB,KAAK,CAAC,YAAY;IAE1C,MAAM,WAAW;QACf,IAAI,cAAc,MAAM,MAAM,GAAG,GAAG;YAClC,eAAe,cAAc;QAC/B,OAAO;YACL;QACF;IACF;IAEA,MAAM,WAAW;QACf,IAAI,cAAc,GAAG;YACnB,eAAe,cAAc;QAC/B;IACF;IAEA,WAAW;IACX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW;QAEhB,MAAM,QAAQ,WAAW;QACvB,eAAe;QACjB,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAa;KAAU;IAE3B,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,2BACC;;8BAEE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,SAAS;;;;;;8BAIX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAK,GAAG;oBAAG;oBACzC,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAG,GAAG;oBAAE;oBACtC,MAAM;wBAAE,SAAS;wBAAG,OAAO;wBAAK,GAAG;oBAAG;oBACtC,YAAY;wBAAE,UAAU;wBAAK,MAAM;4BAAC;4BAAK;4BAAG;4BAAK;yBAAE;oBAAC;8BAEpD,cAAA,8OAAC;wBACC,OAAO;4BACL,YAAY;4BACZ,cAAc;4BACd,WAAW;4BACX,QAAQ;4BACR,SAAS;4BACT,UAAU;4BACV,OAAO;wBACT;;0CAGA,8OAAC;gCACC,SAAS;gCACT,OAAO;oCACL,UAAU;oCACV,KAAK;oCACL,OAAO;oCACP,YAAY;oCACZ,QAAQ;oCACR,QAAQ;oCACR,OAAO;oCACP,SAAS;oCACT,cAAc;oCACd,YAAY;gCACd;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oCACxC,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;gCAChC;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oCACxC,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;gCAChC;0CAEA,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,MAAM;;;;;;;;;;;0CAIX,8OAAC;gCACC,OAAO;oCACL,SAAS;oCACT,KAAK;oCACL,cAAc;oCACd,gBAAgB;gCAClB;0CAEC,MAAM,GAAG,CAAC,CAAC,GAAG,sBACb,8OAAC;wCAEC,OAAO;4CACL,OAAO;4CACP,QAAQ;4CACR,cAAc;4CACd,iBAAiB,UAAU,cAAc,gBAAgB,KAAK,GAAG;4CACjE,YAAY;wCACd;uCAPK;;;;;;;;;;0CAaX,8OAAC;gCACC,OAAO;oCACL,SAAS;oCACT,gBAAgB;oCAChB,cAAc;gCAChB;0CAEA,cAAA,8OAAC;oCACC,OAAO;wCACL,OAAO;wCACP,QAAQ;wCACR,cAAc;wCACd,iBAAiB,GAAG,gBAAgB,KAAK,CAAC,EAAE,CAAC;wCAC7C,SAAS;wCACT,YAAY;wCACZ,gBAAgB;wCAChB,OAAO,gBAAgB,KAAK;oCAC9B;8CAEC,gBAAgB,IAAI;;;;;;;;;;;0CAKzB,8OAAC;gCACC,OAAO;oCACL,UAAU;oCACV,YAAY;oCACZ,OAAO;oCACP,WAAW;oCACX,cAAc;gCAChB;0CAEC,gBAAgB,KAAK;;;;;;0CAIxB,8OAAC;gCACC,OAAO;oCACL,UAAU;oCACV,OAAO;oCACP,WAAW;oCACX,YAAY;oCACZ,cAAc;gCAChB;0CAEC,gBAAgB,WAAW;;;;;;0CAI9B,8OAAC;gCACC,OAAO;oCACL,YAAY;oCACZ,cAAc;oCACd,SAAS;oCACT,cAAc;oCACd,WAAW;oCACX,SAAS;oCACT,YAAY;oCACZ,gBAAgB;gCAClB;0CAEA,cAAA,8OAAC;oCAAS,MAAM,gBAAgB,IAAI;oCAAE,OAAO,gBAAgB,KAAK;;;;;;;;;;;0CAIpE,8OAAC;gCACC,OAAO;oCACL,SAAS;oCACT,KAAK;oCACL,gBAAgB;gCAClB;;kDAEA,8OAAC;wCACC,SAAS;wCACT,UAAU,gBAAgB;wCAC1B,OAAO;4CACL,SAAS;4CACT,cAAc;4CACd,QAAQ;4CACR,YAAY;4CACZ,OAAO,gBAAgB,IAAI,uBAAuB;4CAClD,QAAQ,gBAAgB,IAAI,gBAAgB;4CAC5C,UAAU;4CACV,YAAY;4CACZ,YAAY;wCACd;kDACD;;;;;;kDAID,8OAAC;wCACC,SAAS;wCACT,OAAO;4CACL,SAAS;4CACT,cAAc;4CACd,QAAQ;4CACR,YAAY,gBAAgB,KAAK;4CACjC,OAAO;4CACP,QAAQ;4CACR,UAAU;4CACV,YAAY;4CACZ,YAAY;wCACd;kDAEC,gBAAgB,MAAM,MAAM,GAAG,IAAI,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/D;AAEA,SAAS;AACT,SAAS,SAAS,EAAE,IAAI,EAAE,KAAK,EAAmC;IAChE,qBACE,8OAAC;QACC,OAAO;YACL,SAAS;YACT,qBAAqB;YACrB,KAAK;YACL,OAAO;YACP,QAAQ;QACV;kBAEC,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,CAAC,GAAG,kBAC7B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAET,OAAO;oBACL,YAAY;oBACZ,QAAQ;oBACR,cAAc;oBACd,UAAU;gBACZ;gBACA,SACE,SAAS,mBAAmB,CAAC,MAAM,KAAK,MAAM,CAAC,IAC3C;oBAAE,aAAa;oBAAO,iBAAiB,GAAG,MAAM,EAAE,CAAC;gBAAC,IACpD,SAAS,cAAc,CAAC,MAAM,KAAK,MAAM,KAAK,MAAM,CAAC,IACrD;oBAAE,aAAa;oBAAO,iBAAiB,GAAG,MAAM,EAAE,CAAC;gBAAC,IACpD,CAAC;gBAEP,YAAY;oBAAE,UAAU;oBAAK,OAAO,IAAI;gBAAI;eAdvC;;;;;;;;;;AAmBf", "debugId": null}}, {"offset": {"line": 4363, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/components/SelectionInfoPanel.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useMemo } from 'react';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport { Clock, Calendar, BarChart3, Target, Lightbulb } from 'lucide-react';\r\nimport type { DragPath } from '@/utils/dragUtils';\r\n\r\ninterface SelectionInfoPanelProps {\r\n  selectedBlocks: number[];\r\n  dragPath: DragPath | null;\r\n  className?: string;\r\n}\r\n\r\ninterface SelectionInfo {\r\n  timeRange: string;\r\n  duration: string;\r\n  segmentCount: number;\r\n  blockCount: number;\r\n  suggestion: string;\r\n  dragType: string;\r\n}\r\n\r\n// 计算选择信息的工具函数\r\nfunction calculateSelectionInfo(selectedBlocks: number[], dragPath: DragPath | null): SelectionInfo | null {\r\n  if (selectedBlocks.length === 0) return null;\r\n\r\n  // 计算时间范围\r\n  const sortedBlocks = [...selectedBlocks].sort((a, b) => a - b);\r\n  const startBlock = sortedBlocks[0];\r\n  const endBlock = sortedBlocks[sortedBlocks.length - 1];\r\n  \r\n  const startHour = Math.floor(startBlock / 2);\r\n  const startMinute = (startBlock % 2) * 30;\r\n  const endHour = Math.floor((endBlock + 1) / 2);\r\n  const endMinute = ((endBlock + 1) % 2) * 30;\r\n  \r\n  const timeRange = `${startHour.toString().padStart(2, '0')}:${startMinute.toString().padStart(2, '0')} - ${endHour.toString().padStart(2, '0')}:${endMinute.toString().padStart(2, '0')}`;\r\n  \r\n  // 计算总时长\r\n  const totalMinutes = selectedBlocks.length * 30;\r\n  const hours = Math.floor(totalMinutes / 60);\r\n  const minutes = totalMinutes % 60;\r\n  const duration = hours > 0 \r\n    ? (minutes > 0 ? `${hours}小时${minutes}分钟` : `${hours}小时`)\r\n    : `${minutes}分钟`;\r\n  \r\n  // 计算跨越的时间段数量\r\n  const segments = new Set(selectedBlocks.map(block => Math.floor(block / 8)));\r\n  const segmentCount = segments.size;\r\n  \r\n  // 获取拖拽类型和建议\r\n  const dragType = dragPath?.direction.type === 'vertical' ? '垂直选择' : '跨列选择';\r\n  const suggestion = getSuggestion(totalMinutes, segmentCount, dragPath?.direction.type);\r\n  \r\n  return {\r\n    timeRange,\r\n    duration,\r\n    segmentCount,\r\n    blockCount: selectedBlocks.length,\r\n    suggestion,\r\n    dragType\r\n  };\r\n}\r\n\r\n// 根据选择情况提供建议\r\nfunction getSuggestion(totalMinutes: number, segmentCount: number, dragType?: string): string {\r\n  if (totalMinutes >= 120) {\r\n    return '适合深度工作';\r\n  } else if (totalMinutes >= 60) {\r\n    return '适合专注任务';\r\n  } else if (segmentCount > 2) {\r\n    return '时间较分散';\r\n  } else if (dragType === 'vertical') {\r\n    return '时间连续性好';\r\n  } else {\r\n    return '跨时段选择';\r\n  }\r\n}\r\n\r\n// 信息行组件\r\ninterface InfoRowProps {\r\n  icon: React.ReactNode;\r\n  label: string;\r\n  value: string;\r\n  priority: 'high' | 'medium' | 'low';\r\n}\r\n\r\nconst InfoRow: React.FC<InfoRowProps> = ({ icon, label, value, priority }) => {\r\n  const styles = {\r\n    high: {\r\n      textClass: 'text-base font-semibold',\r\n      textColor: 'var(--neutral-900)',\r\n      iconColor: 'var(--primary-600)'\r\n    },\r\n    medium: {\r\n      textClass: 'text-sm font-medium',\r\n      textColor: 'var(--neutral-700)',\r\n      iconColor: 'var(--neutral-600)'\r\n    },\r\n    low: {\r\n      textClass: 'text-xs font-normal',\r\n      textColor: 'var(--neutral-500)',\r\n      iconColor: 'var(--neutral-400)'\r\n    }\r\n  }[priority];\r\n\r\n  return (\r\n    <motion.div\r\n      className=\"flex items-center gap-3 py-1.5\"\r\n      initial={{ opacity: 0, x: -10 }}\r\n      animate={{ opacity: 1, x: 0 }}\r\n      transition={{ duration: 0.2, delay: priority === 'high' ? 0 : priority === 'medium' ? 0.05 : 0.1 }}\r\n    >\r\n      <div\r\n        className=\"flex-shrink-0 w-4 h-4\"\r\n        style={{ color: styles.iconColor }}\r\n      >\r\n        {icon}\r\n      </div>\r\n      <div className=\"flex-1 min-w-0\">\r\n        <span\r\n          className={styles.textClass}\r\n          style={{ color: styles.textColor }}\r\n        >\r\n          {value}\r\n        </span>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n};\r\n\r\n// 分割线组件\r\nconst Divider: React.FC = () => (\r\n  <motion.div\r\n    className=\"my-3\"\r\n    initial={{ scaleX: 0 }}\r\n    animate={{ scaleX: 1 }}\r\n    transition={{ duration: 0.3, delay: 0.1 }}\r\n    style={{\r\n      height: '1px',\r\n      background: 'var(--neutral-200)',\r\n      transformOrigin: 'left'\r\n    }}\r\n  />\r\n);\r\n\r\nexport const SelectionInfoPanel: React.FC<SelectionInfoPanelProps> = ({\r\n  selectedBlocks,\r\n  dragPath,\r\n  className = ''\r\n}) => {\r\n  const selectionInfo = useMemo(() => \r\n    calculateSelectionInfo(selectedBlocks, dragPath), \r\n    [selectedBlocks, dragPath]\r\n  );\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      {selectionInfo && (\r\n        <motion.div\r\n          className={`\r\n            fixed right-6 top-1/2 -translate-y-1/2\r\n            w-60 z-50\r\n            ${className}\r\n          `}\r\n          style={{\r\n            background: 'var(--background)',\r\n            borderRadius: 'var(--radius-lg)',\r\n            boxShadow: 'var(--shadow-lg)',\r\n            border: '1px solid var(--neutral-200)',\r\n            padding: 'var(--spacing-4)',\r\n            backdropFilter: 'blur(8px)',\r\n            backgroundColor: 'rgba(255, 255, 255, 0.95)'\r\n          }}\r\n          initial={{ opacity: 0, scale: 0.95, x: 20 }}\r\n          animate={{ opacity: 1, scale: 1, x: 0 }}\r\n          exit={{ opacity: 0, scale: 0.95, x: 20 }}\r\n          transition={{\r\n            duration: 0.25,\r\n            ease: [0.4, 0, 0.2, 1]\r\n          }}\r\n        >\r\n          {/* 核心信息 */}\r\n          <div className=\"space-y-1\">\r\n            <InfoRow\r\n              icon={<Calendar size={16} />}\r\n              label=\"时间范围\"\r\n              value={selectionInfo.timeRange}\r\n              priority=\"high\"\r\n            />\r\n            <InfoRow\r\n              icon={<Clock size={16} />}\r\n              label=\"总时长\"\r\n              value={selectionInfo.duration}\r\n              priority=\"high\"\r\n            />\r\n          </div>\r\n\r\n          <Divider />\r\n\r\n          {/* 统计信息 */}\r\n          <div className=\"space-y-1\">\r\n            <InfoRow\r\n              icon={<BarChart3 size={16} />}\r\n              label=\"跨越时段\"\r\n              value={`${selectionInfo.segmentCount}个时段`}\r\n              priority=\"medium\"\r\n            />\r\n            <InfoRow\r\n              icon={<Target size={16} />}\r\n              label=\"选择块数\"\r\n              value={`${selectionInfo.blockCount}个时间块`}\r\n              priority=\"medium\"\r\n            />\r\n          </div>\r\n\r\n          <Divider />\r\n\r\n          {/* 建议信息 */}\r\n          <div className=\"space-y-1\">\r\n            <InfoRow\r\n              icon={<Lightbulb size={16} />}\r\n              label=\"建议\"\r\n              value={selectionInfo.suggestion}\r\n              priority=\"low\"\r\n            />\r\n          </div>\r\n\r\n          {/* 装饰性指示器 */}\r\n          <motion.div\r\n            className=\"absolute -left-2 top-1/2 -translate-y-1/2\"\r\n            initial={{ scaleY: 0 }}\r\n            animate={{ scaleY: 1 }}\r\n            transition={{ duration: 0.4, delay: 0.2 }}\r\n          >\r\n            <div\r\n              className=\"w-1 h-12 rounded-full\"\r\n              style={{\r\n                background: 'linear-gradient(to bottom, var(--primary-500), var(--primary-300))',\r\n                opacity: 0.8,\r\n                boxShadow: '0 0 8px var(--primary-200)'\r\n              }}\r\n            />\r\n          </motion.div>\r\n        </motion.div>\r\n      )}\r\n    </AnimatePresence>\r\n  );\r\n};\r\n\r\nexport default SelectionInfoPanel;\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAsBA,cAAc;AACd,SAAS,uBAAuB,cAAwB,EAAE,QAAyB;IACjF,IAAI,eAAe,MAAM,KAAK,GAAG,OAAO;IAExC,SAAS;IACT,MAAM,eAAe;WAAI;KAAe,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;IAC5D,MAAM,aAAa,YAAY,CAAC,EAAE;IAClC,MAAM,WAAW,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE;IAEtD,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa;IAC1C,MAAM,cAAc,AAAC,aAAa,IAAK;IACvC,MAAM,UAAU,KAAK,KAAK,CAAC,CAAC,WAAW,CAAC,IAAI;IAC5C,MAAM,YAAY,AAAC,CAAC,WAAW,CAAC,IAAI,IAAK;IAEzC,MAAM,YAAY,GAAG,UAAU,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,YAAY,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,GAAG,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,UAAU,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IAEzL,QAAQ;IACR,MAAM,eAAe,eAAe,MAAM,GAAG;IAC7C,MAAM,QAAQ,KAAK,KAAK,CAAC,eAAe;IACxC,MAAM,UAAU,eAAe;IAC/B,MAAM,WAAW,QAAQ,IACpB,UAAU,IAAI,GAAG,MAAM,EAAE,EAAE,QAAQ,EAAE,CAAC,GAAG,GAAG,MAAM,EAAE,CAAC,GACtD,GAAG,QAAQ,EAAE,CAAC;IAElB,aAAa;IACb,MAAM,WAAW,IAAI,IAAI,eAAe,GAAG,CAAC,CAAA,QAAS,KAAK,KAAK,CAAC,QAAQ;IACxE,MAAM,eAAe,SAAS,IAAI;IAElC,YAAY;IACZ,MAAM,WAAW,UAAU,UAAU,SAAS,aAAa,SAAS;IACpE,MAAM,aAAa,cAAc,cAAc,cAAc,UAAU,UAAU;IAEjF,OAAO;QACL;QACA;QACA;QACA,YAAY,eAAe,MAAM;QACjC;QACA;IACF;AACF;AAEA,aAAa;AACb,SAAS,cAAc,YAAoB,EAAE,YAAoB,EAAE,QAAiB;IAClF,IAAI,gBAAgB,KAAK;QACvB,OAAO;IACT,OAAO,IAAI,gBAAgB,IAAI;QAC7B,OAAO;IACT,OAAO,IAAI,eAAe,GAAG;QAC3B,OAAO;IACT,OAAO,IAAI,aAAa,YAAY;QAClC,OAAO;IACT,OAAO;QACL,OAAO;IACT;AACF;AAUA,MAAM,UAAkC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE;IACvE,MAAM,SAAS;QACb,MAAM;YACJ,WAAW;YACX,WAAW;YACX,WAAW;QACb;QACA,QAAQ;YACN,WAAW;YACX,WAAW;YACX,WAAW;QACb;QACA,KAAK;YACH,WAAW;YACX,WAAW;YACX,WAAW;QACb;IACF,CAAC,CAAC,SAAS;IAEX,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC9B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,OAAO,aAAa,SAAS,IAAI,aAAa,WAAW,OAAO;QAAI;;0BAEjG,8OAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,OAAO,OAAO,SAAS;gBAAC;0BAEhC;;;;;;0BAEH,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAW,OAAO,SAAS;oBAC3B,OAAO;wBAAE,OAAO,OAAO,SAAS;oBAAC;8BAEhC;;;;;;;;;;;;;;;;;AAKX;AAEA,QAAQ;AACR,MAAM,UAAoB,kBACxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,QAAQ;QAAE;QACrB,SAAS;YAAE,QAAQ;QAAE;QACrB,YAAY;YAAE,UAAU;YAAK,OAAO;QAAI;QACxC,OAAO;YACL,QAAQ;YACR,YAAY;YACZ,iBAAiB;QACnB;;;;;;AAIG,MAAM,qBAAwD,CAAC,EACpE,cAAc,EACd,QAAQ,EACR,YAAY,EAAE,EACf;IACC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAC5B,uBAAuB,gBAAgB,WACvC;QAAC;QAAgB;KAAS;IAG5B,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,+BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAW,CAAC;;;YAGV,EAAE,UAAU;UACd,CAAC;YACD,OAAO;gBACL,YAAY;gBACZ,cAAc;gBACd,WAAW;gBACX,QAAQ;gBACR,SAAS;gBACT,gBAAgB;gBAChB,iBAAiB;YACnB;YACA,SAAS;gBAAE,SAAS;gBAAG,OAAO;gBAAM,GAAG;YAAG;YAC1C,SAAS;gBAAE,SAAS;gBAAG,OAAO;gBAAG,GAAG;YAAE;YACtC,MAAM;gBAAE,SAAS;gBAAG,OAAO;gBAAM,GAAG;YAAG;YACvC,YAAY;gBACV,UAAU;gBACV,MAAM;oBAAC;oBAAK;oBAAG;oBAAK;iBAAE;YACxB;;8BAGA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,MAAM;;;;;;4BACtB,OAAM;4BACN,OAAO,cAAc,SAAS;4BAC9B,UAAS;;;;;;sCAEX,8OAAC;4BACC,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gCAAC,MAAM;;;;;;4BACnB,OAAM;4BACN,OAAO,cAAc,QAAQ;4BAC7B,UAAS;;;;;;;;;;;;8BAIb,8OAAC;;;;;8BAGD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,oBAAM,8OAAC,kNAAA,CAAA,YAAS;gCAAC,MAAM;;;;;;4BACvB,OAAM;4BACN,OAAO,GAAG,cAAc,YAAY,CAAC,GAAG,CAAC;4BACzC,UAAS;;;;;;sCAEX,8OAAC;4BACC,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gCAAC,MAAM;;;;;;4BACpB,OAAM;4BACN,OAAO,GAAG,cAAc,UAAU,CAAC,IAAI,CAAC;4BACxC,UAAS;;;;;;;;;;;;8BAIb,8OAAC;;;;;8BAGD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,oBAAM,8OAAC,4MAAA,CAAA,YAAS;4BAAC,MAAM;;;;;;wBACvB,OAAM;wBACN,OAAO,cAAc,UAAU;wBAC/B,UAAS;;;;;;;;;;;8BAKb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,QAAQ;oBAAE;oBACrB,SAAS;wBAAE,QAAQ;oBAAE;oBACrB,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;8BAExC,cAAA,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,YAAY;4BACZ,SAAS;4BACT,WAAW;wBACb;;;;;;;;;;;;;;;;;;;;;;AAOd;uCAEe", "debugId": null}}, {"offset": {"line": 4728, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/components/TimeGrid.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useCallback, useEffect, useState } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { useAppStore } from '@/stores/useAppStore';\r\nimport { isTimeSlotEditable } from '@/utils/timeUtils';\r\nimport type { TimeBlock as TimeBlockType } from '@/types';\r\nimport {\r\n  calculateDragPath,\r\n  detectDragDirection,\r\n  getDragSuggestion,\r\n  type DragPath\r\n} from '@/utils/dragUtils';\r\nimport { TimeBlock } from './TimeBlock';\r\nimport { ActivityPalette } from './ActivityPalette';\r\nimport { TimelineIndicator } from './TimelineIndicator';\r\nimport { DragPathVisualizer } from './DragPreview';\r\nimport { DragGuide } from './DragGuide';\r\nimport { SelectionInfoPanel } from './SelectionInfoPanel';\r\n\r\n\r\nexport function TimeGrid() {\r\n  const {\r\n    currentDate,\r\n    getTimeBlocksForDate,\r\n    ui,\r\n    setSelectedBlocks,\r\n    clearSelectedBlocks,\r\n    setShowActivityPalette,\r\n    setDragging,\r\n    updateMultipleTimeBlocks\r\n  } = useAppStore();\r\n\r\n  const timeBlocks = getTimeBlocksForDate(currentDate);\r\n  const [dragStart, setDragStart] = useState<number | null>(null);\r\n  const [dragEnd, setDragEnd] = useState<number | null>(null);\r\n  const [currentDragPath, setCurrentDragPath] = useState<DragPath | null>(null);\r\n  const [showDragGuide, setShowDragGuide] = useState(false);\r\n\r\n\r\n  // 检查是否需要显示拖拽指南（首次使用）\r\n  useEffect(() => {\r\n    const hasSeenDragGuide = localStorage.getItem('chronospect-drag-guide-seen');\r\n    if (!hasSeenDragGuide) {\r\n      // 延迟显示，让用户先看到新界面\r\n      const timer = setTimeout(() => {\r\n        setShowDragGuide(true);\r\n      }, 2000);\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, []);\r\n\r\n  // 关闭拖拽指南\r\n  const handleCloseDragGuide = useCallback(() => {\r\n    setShowDragGuide(false);\r\n    localStorage.setItem('chronospect-drag-guide-seen', 'true');\r\n  }, []);\r\n\r\n  // 处理鼠标按下开始拖拽\r\n  const handleMouseDown = useCallback((timeSlot: number, event: React.MouseEvent) => {\r\n    event.preventDefault();\r\n\r\n    // 检查是否可编辑\r\n    if (!isTimeSlotEditable(currentDate, timeSlot)) {\r\n      return; // 不可编辑的区块不允许拖拽\r\n    }\r\n\r\n    setDragStart(timeSlot);\r\n    setDragEnd(timeSlot);\r\n    setDragging(true, timeSlot);\r\n    setSelectedBlocks([timeSlot]);\r\n  }, [currentDate, setDragging, setSelectedBlocks]);\r\n\r\n  // 处理鼠标移动时的拖拽 - 集成智能方向检测\r\n  const handleMouseEnter = useCallback((timeSlot: number) => {\r\n    if (dragStart !== null && ui.isDragging) {\r\n      // 检查目标时间槽是否可编辑\r\n      if (!isTimeSlotEditable(currentDate, timeSlot)) {\r\n        return; // 不允许拖拽到不可编辑的区块\r\n      }\r\n\r\n      setDragEnd(timeSlot);\r\n\r\n      // 计算智能拖拽路径\r\n      const dragPath = calculateDragPath(dragStart, timeSlot);\r\n      setCurrentDragPath(dragPath);\r\n\r\n      // 过滤出可编辑的时间槽\r\n      const editableSlots = dragPath.selectedSlots.filter(slot =>\r\n        isTimeSlotEditable(currentDate, slot)\r\n      );\r\n\r\n      setSelectedBlocks(editableSlots);\r\n    }\r\n  }, [dragStart, ui.isDragging, currentDate, setSelectedBlocks]);\r\n\r\n  // 处理鼠标释放结束拖拽\r\n  const handleMouseUp = useCallback(() => {\r\n    // 先保存当前状态，避免状态更新导致的时序问题\r\n    const wasDragging = ui.isDragging;\r\n    const selectedCount = ui.selectedBlocks.length;\r\n\r\n    // 重置拖拽状态\r\n    setDragging(false);\r\n    setDragStart(null);\r\n    setDragEnd(null);\r\n    setCurrentDragPath(null);\r\n\r\n    // 如果是拖拽结束且有选中的块，显示活动选择面板\r\n    if (wasDragging && selectedCount > 0) {\r\n      setShowActivityPalette(true);\r\n    }\r\n  }, [ui.isDragging, ui.selectedBlocks.length, setShowActivityPalette, setDragging]);\r\n\r\n  // 处理单击时间块\r\n  const handleBlockClick = useCallback((timeSlot: number, event: React.MouseEvent) => {\r\n    event.stopPropagation();\r\n\r\n    // 检查是否可编辑\r\n    if (!isTimeSlotEditable(currentDate, timeSlot)) {\r\n      return; // 不可编辑的区块不响应点击\r\n    }\r\n\r\n    if (!ui.isDragging) {\r\n      setSelectedBlocks([timeSlot]);\r\n      setShowActivityPalette(true);\r\n    }\r\n  }, [ui.isDragging, currentDate, setSelectedBlocks, setShowActivityPalette]);\r\n\r\n  // 处理活动选择\r\n  const handleActivitySelect = useCallback((activityId: string | null) => {\r\n    if (ui.selectedBlocks.length > 0) {\r\n      updateMultipleTimeBlocks(currentDate, ui.selectedBlocks, activityId);\r\n      clearSelectedBlocks();\r\n      setShowActivityPalette(false);\r\n    }\r\n  }, [ui.selectedBlocks, currentDate, updateMultipleTimeBlocks, clearSelectedBlocks, setShowActivityPalette]);\r\n\r\n  // 处理点击空白区域\r\n  const handleBackgroundClick = useCallback((event: React.MouseEvent) => {\r\n    // 只有在点击的是背景元素本身时才关闭，避免干扰拖拽\r\n    if (event.target === event.currentTarget && !ui.isDragging) {\r\n      clearSelectedBlocks();\r\n      setShowActivityPalette(false);\r\n    }\r\n  }, [clearSelectedBlocks, setShowActivityPalette, ui.isDragging]);\r\n\r\n  // 全局鼠标事件监听\r\n  useEffect(() => {\r\n    const handleGlobalMouseUp = () => {\r\n      if (ui.isDragging) {\r\n        handleMouseUp();\r\n      }\r\n    };\r\n\r\n    document.addEventListener('mouseup', handleGlobalMouseUp);\r\n    return () => {\r\n      document.removeEventListener('mouseup', handleGlobalMouseUp);\r\n    };\r\n  }, [ui.isDragging, handleMouseUp]);\r\n\r\n  // 生成时间段标签 - 6个4小时时间段\r\n  const generateTimeSegmentLabels = () => {\r\n    const segments = [\r\n      {\r\n        range: '00-04',\r\n        period: '深夜',\r\n        hours: [0, 1, 2, 3],\r\n        theme: 'night'\r\n      },\r\n      {\r\n        range: '04-08',\r\n        period: '黎明',\r\n        hours: [4, 5, 6, 7],\r\n        theme: 'dawn'\r\n      },\r\n      {\r\n        range: '08-12',\r\n        period: '上午',\r\n        hours: [8, 9, 10, 11],\r\n        theme: 'morning'\r\n      },\r\n      {\r\n        range: '12-16',\r\n        period: '下午',\r\n        hours: [12, 13, 14, 15],\r\n        theme: 'afternoon'\r\n      },\r\n      {\r\n        range: '16-20',\r\n        period: '傍晚',\r\n        hours: [16, 17, 18, 19],\r\n        theme: 'evening'\r\n      },\r\n      {\r\n        range: '20-24',\r\n        period: '夜晚',\r\n        hours: [20, 21, 22, 23],\r\n        theme: 'twilight'\r\n      }\r\n    ];\r\n    return segments;\r\n  };\r\n\r\n  // 生成小时刻度标签\r\n  const generateHourLabels = () => {\r\n    const labels = [];\r\n    for (let hour = 0; hour < 24; hour += 4) {\r\n      const endHour = Math.min(hour + 4, 24);\r\n      labels.push({\r\n        start: hour,\r\n        end: endHour,\r\n        hours: Array.from({ length: 4 }, (_, i) => hour + i).filter(h => h < 24)\r\n      });\r\n    }\r\n    return labels;\r\n  };\r\n\r\n  const timeSegments = generateTimeSegmentLabels();\r\n  const hourLabels = generateHourLabels();\r\n\r\n  // 重新排列时间块以按列分时间段\r\n  const reorderTimeBlocksByColumn = (blocks: TimeBlockType[]): TimeBlockType[] => {\r\n    const reorderedBlocks: TimeBlockType[] = [];\r\n\r\n    // 6列×8行，每列代表一个4小时时间段\r\n    // 第1列: 00-04 (timeSlot 0-7)\r\n    // 第2列: 04-08 (timeSlot 8-15)\r\n    // 第3列: 08-12 (timeSlot 16-23)\r\n    // 第4列: 12-16 (timeSlot 24-31)\r\n    // 第5列: 16-20 (timeSlot 32-39)\r\n    // 第6列: 20-24 (timeSlot 40-47)\r\n\r\n    for (let row = 0; row < 8; row++) {\r\n      for (let col = 0; col < 6; col++) {\r\n        const timeSlot = col * 8 + row; // 按列计算timeSlot\r\n        const block = blocks.find(b => b.timeSlot === timeSlot);\r\n        if (block) {\r\n          reorderedBlocks.push(block);\r\n        }\r\n      }\r\n    }\r\n\r\n    return reorderedBlocks;\r\n  };\r\n\r\n  // 重新排列时间块\r\n  const reorderedTimeBlocks = reorderTimeBlocksByColumn(timeBlocks);\r\n\r\n  return (\r\n    <div className=\"w-full max-w-6xl mx-auto\" style={{ padding: 'var(--spacing-6)' }}>\r\n      <div\r\n        className=\"relative select-none\"\r\n        onClick={handleBackgroundClick}\r\n      >\r\n        {/* 时间段标签行 */}\r\n        <motion.div\r\n          className=\"grid grid-cols-6\"\r\n          style={{\r\n            gap: 'var(--spacing-3)',\r\n            marginBottom: 'var(--spacing-2)'\r\n          }}\r\n          initial={{ opacity: 0, y: -10 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}\r\n        >\r\n          {timeSegments.map((segment, index) => (\r\n            <motion.div\r\n              key={index}\r\n              className=\"text-center cursor-default\"\r\n              style={{\r\n                padding: 'var(--spacing-3)',\r\n                borderRadius: 'var(--radius-lg)',\r\n                background: `var(--time-segment-${segment.theme}-bg)`,\r\n                border: `2px solid var(--time-segment-${segment.theme}-border)`,\r\n                boxShadow: 'var(--shadow-sm)',\r\n                transition: 'all var(--duration-fast) var(--ease-out)'\r\n              }}\r\n              whileHover={{\r\n                scale: 1.02,\r\n                boxShadow: 'var(--shadow-md)',\r\n                y: -2\r\n              }}\r\n              transition={{ duration: 0.2, ease: [0.4, 0, 0.2, 1] }}\r\n            >\r\n              <div\r\n                style={{\r\n                  fontSize: 'var(--font-size-sm)',\r\n                  fontWeight: 'var(--font-weight-bold)',\r\n                  color: `var(--time-segment-${segment.theme}-text)`,\r\n                  marginBottom: 'var(--spacing-1)',\r\n                  textShadow: '0 1px 2px rgba(0, 0, 0, 0.1)'\r\n                }}\r\n              >\r\n                {segment.period}\r\n              </div>\r\n              <div\r\n                style={{\r\n                  fontSize: 'var(--font-size-xs)',\r\n                  color: `var(--time-segment-${segment.theme}-text)`,\r\n                  fontFamily: 'var(--font-mono)',\r\n                  opacity: 0.8\r\n                }}\r\n              >\r\n                {segment.range}\r\n              </div>\r\n            </motion.div>\r\n          ))}\r\n        </motion.div>\r\n\r\n        {/* 小时刻度行 */}\r\n        <motion.div\r\n          className=\"grid grid-cols-6\"\r\n          style={{\r\n            gap: 'var(--spacing-3)',\r\n            marginBottom: 'var(--spacing-4)'\r\n          }}\r\n          initial={{ opacity: 0, y: -5 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.3, delay: 0.1, ease: [0.4, 0, 0.2, 1] }}\r\n        >\r\n          {hourLabels.map((labelGroup, index) => (\r\n            <div\r\n              key={index}\r\n              className=\"flex justify-between\"\r\n              style={{\r\n                padding: '0 var(--spacing-1)'\r\n              }}\r\n            >\r\n              {labelGroup.hours.map((hour) => (\r\n                <div\r\n                  key={hour}\r\n                  style={{\r\n                    fontSize: 'var(--font-size-xs)',\r\n                    color: 'var(--neutral-400)',\r\n                    fontFamily: 'var(--font-mono)',\r\n                    fontWeight: 'var(--font-weight-medium)'\r\n                  }}\r\n                >\r\n                  {hour.toString().padStart(2, '0')}\r\n                </div>\r\n              ))}\r\n            </div>\r\n          ))}\r\n        </motion.div>\r\n\r\n        {/* 时间块网格 - 6列×8行布局 */}\r\n        <div className=\"relative\">\r\n\r\n          <motion.div\r\n            className=\"grid grid-cols-6 relative\"\r\n            style={{\r\n              gap: 'var(--spacing-2)',\r\n              rowGap: 'var(--spacing-2)'\r\n            }}\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.3, delay: 0.2, ease: [0.4, 0, 0.2, 1] }}\r\n          >\r\n          {/* 拖拽路径可视化 */}\r\n          <DragPathVisualizer\r\n            dragPath={currentDragPath}\r\n            isVisible={ui.isDragging}\r\n          />\r\n\r\n          {/* 时间线指示器 */}\r\n          <TimelineIndicator />\r\n\r\n\r\n\r\n          {reorderedTimeBlocks.map((block, index) => (\r\n            <motion.div\r\n              key={block.id}\r\n              initial={{ opacity: 0, scale: 0.8 }}\r\n              animate={{ opacity: 1, scale: 1 }}\r\n              transition={{\r\n                duration: 0.2,\r\n                delay: index * 0.01, // 错位动画\r\n                ease: [0.4, 0, 0.2, 1]\r\n              }}\r\n            >\r\n              <TimeBlock\r\n                block={block}\r\n                isSelected={ui.selectedBlocks.includes(block.timeSlot)}\r\n                onClick={(event) => handleBlockClick(block.timeSlot, event)}\r\n                onMouseDown={(event) => handleMouseDown(block.timeSlot, event)}\r\n                onMouseEnter={() => handleMouseEnter(block.timeSlot)}\r\n              />\r\n            </motion.div>\r\n          ))}\r\n          </motion.div>\r\n        </div>\r\n\r\n        {/* 活动选择面板 */}\r\n        {ui.showActivityPalette && (\r\n          <motion.div\r\n            initial={{ opacity: 0, scale: 0.9 }}\r\n            animate={{ opacity: 1, scale: 1 }}\r\n            exit={{ opacity: 0, scale: 0.9 }}\r\n            transition={{ duration: 0.2, ease: [0.4, 0, 0.2, 1] }}\r\n          >\r\n            <ActivityPalette\r\n              onActivitySelect={handleActivitySelect}\r\n              onClose={() => {\r\n                setShowActivityPalette(false);\r\n                clearSelectedBlocks();\r\n              }}\r\n              selectedBlocks={ui.selectedBlocks}\r\n            />\r\n          </motion.div>\r\n        )}\r\n\r\n\r\n\r\n        {/* 拖拽指南 */}\r\n        <DragGuide\r\n          isVisible={showDragGuide}\r\n          onClose={handleCloseDragGuide}\r\n        />\r\n\r\n        {/* 选择信息面板 */}\r\n        <SelectionInfoPanel\r\n          selectedBlocks={ui.selectedBlocks}\r\n          dragPath={currentDragPath}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAMA;AACA;AACA;AACA;AACA;AACA;AAlBA;;;;;;;;;;;;;AAqBO,SAAS;IACd,MAAM,EACJ,WAAW,EACX,oBAAoB,EACpB,EAAE,EACF,iBAAiB,EACjB,mBAAmB,EACnB,sBAAsB,EACtB,WAAW,EACX,wBAAwB,EACzB,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IAEd,MAAM,aAAa,qBAAqB;IACxC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IACxE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAGnD,qBAAqB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB,aAAa,OAAO,CAAC;QAC9C,IAAI,CAAC,kBAAkB;YACrB,iBAAiB;YACjB,MAAM,QAAQ,WAAW;gBACvB,iBAAiB;YACnB,GAAG;YACH,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG,EAAE;IAEL,SAAS;IACT,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvC,iBAAiB;QACjB,aAAa,OAAO,CAAC,+BAA+B;IACtD,GAAG,EAAE;IAEL,aAAa;IACb,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,UAAkB;QACrD,MAAM,cAAc;QAEpB,UAAU;QACV,IAAI,CAAC,CAAA,GAAA,yHAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa,WAAW;YAC9C,QAAQ,eAAe;QACzB;QAEA,aAAa;QACb,WAAW;QACX,YAAY,MAAM;QAClB,kBAAkB;YAAC;SAAS;IAC9B,GAAG;QAAC;QAAa;QAAa;KAAkB;IAEhD,wBAAwB;IACxB,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,IAAI,cAAc,QAAQ,GAAG,UAAU,EAAE;YACvC,eAAe;YACf,IAAI,CAAC,CAAA,GAAA,yHAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa,WAAW;gBAC9C,QAAQ,gBAAgB;YAC1B;YAEA,WAAW;YAEX,WAAW;YACX,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW;YAC9C,mBAAmB;YAEnB,aAAa;YACb,MAAM,gBAAgB,SAAS,aAAa,CAAC,MAAM,CAAC,CAAA,OAClD,CAAA,GAAA,yHAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa;YAGlC,kBAAkB;QACpB;IACF,GAAG;QAAC;QAAW,GAAG,UAAU;QAAE;QAAa;KAAkB;IAE7D,aAAa;IACb,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,wBAAwB;QACxB,MAAM,cAAc,GAAG,UAAU;QACjC,MAAM,gBAAgB,GAAG,cAAc,CAAC,MAAM;QAE9C,SAAS;QACT,YAAY;QACZ,aAAa;QACb,WAAW;QACX,mBAAmB;QAEnB,yBAAyB;QACzB,IAAI,eAAe,gBAAgB,GAAG;YACpC,uBAAuB;QACzB;IACF,GAAG;QAAC,GAAG,UAAU;QAAE,GAAG,cAAc,CAAC,MAAM;QAAE;QAAwB;KAAY;IAEjF,UAAU;IACV,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,UAAkB;QACtD,MAAM,eAAe;QAErB,UAAU;QACV,IAAI,CAAC,CAAA,GAAA,yHAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa,WAAW;YAC9C,QAAQ,eAAe;QACzB;QAEA,IAAI,CAAC,GAAG,UAAU,EAAE;YAClB,kBAAkB;gBAAC;aAAS;YAC5B,uBAAuB;QACzB;IACF,GAAG;QAAC,GAAG,UAAU;QAAE;QAAa;QAAmB;KAAuB;IAE1E,SAAS;IACT,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACxC,IAAI,GAAG,cAAc,CAAC,MAAM,GAAG,GAAG;YAChC,yBAAyB,aAAa,GAAG,cAAc,EAAE;YACzD;YACA,uBAAuB;QACzB;IACF,GAAG;QAAC,GAAG,cAAc;QAAE;QAAa;QAA0B;QAAqB;KAAuB;IAE1G,WAAW;IACX,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACzC,2BAA2B;QAC3B,IAAI,MAAM,MAAM,KAAK,MAAM,aAAa,IAAI,CAAC,GAAG,UAAU,EAAE;YAC1D;YACA,uBAAuB;QACzB;IACF,GAAG;QAAC;QAAqB;QAAwB,GAAG,UAAU;KAAC;IAE/D,WAAW;IACX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,sBAAsB;YAC1B,IAAI,GAAG,UAAU,EAAE;gBACjB;YACF;QACF;QAEA,SAAS,gBAAgB,CAAC,WAAW;QACrC,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;QAC1C;IACF,GAAG;QAAC,GAAG,UAAU;QAAE;KAAc;IAEjC,qBAAqB;IACrB,MAAM,4BAA4B;QAChC,MAAM,WAAW;YACf;gBACE,OAAO;gBACP,QAAQ;gBACR,OAAO;oBAAC;oBAAG;oBAAG;oBAAG;iBAAE;gBACnB,OAAO;YACT;YACA;gBACE,OAAO;gBACP,QAAQ;gBACR,OAAO;oBAAC;oBAAG;oBAAG;oBAAG;iBAAE;gBACnB,OAAO;YACT;YACA;gBACE,OAAO;gBACP,QAAQ;gBACR,OAAO;oBAAC;oBAAG;oBAAG;oBAAI;iBAAG;gBACrB,OAAO;YACT;YACA;gBACE,OAAO;gBACP,QAAQ;gBACR,OAAO;oBAAC;oBAAI;oBAAI;oBAAI;iBAAG;gBACvB,OAAO;YACT;YACA;gBACE,OAAO;gBACP,QAAQ;gBACR,OAAO;oBAAC;oBAAI;oBAAI;oBAAI;iBAAG;gBACvB,OAAO;YACT;YACA;gBACE,OAAO;gBACP,QAAQ;gBACR,OAAO;oBAAC;oBAAI;oBAAI;oBAAI;iBAAG;gBACvB,OAAO;YACT;SACD;QACD,OAAO;IACT;IAEA,WAAW;IACX,MAAM,qBAAqB;QACzB,MAAM,SAAS,EAAE;QACjB,IAAK,IAAI,OAAO,GAAG,OAAO,IAAI,QAAQ,EAAG;YACvC,MAAM,UAAU,KAAK,GAAG,CAAC,OAAO,GAAG;YACnC,OAAO,IAAI,CAAC;gBACV,OAAO;gBACP,KAAK;gBACL,OAAO,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAE,GAAG,CAAC,GAAG,IAAM,OAAO,GAAG,MAAM,CAAC,CAAA,IAAK,IAAI;YACvE;QACF;QACA,OAAO;IACT;IAEA,MAAM,eAAe;IACrB,MAAM,aAAa;IAEnB,iBAAiB;IACjB,MAAM,4BAA4B,CAAC;QACjC,MAAM,kBAAmC,EAAE;QAE3C,qBAAqB;QACrB,4BAA4B;QAC5B,6BAA6B;QAC7B,8BAA8B;QAC9B,8BAA8B;QAC9B,8BAA8B;QAC9B,8BAA8B;QAE9B,IAAK,IAAI,MAAM,GAAG,MAAM,GAAG,MAAO;YAChC,IAAK,IAAI,MAAM,GAAG,MAAM,GAAG,MAAO;gBAChC,MAAM,WAAW,MAAM,IAAI,KAAK,eAAe;gBAC/C,MAAM,QAAQ,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;gBAC9C,IAAI,OAAO;oBACT,gBAAgB,IAAI,CAAC;gBACvB;YACF;QACF;QAEA,OAAO;IACT;IAEA,UAAU;IACV,MAAM,sBAAsB,0BAA0B;IAEtD,qBACE,8OAAC;QAAI,WAAU;QAA2B,OAAO;YAAE,SAAS;QAAmB;kBAC7E,cAAA,8OAAC;YACC,WAAU;YACV,SAAS;;8BAGT,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,OAAO;wBACL,KAAK;wBACL,cAAc;oBAChB;oBACA,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,MAAM;4BAAC;4BAAK;4BAAG;4BAAK;yBAAE;oBAAC;8BAEnD,aAAa,GAAG,CAAC,CAAC,SAAS,sBAC1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,OAAO;gCACL,SAAS;gCACT,cAAc;gCACd,YAAY,CAAC,mBAAmB,EAAE,QAAQ,KAAK,CAAC,IAAI,CAAC;gCACrD,QAAQ,CAAC,6BAA6B,EAAE,QAAQ,KAAK,CAAC,QAAQ,CAAC;gCAC/D,WAAW;gCACX,YAAY;4BACd;4BACA,YAAY;gCACV,OAAO;gCACP,WAAW;gCACX,GAAG,CAAC;4BACN;4BACA,YAAY;gCAAE,UAAU;gCAAK,MAAM;oCAAC;oCAAK;oCAAG;oCAAK;iCAAE;4BAAC;;8CAEpD,8OAAC;oCACC,OAAO;wCACL,UAAU;wCACV,YAAY;wCACZ,OAAO,CAAC,mBAAmB,EAAE,QAAQ,KAAK,CAAC,MAAM,CAAC;wCAClD,cAAc;wCACd,YAAY;oCACd;8CAEC,QAAQ,MAAM;;;;;;8CAEjB,8OAAC;oCACC,OAAO;wCACL,UAAU;wCACV,OAAO,CAAC,mBAAmB,EAAE,QAAQ,KAAK,CAAC,MAAM,CAAC;wCAClD,YAAY;wCACZ,SAAS;oCACX;8CAEC,QAAQ,KAAK;;;;;;;2BApCX;;;;;;;;;;8BA2CX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,OAAO;wBACL,KAAK;wBACL,cAAc;oBAChB;oBACA,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAE;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;wBAAK,MAAM;4BAAC;4BAAK;4BAAG;4BAAK;yBAAE;oBAAC;8BAE/D,WAAW,GAAG,CAAC,CAAC,YAAY,sBAC3B,8OAAC;4BAEC,WAAU;4BACV,OAAO;gCACL,SAAS;4BACX;sCAEC,WAAW,KAAK,CAAC,GAAG,CAAC,CAAC,qBACrB,8OAAC;oCAEC,OAAO;wCACL,UAAU;wCACV,OAAO;wCACP,YAAY;wCACZ,YAAY;oCACd;8CAEC,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG;mCARxB;;;;;2BARJ;;;;;;;;;;8BAwBX,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,OAAO;4BACL,KAAK;4BACL,QAAQ;wBACV;wBACA,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;4BAAK,MAAM;gCAAC;gCAAK;gCAAG;gCAAK;6BAAE;wBAAC;;0CAGlE,8OAAC,iIAAA,CAAA,qBAAkB;gCACjB,UAAU;gCACV,WAAW,GAAG,UAAU;;;;;;0CAI1B,8OAAC,uIAAA,CAAA,oBAAiB;;;;;4BAIjB,oBAAoB,GAAG,CAAC,CAAC,OAAO,sBAC/B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,YAAY;wCACV,UAAU;wCACV,OAAO,QAAQ;wCACf,MAAM;4CAAC;4CAAK;4CAAG;4CAAK;yCAAE;oCACxB;8CAEA,cAAA,8OAAC,+HAAA,CAAA,YAAS;wCACR,OAAO;wCACP,YAAY,GAAG,cAAc,CAAC,QAAQ,CAAC,MAAM,QAAQ;wCACrD,SAAS,CAAC,QAAU,iBAAiB,MAAM,QAAQ,EAAE;wCACrD,aAAa,CAAC,QAAU,gBAAgB,MAAM,QAAQ,EAAE;wCACxD,cAAc,IAAM,iBAAiB,MAAM,QAAQ;;;;;;mCAdhD,MAAM,EAAE;;;;;;;;;;;;;;;;gBAsBlB,GAAG,mBAAmB,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAI;oBAClC,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAE;oBAChC,MAAM;wBAAE,SAAS;wBAAG,OAAO;oBAAI;oBAC/B,YAAY;wBAAE,UAAU;wBAAK,MAAM;4BAAC;4BAAK;4BAAG;4BAAK;yBAAE;oBAAC;8BAEpD,cAAA,8OAAC,qIAAA,CAAA,kBAAe;wBACd,kBAAkB;wBAClB,SAAS;4BACP,uBAAuB;4BACvB;wBACF;wBACA,gBAAgB,GAAG,cAAc;;;;;;;;;;;8BAQvC,8OAAC,+HAAA,CAAA,YAAS;oBACR,WAAW;oBACX,SAAS;;;;;;8BAIX,8OAAC,wIAAA,CAAA,qBAAkB;oBACjB,gBAAgB,GAAG,cAAc;oBACjC,UAAU;;;;;;;;;;;;;;;;;AAKpB", "debugId": null}}, {"offset": {"line": 5314, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/components/DailyStatsCard.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { DailyStats } from '@/types';\r\nimport { formatDuration } from '@/utils/timeUtils';\r\nimport { getUtilizationLevel, generateDailySummary } from '@/utils/statsUtils';\r\n\r\ninterface DailyStatsCardProps {\r\n  stats: DailyStats;\r\n}\r\n\r\nexport function DailyStatsCard({ stats }: DailyStatsCardProps) {\r\n  const utilizationLevel = getUtilizationLevel(stats.filledPercentage);\r\n  const summary = generateDailySummary(stats);\r\n\r\n  return (\r\n    <motion.div\r\n      className=\"card\"\r\n      style={{\r\n        background: 'white',\r\n        borderRadius: 'var(--radius-xl)',\r\n        boxShadow: 'var(--shadow-lg)',\r\n        border: '1px solid var(--neutral-200)',\r\n        padding: 'var(--spacing-6)'\r\n      }}\r\n      initial={{ opacity: 0, scale: 0.95 }}\r\n      animate={{ opacity: 1, scale: 1 }}\r\n      transition={{\r\n        duration: 0.3,\r\n        ease: [0.4, 0, 0.2, 1]\r\n      }}\r\n      whileHover={{\r\n        boxShadow: 'var(--shadow-xl)',\r\n        borderColor: 'var(--neutral-300)'\r\n      }}\r\n    >\r\n      {/* 标题和总结 */}\r\n      <div style={{ marginBottom: 'var(--spacing-6)' }}>\r\n        <h2 style={{\r\n          fontSize: 'var(--font-size-xl)',\r\n          fontWeight: 'var(--font-weight-semibold)',\r\n          color: 'var(--neutral-900)',\r\n          marginBottom: 'var(--spacing-2)',\r\n          lineHeight: 'var(--line-height-tight)'\r\n        }}>\r\n          📊 今日概览\r\n        </h2>\r\n        <p style={{\r\n          color: 'var(--neutral-600)',\r\n          fontSize: 'var(--font-size-base)',\r\n          lineHeight: 'var(--line-height-normal)'\r\n        }}>\r\n          {summary}\r\n        </p>\r\n      </div>\r\n\r\n      {/* 统计数据网格 */}\r\n      <div className=\"grid grid-cols-2 md:grid-cols-4\" style={{ gap: 'var(--spacing-4)' }}>\r\n        {/* 已记录时间 */}\r\n        <StatItem\r\n          label=\"已记录时间\"\r\n          value={formatDuration(stats.filledMinutes)}\r\n          icon=\"⏰\"\r\n          color=\"var(--primary-600)\"\r\n        />\r\n\r\n        {/* 空白时间 */}\r\n        <StatItem\r\n          label=\"空白时间\"\r\n          value={formatDuration(stats.unfilledMinutes)}\r\n          icon=\"⚪\"\r\n          color=\"var(--neutral-500)\"\r\n        />\r\n\r\n        {/* 活动数量 */}\r\n        <StatItem\r\n          label=\"活动类型\"\r\n          value={`${stats.activities.length} 种`}\r\n          icon=\"🎯\"\r\n          color=\"var(--success-600)\"\r\n        />\r\n\r\n        {/* 时间利用率 */}\r\n        <StatItem\r\n          label=\"时间利用率\"\r\n          value={`${stats.filledPercentage.toFixed(1)}%`}\r\n          icon=\"📈\"\r\n          color=\"var(--info-600)\"\r\n          badge={{\r\n            text: utilizationLevel.level,\r\n            color: utilizationLevel.color\r\n          }}\r\n        />\r\n      </div>\r\n\r\n      {/* 进度条 */}\r\n      <div style={{ marginTop: 'var(--spacing-6)' }}>\r\n        <div className=\"flex justify-between items-center\" style={{ marginBottom: 'var(--spacing-2)' }}>\r\n          <span style={{\r\n            fontSize: 'var(--font-size-sm)',\r\n            fontWeight: 'var(--font-weight-medium)',\r\n            color: 'var(--neutral-700)'\r\n          }}>\r\n            时间填充进度\r\n          </span>\r\n          <span style={{\r\n            fontSize: 'var(--font-size-sm)',\r\n            color: 'var(--neutral-500)'\r\n          }}>\r\n            {stats.filledMinutes} / {stats.totalMinutes} 分钟\r\n          </span>\r\n        </div>\r\n        <div style={{\r\n          width: '100%',\r\n          background: 'var(--neutral-200)',\r\n          borderRadius: 'var(--radius-full)',\r\n          height: '12px'\r\n        }}>\r\n          <motion.div\r\n            style={{\r\n              height: '12px',\r\n              borderRadius: 'var(--radius-full)',\r\n              background: 'linear-gradient(90deg, var(--primary-500), var(--info-500))'\r\n            }}\r\n            initial={{ width: 0 }}\r\n            animate={{ width: `${stats.filledPercentage}%` }}\r\n            transition={{ duration: 1, ease: \"easeOut\" }}\r\n          />\r\n        </div>\r\n        <div className=\"flex justify-between\" style={{\r\n          fontSize: 'var(--font-size-xs)',\r\n          color: 'var(--neutral-500)',\r\n          marginTop: 'var(--spacing-1)'\r\n        }}>\r\n          <span>0%</span>\r\n          <span>50%</span>\r\n          <span>100%</span>\r\n        </div>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n}\r\n\r\n// 统计项组件\r\ninterface StatItemProps {\r\n  label: string;\r\n  value: string;\r\n  icon: string;\r\n  color: string;\r\n  badge?: {\r\n    text: string;\r\n    color: string;\r\n  };\r\n}\r\n\r\nfunction StatItem({ label, value, icon, color, badge }: StatItemProps) {\r\n  return (\r\n    <motion.div\r\n      className=\"text-center\"\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}\r\n    >\r\n      <div style={{\r\n        fontSize: 'var(--font-size-2xl)',\r\n        marginBottom: 'var(--spacing-2)'\r\n      }}>\r\n        {icon}\r\n      </div>\r\n      <div style={{\r\n        fontSize: 'var(--font-size-2xl)',\r\n        fontWeight: 'var(--font-weight-bold)',\r\n        color: color,\r\n        marginBottom: 'var(--spacing-1)',\r\n        lineHeight: 'var(--line-height-tight)'\r\n      }}>\r\n        {value}\r\n      </div>\r\n      <div style={{\r\n        fontSize: 'var(--font-size-sm)',\r\n        color: 'var(--neutral-600)',\r\n        marginBottom: 'var(--spacing-2)',\r\n        lineHeight: 'var(--line-height-normal)'\r\n      }}>\r\n        {label}\r\n      </div>\r\n      {badge && (\r\n        <motion.div\r\n          style={{\r\n            display: 'inline-block',\r\n            padding: 'var(--spacing-1) var(--spacing-2)',\r\n            borderRadius: 'var(--radius-full)',\r\n            fontSize: 'var(--font-size-xs)',\r\n            fontWeight: 'var(--font-weight-medium)',\r\n            color: 'white',\r\n            backgroundColor: badge.color\r\n          }}\r\n          initial={{ scale: 0 }}\r\n          animate={{ scale: 1 }}\r\n          transition={{ delay: 0.2, duration: 0.2, ease: [0.68, -0.55, 0.265, 1.55] }}\r\n        >\r\n          {badge.text}\r\n        </motion.div>\r\n      )}\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AACA;AANA;;;;;AAYO,SAAS,eAAe,EAAE,KAAK,EAAuB;IAC3D,MAAM,mBAAmB,CAAA,GAAA,0HAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,gBAAgB;IACnE,MAAM,UAAU,CAAA,GAAA,0HAAA,CAAA,uBAAoB,AAAD,EAAE;IAErC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,OAAO;YACL,YAAY;YACZ,cAAc;YACd,WAAW;YACX,QAAQ;YACR,SAAS;QACX;QACA,SAAS;YAAE,SAAS;YAAG,OAAO;QAAK;QACnC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,YAAY;YACV,UAAU;YACV,MAAM;gBAAC;gBAAK;gBAAG;gBAAK;aAAE;QACxB;QACA,YAAY;YACV,WAAW;YACX,aAAa;QACf;;0BAGA,8OAAC;gBAAI,OAAO;oBAAE,cAAc;gBAAmB;;kCAC7C,8OAAC;wBAAG,OAAO;4BACT,UAAU;4BACV,YAAY;4BACZ,OAAO;4BACP,cAAc;4BACd,YAAY;wBACd;kCAAG;;;;;;kCAGH,8OAAC;wBAAE,OAAO;4BACR,OAAO;4BACP,UAAU;4BACV,YAAY;wBACd;kCACG;;;;;;;;;;;;0BAKL,8OAAC;gBAAI,WAAU;gBAAkC,OAAO;oBAAE,KAAK;gBAAmB;;kCAEhF,8OAAC;wBACC,OAAM;wBACN,OAAO,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,aAAa;wBACzC,MAAK;wBACL,OAAM;;;;;;kCAIR,8OAAC;wBACC,OAAM;wBACN,OAAO,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,eAAe;wBAC3C,MAAK;wBACL,OAAM;;;;;;kCAIR,8OAAC;wBACC,OAAM;wBACN,OAAO,GAAG,MAAM,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;wBACrC,MAAK;wBACL,OAAM;;;;;;kCAIR,8OAAC;wBACC,OAAM;wBACN,OAAO,GAAG,MAAM,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;wBAC9C,MAAK;wBACL,OAAM;wBACN,OAAO;4BACL,MAAM,iBAAiB,KAAK;4BAC5B,OAAO,iBAAiB,KAAK;wBAC/B;;;;;;;;;;;;0BAKJ,8OAAC;gBAAI,OAAO;oBAAE,WAAW;gBAAmB;;kCAC1C,8OAAC;wBAAI,WAAU;wBAAoC,OAAO;4BAAE,cAAc;wBAAmB;;0CAC3F,8OAAC;gCAAK,OAAO;oCACX,UAAU;oCACV,YAAY;oCACZ,OAAO;gCACT;0CAAG;;;;;;0CAGH,8OAAC;gCAAK,OAAO;oCACX,UAAU;oCACV,OAAO;gCACT;;oCACG,MAAM,aAAa;oCAAC;oCAAI,MAAM,YAAY;oCAAC;;;;;;;;;;;;;kCAGhD,8OAAC;wBAAI,OAAO;4BACV,OAAO;4BACP,YAAY;4BACZ,cAAc;4BACd,QAAQ;wBACV;kCACE,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,OAAO;gCACL,QAAQ;gCACR,cAAc;gCACd,YAAY;4BACd;4BACA,SAAS;gCAAE,OAAO;4BAAE;4BACpB,SAAS;gCAAE,OAAO,GAAG,MAAM,gBAAgB,CAAC,CAAC,CAAC;4BAAC;4BAC/C,YAAY;gCAAE,UAAU;gCAAG,MAAM;4BAAU;;;;;;;;;;;kCAG/C,8OAAC;wBAAI,WAAU;wBAAuB,OAAO;4BAC3C,UAAU;4BACV,OAAO;4BACP,WAAW;wBACb;;0CACE,8OAAC;0CAAK;;;;;;0CACN,8OAAC;0CAAK;;;;;;0CACN,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAKhB;AAcA,SAAS,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAiB;IACnE,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,MAAM;gBAAC;gBAAK;gBAAG;gBAAK;aAAE;QAAC;;0BAEpD,8OAAC;gBAAI,OAAO;oBACV,UAAU;oBACV,cAAc;gBAChB;0BACG;;;;;;0BAEH,8OAAC;gBAAI,OAAO;oBACV,UAAU;oBACV,YAAY;oBACZ,OAAO;oBACP,cAAc;oBACd,YAAY;gBACd;0BACG;;;;;;0BAEH,8OAAC;gBAAI,OAAO;oBACV,UAAU;oBACV,OAAO;oBACP,cAAc;oBACd,YAAY;gBACd;0BACG;;;;;;YAEF,uBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,OAAO;oBACL,SAAS;oBACT,SAAS;oBACT,cAAc;oBACd,UAAU;oBACV,YAAY;oBACZ,OAAO;oBACP,iBAAiB,MAAM,KAAK;gBAC9B;gBACA,SAAS;oBAAE,OAAO;gBAAE;gBACpB,SAAS;oBAAE,OAAO;gBAAE;gBACpB,YAAY;oBAAE,OAAO;oBAAK,UAAU;oBAAK,MAAM;wBAAC;wBAAM,CAAC;wBAAM;wBAAO;qBAAK;gBAAC;0BAEzE,MAAM,IAAI;;;;;;;;;;;;AAKrB", "debugId": null}}, {"offset": {"line": 5681, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/components/ActivityChart.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport {\r\n  Chart as ChartJS,\r\n  ArcElement,\r\n  CategoryScale,\r\n  LinearScale,\r\n  BarElement,\r\n  Title,\r\n  Tooltip,\r\n  Legend,\r\n} from 'chart.js';\r\nimport { Pie, Bar } from 'react-chartjs-2';\r\nimport { DailyStats } from '@/types';\r\nimport { formatDuration } from '@/utils/timeUtils';\r\n\r\n// 注册Chart.js组件\r\nChartJS.register(\r\n  ArcElement,\r\n  CategoryScale,\r\n  LinearScale,\r\n  BarElement,\r\n  Title,\r\n  Tooltip,\r\n  Legend\r\n);\r\n\r\ninterface ActivityChartProps {\r\n  stats: DailyStats;\r\n}\r\n\r\nexport function ActivityChart({ stats }: ActivityChartProps) {\r\n  // 如果没有活动数据，显示空状态\r\n  if (stats.activities.length === 0) {\r\n    return (\r\n      <motion.div\r\n        className=\"card\"\r\n        style={{\r\n          background: 'white',\r\n          borderRadius: 'var(--radius-xl)',\r\n          boxShadow: 'var(--shadow-lg)',\r\n          border: '1px solid var(--neutral-200)',\r\n          padding: 'var(--spacing-6)'\r\n        }}\r\n        initial={{ opacity: 0, scale: 0.95 }}\r\n        animate={{ opacity: 1, scale: 1 }}\r\n        transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}\r\n      >\r\n        <h3 style={{\r\n          fontSize: 'var(--font-size-lg)',\r\n          fontWeight: 'var(--font-weight-semibold)',\r\n          color: 'var(--neutral-900)',\r\n          marginBottom: 'var(--spacing-4)'\r\n        }}>\r\n          📊 活动分布\r\n        </h3>\r\n        <div className=\"text-center\" style={{ padding: 'var(--spacing-12) 0' }}>\r\n          <motion.div\r\n            style={{ fontSize: 'var(--font-size-4xl)', marginBottom: 'var(--spacing-4)' }}\r\n            initial={{ scale: 0 }}\r\n            animate={{ scale: 1 }}\r\n            transition={{ delay: 0.2, duration: 0.3, ease: [0.68, -0.55, 0.265, 1.55] }}\r\n          >\r\n            📈\r\n          </motion.div>\r\n          <p style={{ color: 'var(--neutral-500)', fontSize: 'var(--font-size-base)' }}>\r\n            暂无数据可显示\r\n          </p>\r\n        </div>\r\n      </motion.div>\r\n    );\r\n  }\r\n\r\n  // 准备饼图数据\r\n  const pieData = {\r\n    labels: stats.activities.map(activity => activity.activityName),\r\n    datasets: [\r\n      {\r\n        data: stats.activities.map(activity => activity.totalMinutes),\r\n        backgroundColor: stats.activities.map(activity => activity.color),\r\n        borderColor: stats.activities.map(activity => activity.color),\r\n        borderWidth: 2,\r\n        hoverBorderWidth: 3,\r\n      },\r\n    ],\r\n  };\r\n\r\n  // 准备条形图数据\r\n  const barData = {\r\n    labels: stats.activities.map(activity => activity.activityName),\r\n    datasets: [\r\n      {\r\n        label: '时间 (分钟)',\r\n        data: stats.activities.map(activity => activity.totalMinutes),\r\n        backgroundColor: stats.activities.map(activity => activity.color + '80'), // 添加透明度\r\n        borderColor: stats.activities.map(activity => activity.color),\r\n        borderWidth: 2,\r\n        borderRadius: 6,\r\n        borderSkipped: false,\r\n      },\r\n    ],\r\n  };\r\n\r\n  // 图表配置\r\n  const pieOptions = {\r\n    responsive: true,\r\n    maintainAspectRatio: false,\r\n    plugins: {\r\n      legend: {\r\n        position: 'bottom' as const,\r\n        labels: {\r\n          padding: 20,\r\n          usePointStyle: true,\r\n          font: {\r\n            size: 12,\r\n          },\r\n        },\r\n      },\r\n      tooltip: {\r\n        callbacks: {\r\n          label: function(context: any) {\r\n            const label = context.label || '';\r\n            const value = context.parsed;\r\n            const percentage = ((value / stats.filledMinutes) * 100).toFixed(1);\r\n            return `${label}: ${formatDuration(value)} (${percentage}%)`;\r\n          },\r\n        },\r\n      },\r\n    },\r\n  };\r\n\r\n  const barOptions = {\r\n    responsive: true,\r\n    maintainAspectRatio: false,\r\n    plugins: {\r\n      legend: {\r\n        display: false,\r\n      },\r\n      tooltip: {\r\n        callbacks: {\r\n          label: function(context: any) {\r\n            const value = context.parsed.y;\r\n            return `时间: ${formatDuration(value)}`;\r\n          },\r\n        },\r\n      },\r\n    },\r\n    scales: {\r\n      y: {\r\n        beginAtZero: true,\r\n        ticks: {\r\n          callback: function(value: any) {\r\n            return formatDuration(value);\r\n          },\r\n        },\r\n        grid: {\r\n          color: '#f3f4f6',\r\n        },\r\n      },\r\n      x: {\r\n        grid: {\r\n          display: false,\r\n        },\r\n        ticks: {\r\n          maxRotation: 45,\r\n          minRotation: 0,\r\n        },\r\n      },\r\n    },\r\n  };\r\n\r\n  return (\r\n    <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-6)' }}>\r\n      {/* 饼图 */}\r\n      <motion.div\r\n        className=\"card\"\r\n        style={{\r\n          background: 'white',\r\n          borderRadius: 'var(--radius-xl)',\r\n          boxShadow: 'var(--shadow-lg)',\r\n          border: '1px solid var(--neutral-200)',\r\n          padding: 'var(--spacing-6)'\r\n        }}\r\n        initial={{ opacity: 0, x: -20 }}\r\n        animate={{ opacity: 1, x: 0 }}\r\n        transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}\r\n        whileHover={{\r\n          boxShadow: 'var(--shadow-xl)',\r\n          borderColor: 'var(--neutral-300)'\r\n        }}\r\n      >\r\n        <h3 style={{\r\n          fontSize: 'var(--font-size-lg)',\r\n          fontWeight: 'var(--font-weight-semibold)',\r\n          color: 'var(--neutral-900)',\r\n          marginBottom: 'var(--spacing-4)'\r\n        }}>\r\n          🥧 活动时间分布\r\n        </h3>\r\n        <motion.div\r\n          style={{ height: '320px' }}\r\n          initial={{ scale: 0.8, opacity: 0 }}\r\n          animate={{ scale: 1, opacity: 1 }}\r\n          transition={{ delay: 0.2, duration: 0.4, ease: [0.4, 0, 0.2, 1] }}\r\n        >\r\n          <Pie data={pieData} options={pieOptions} />\r\n        </motion.div>\r\n      </motion.div>\r\n\r\n      {/* 条形图 */}\r\n      <motion.div\r\n        className=\"card\"\r\n        style={{\r\n          background: 'white',\r\n          borderRadius: 'var(--radius-xl)',\r\n          boxShadow: 'var(--shadow-lg)',\r\n          border: '1px solid var(--neutral-200)',\r\n          padding: 'var(--spacing-6)'\r\n        }}\r\n        initial={{ opacity: 0, x: -20 }}\r\n        animate={{ opacity: 1, x: 0 }}\r\n        transition={{ duration: 0.3, delay: 0.1, ease: [0.4, 0, 0.2, 1] }}\r\n        whileHover={{\r\n          boxShadow: 'var(--shadow-xl)',\r\n          borderColor: 'var(--neutral-300)'\r\n        }}\r\n      >\r\n        <h3 style={{\r\n          fontSize: 'var(--font-size-lg)',\r\n          fontWeight: 'var(--font-weight-semibold)',\r\n          color: 'var(--neutral-900)',\r\n          marginBottom: 'var(--spacing-4)'\r\n        }}>\r\n          📊 活动时长对比\r\n        </h3>\r\n        <motion.div\r\n          style={{ height: '320px' }}\r\n          initial={{ scale: 0.8, opacity: 0 }}\r\n          animate={{ scale: 1, opacity: 1 }}\r\n          transition={{ delay: 0.3, duration: 0.4, ease: [0.4, 0, 0.2, 1] }}\r\n        >\r\n          <Bar data={barData} options={barOptions} />\r\n        </motion.div>\r\n      </motion.div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAUA;AAEA;AAhBA;;;;;;AAkBA,eAAe;AACf,4JAAA,CAAA,QAAO,CAAC,QAAQ,CACd,4JAAA,CAAA,aAAU,EACV,4JAAA,CAAA,gBAAa,EACb,4JAAA,CAAA,cAAW,EACX,4JAAA,CAAA,aAAU,EACV,4JAAA,CAAA,QAAK,EACL,4JAAA,CAAA,UAAO,EACP,4JAAA,CAAA,SAAM;AAOD,SAAS,cAAc,EAAE,KAAK,EAAsB;IACzD,iBAAiB;IACjB,IAAI,MAAM,UAAU,CAAC,MAAM,KAAK,GAAG;QACjC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,OAAO;gBACL,YAAY;gBACZ,cAAc;gBACd,WAAW;gBACX,QAAQ;gBACR,SAAS;YACX;YACA,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAK;YACnC,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAE;YAChC,YAAY;gBAAE,UAAU;gBAAK,MAAM;oBAAC;oBAAK;oBAAG;oBAAK;iBAAE;YAAC;;8BAEpD,8OAAC;oBAAG,OAAO;wBACT,UAAU;wBACV,YAAY;wBACZ,OAAO;wBACP,cAAc;oBAChB;8BAAG;;;;;;8BAGH,8OAAC;oBAAI,WAAU;oBAAc,OAAO;wBAAE,SAAS;oBAAsB;;sCACnE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,OAAO;gCAAE,UAAU;gCAAwB,cAAc;4BAAmB;4BAC5E,SAAS;gCAAE,OAAO;4BAAE;4BACpB,SAAS;gCAAE,OAAO;4BAAE;4BACpB,YAAY;gCAAE,OAAO;gCAAK,UAAU;gCAAK,MAAM;oCAAC;oCAAM,CAAC;oCAAM;oCAAO;iCAAK;4BAAC;sCAC3E;;;;;;sCAGD,8OAAC;4BAAE,OAAO;gCAAE,OAAO;gCAAsB,UAAU;4BAAwB;sCAAG;;;;;;;;;;;;;;;;;;IAMtF;IAEA,SAAS;IACT,MAAM,UAAU;QACd,QAAQ,MAAM,UAAU,CAAC,GAAG,CAAC,CAAA,WAAY,SAAS,YAAY;QAC9D,UAAU;YACR;gBACE,MAAM,MAAM,UAAU,CAAC,GAAG,CAAC,CAAA,WAAY,SAAS,YAAY;gBAC5D,iBAAiB,MAAM,UAAU,CAAC,GAAG,CAAC,CAAA,WAAY,SAAS,KAAK;gBAChE,aAAa,MAAM,UAAU,CAAC,GAAG,CAAC,CAAA,WAAY,SAAS,KAAK;gBAC5D,aAAa;gBACb,kBAAkB;YACpB;SACD;IACH;IAEA,UAAU;IACV,MAAM,UAAU;QACd,QAAQ,MAAM,UAAU,CAAC,GAAG,CAAC,CAAA,WAAY,SAAS,YAAY;QAC9D,UAAU;YACR;gBACE,OAAO;gBACP,MAAM,MAAM,UAAU,CAAC,GAAG,CAAC,CAAA,WAAY,SAAS,YAAY;gBAC5D,iBAAiB,MAAM,UAAU,CAAC,GAAG,CAAC,CAAA,WAAY,SAAS,KAAK,GAAG;gBACnE,aAAa,MAAM,UAAU,CAAC,GAAG,CAAC,CAAA,WAAY,SAAS,KAAK;gBAC5D,aAAa;gBACb,cAAc;gBACd,eAAe;YACjB;SACD;IACH;IAEA,OAAO;IACP,MAAM,aAAa;QACjB,YAAY;QACZ,qBAAqB;QACrB,SAAS;YACP,QAAQ;gBACN,UAAU;gBACV,QAAQ;oBACN,SAAS;oBACT,eAAe;oBACf,MAAM;wBACJ,MAAM;oBACR;gBACF;YACF;YACA,SAAS;gBACP,WAAW;oBACT,OAAO,SAAS,OAAY;wBAC1B,MAAM,QAAQ,QAAQ,KAAK,IAAI;wBAC/B,MAAM,QAAQ,QAAQ,MAAM;wBAC5B,MAAM,aAAa,CAAC,AAAC,QAAQ,MAAM,aAAa,GAAI,GAAG,EAAE,OAAO,CAAC;wBACjE,OAAO,GAAG,MAAM,EAAE,EAAE,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,EAAE,EAAE,WAAW,EAAE,CAAC;oBAC9D;gBACF;YACF;QACF;IACF;IAEA,MAAM,aAAa;QACjB,YAAY;QACZ,qBAAqB;QACrB,SAAS;YACP,QAAQ;gBACN,SAAS;YACX;YACA,SAAS;gBACP,WAAW;oBACT,OAAO,SAAS,OAAY;wBAC1B,MAAM,QAAQ,QAAQ,MAAM,CAAC,CAAC;wBAC9B,OAAO,CAAC,IAAI,EAAE,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ;oBACvC;gBACF;YACF;QACF;QACA,QAAQ;YACN,GAAG;gBACD,aAAa;gBACb,OAAO;oBACL,UAAU,SAAS,KAAU;wBAC3B,OAAO,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE;oBACxB;gBACF;gBACA,MAAM;oBACJ,OAAO;gBACT;YACF;YACA,GAAG;gBACD,MAAM;oBACJ,SAAS;gBACX;gBACA,OAAO;oBACL,aAAa;oBACb,aAAa;gBACf;YACF;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,OAAO;YAAE,SAAS;YAAQ,eAAe;YAAU,KAAK;QAAmB;;0BAE9E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBACL,YAAY;oBACZ,cAAc;oBACd,WAAW;oBACX,QAAQ;oBACR,SAAS;gBACX;gBACA,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,MAAM;wBAAC;wBAAK;wBAAG;wBAAK;qBAAE;gBAAC;gBACpD,YAAY;oBACV,WAAW;oBACX,aAAa;gBACf;;kCAEA,8OAAC;wBAAG,OAAO;4BACT,UAAU;4BACV,YAAY;4BACZ,OAAO;4BACP,cAAc;wBAChB;kCAAG;;;;;;kCAGH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,OAAO;4BAAE,QAAQ;wBAAQ;wBACzB,SAAS;4BAAE,OAAO;4BAAK,SAAS;wBAAE;wBAClC,SAAS;4BAAE,OAAO;4BAAG,SAAS;wBAAE;wBAChC,YAAY;4BAAE,OAAO;4BAAK,UAAU;4BAAK,MAAM;gCAAC;gCAAK;gCAAG;gCAAK;6BAAE;wBAAC;kCAEhE,cAAA,8OAAC,sJAAA,CAAA,MAAG;4BAAC,MAAM;4BAAS,SAAS;;;;;;;;;;;;;;;;;0BAKjC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBACL,YAAY;oBACZ,cAAc;oBACd,WAAW;oBACX,QAAQ;oBACR,SAAS;gBACX;gBACA,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;oBAAK,MAAM;wBAAC;wBAAK;wBAAG;wBAAK;qBAAE;gBAAC;gBAChE,YAAY;oBACV,WAAW;oBACX,aAAa;gBACf;;kCAEA,8OAAC;wBAAG,OAAO;4BACT,UAAU;4BACV,YAAY;4BACZ,OAAO;4BACP,cAAc;wBAChB;kCAAG;;;;;;kCAGH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,OAAO;4BAAE,QAAQ;wBAAQ;wBACzB,SAAS;4BAAE,OAAO;4BAAK,SAAS;wBAAE;wBAClC,SAAS;4BAAE,OAAO;4BAAG,SAAS;wBAAE;wBAChC,YAAY;4BAAE,OAAO;4BAAK,UAAU;4BAAK,MAAM;gCAAC;gCAAK;gCAAG;gCAAK;6BAAE;wBAAC;kCAEhE,cAAA,8OAAC,sJAAA,CAAA,MAAG;4BAAC,MAAM;4BAAS,SAAS;;;;;;;;;;;;;;;;;;;;;;;AAKvC", "debugId": null}}, {"offset": {"line": 6080, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/components/ActivityList.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { DailyStats, ActivityStats } from '@/types';\r\nimport { formatDuration } from '@/utils/timeUtils';\r\nimport { getTimeRangesText, calculateEfficiencyScore } from '@/utils/statsUtils';\r\nimport * as LucideIcons from 'lucide-react';\r\n\r\ninterface ActivityListProps {\r\n  stats: DailyStats;\r\n}\r\n\r\nexport function ActivityList({ stats }: ActivityListProps) {\r\n  // 如果没有活动数据，显示空状态\r\n  if (stats.activities.length === 0) {\r\n    return (\r\n      <div className=\"bg-white rounded-xl shadow-lg border border-gray-200 p-6\">\r\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\r\n          📋 活动详情\r\n        </h3>\r\n        <div className=\"text-center py-12\">\r\n          <div className=\"text-4xl mb-4\">📝</div>\r\n          <p className=\"text-gray-500\">暂无活动记录</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* 活动排行榜 */}\r\n      <motion.div\r\n        className=\"bg-white rounded-xl shadow-lg border border-gray-200 p-6\"\r\n        initial={{ opacity: 0, x: 20 }}\r\n        animate={{ opacity: 1, x: 0 }}\r\n        transition={{ duration: 0.3 }}\r\n      >\r\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\r\n          🏆 活动排行榜\r\n        </h3>\r\n        <div className=\"space-y-3\">\r\n          {stats.activities.map((activity, index) => (\r\n            <ActivityRankItem\r\n              key={activity.activityId}\r\n              activity={activity}\r\n              rank={index + 1}\r\n              totalFilledMinutes={stats.filledMinutes}\r\n            />\r\n          ))}\r\n        </div>\r\n      </motion.div>\r\n\r\n      {/* 时间段详情 */}\r\n      <motion.div\r\n        className=\"bg-white rounded-xl shadow-lg border border-gray-200 p-6\"\r\n        initial={{ opacity: 0, x: 20 }}\r\n        animate={{ opacity: 1, x: 0 }}\r\n        transition={{ duration: 0.3, delay: 0.1 }}\r\n      >\r\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\r\n          ⏰ 时间段详情\r\n        </h3>\r\n        <div className=\"space-y-4\">\r\n          {stats.activities.map((activity) => (\r\n            <ActivityTimeDetail\r\n              key={activity.activityId}\r\n              activity={activity}\r\n            />\r\n          ))}\r\n        </div>\r\n      </motion.div>\r\n    </div>\r\n  );\r\n}\r\n\r\n// 活动排行项组件\r\ninterface ActivityRankItemProps {\r\n  activity: ActivityStats;\r\n  rank: number;\r\n  totalFilledMinutes: number;\r\n}\r\n\r\nfunction ActivityRankItem({ activity, rank, totalFilledMinutes }: ActivityRankItemProps) {\r\n  const percentage = totalFilledMinutes > 0 ? (activity.totalMinutes / totalFilledMinutes) * 100 : 0;\r\n  \r\n  // 获取排名图标\r\n  const getRankIcon = (rank: number) => {\r\n    switch (rank) {\r\n      case 1: return '🥇';\r\n      case 2: return '🥈';\r\n      case 3: return '🥉';\r\n      default: return `${rank}`;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <motion.div\r\n      className=\"flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors\"\r\n      whileHover={{ scale: 1.02 }}\r\n      transition={{ type: \"spring\", stiffness: 300, damping: 20 }}\r\n    >\r\n      <div className=\"flex items-center space-x-4\">\r\n        {/* 排名 */}\r\n        <div className=\"text-2xl font-bold w-8 text-center\">\r\n          {getRankIcon(rank)}\r\n        </div>\r\n        \r\n        {/* 活动信息 */}\r\n        <div className=\"flex items-center space-x-3\">\r\n          <div\r\n            className=\"w-4 h-4 rounded-full\"\r\n            style={{ backgroundColor: activity.color }}\r\n          />\r\n          <div>\r\n            <div className=\"font-medium text-gray-900\">\r\n              {activity.activityName}\r\n            </div>\r\n            <div className=\"text-sm text-gray-500\">\r\n              {activity.timeRanges.length} 个时间段\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 时间和百分比 */}\r\n      <div className=\"text-right\">\r\n        <div className=\"font-semibold text-gray-900\">\r\n          {formatDuration(activity.totalMinutes)}\r\n        </div>\r\n        <div className=\"text-sm text-gray-500\">\r\n          {percentage.toFixed(1)}%\r\n        </div>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n}\r\n\r\n// 活动时间详情组件\r\ninterface ActivityTimeDetailProps {\r\n  activity: ActivityStats;\r\n}\r\n\r\nfunction ActivityTimeDetail({ activity }: ActivityTimeDetailProps) {\r\n  const efficiencyScore = calculateEfficiencyScore(activity.timeRanges, activity.totalMinutes);\r\n  \r\n  return (\r\n    <div className=\"border border-gray-200 rounded-lg p-4\">\r\n      {/* 活动标题 */}\r\n      <div className=\"flex items-center justify-between mb-3\">\r\n        <div className=\"flex items-center space-x-3\">\r\n          <div\r\n            className=\"w-3 h-3 rounded-full\"\r\n            style={{ backgroundColor: activity.color }}\r\n          />\r\n          <h4 className=\"font-medium text-gray-900\">\r\n            {activity.activityName}\r\n          </h4>\r\n        </div>\r\n        <div className=\"text-sm text-gray-500\">\r\n          {formatDuration(activity.totalMinutes)}\r\n        </div>\r\n      </div>\r\n\r\n      {/* 时间段列表 */}\r\n      <div className=\"space-y-2\">\r\n        {activity.timeRanges.map((range, index) => (\r\n          <div\r\n            key={index}\r\n            className=\"flex items-center justify-between text-sm bg-gray-50 rounded px-3 py-2\"\r\n          >\r\n            <span className=\"text-gray-700\">\r\n              {range.startTime} - {range.endTime}\r\n            </span>\r\n            <span className=\"text-gray-500\">\r\n              {formatDuration(range.duration)}\r\n            </span>\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n      {/* 效率指标 */}\r\n      <div className=\"mt-3 pt-3 border-t border-gray-100\">\r\n        <div className=\"flex items-center justify-between text-sm\">\r\n          <span className=\"text-gray-600\">连续性评分</span>\r\n          <div className=\"flex items-center space-x-2\">\r\n            <div className=\"w-16 bg-gray-200 rounded-full h-2\">\r\n              <div\r\n                className=\"h-2 rounded-full bg-gradient-to-r from-yellow-400 to-green-500\"\r\n                style={{ width: `${efficiencyScore}%` }}\r\n              />\r\n            </div>\r\n            <span className=\"text-gray-700 font-medium\">\r\n              {efficiencyScore.toFixed(0)}%\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AACA;AANA;;;;;AAaO,SAAS,aAAa,EAAE,KAAK,EAAqB;IACvD,iBAAiB;IACjB,IAAI,MAAM,UAAU,CAAC,MAAM,KAAK,GAAG;QACjC,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA2C;;;;;;8BAGzD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;;kCAE5B,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,8OAAC;wBAAI,WAAU;kCACZ,MAAM,UAAU,CAAC,GAAG,CAAC,CAAC,UAAU,sBAC/B,8OAAC;gCAEC,UAAU;gCACV,MAAM,QAAQ;gCACd,oBAAoB,MAAM,aAAa;+BAHlC,SAAS,UAAU;;;;;;;;;;;;;;;;0BAUhC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;;kCAExC,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,8OAAC;wBAAI,WAAU;kCACZ,MAAM,UAAU,CAAC,GAAG,CAAC,CAAC,yBACrB,8OAAC;gCAEC,UAAU;+BADL,SAAS,UAAU;;;;;;;;;;;;;;;;;;;;;;AAQtC;AASA,SAAS,iBAAiB,EAAE,QAAQ,EAAE,IAAI,EAAE,kBAAkB,EAAyB;IACrF,MAAM,aAAa,qBAAqB,IAAI,AAAC,SAAS,YAAY,GAAG,qBAAsB,MAAM;IAEjG,SAAS;IACT,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBAAG,OAAO;YACf,KAAK;gBAAG,OAAO;YACf,KAAK;gBAAG,OAAO;YACf;gBAAS,OAAO,GAAG,MAAM;QAC3B;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,YAAY;YAAE,OAAO;QAAK;QAC1B,YAAY;YAAE,MAAM;YAAU,WAAW;YAAK,SAAS;QAAG;;0BAE1D,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACZ,YAAY;;;;;;kCAIf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,iBAAiB,SAAS,KAAK;gCAAC;;;;;;0CAE3C,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDACZ,SAAS,YAAY;;;;;;kDAExB,8OAAC;wCAAI,WAAU;;4CACZ,SAAS,UAAU,CAAC,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;;0BAOpC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACZ,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,YAAY;;;;;;kCAEvC,8OAAC;wBAAI,WAAU;;4BACZ,WAAW,OAAO,CAAC;4BAAG;;;;;;;;;;;;;;;;;;;AAKjC;AAOA,SAAS,mBAAmB,EAAE,QAAQ,EAA2B;IAC/D,MAAM,kBAAkB,CAAA,GAAA,0HAAA,CAAA,2BAAwB,AAAD,EAAE,SAAS,UAAU,EAAE,SAAS,YAAY;IAE3F,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,iBAAiB,SAAS,KAAK;gCAAC;;;;;;0CAE3C,8OAAC;gCAAG,WAAU;0CACX,SAAS,YAAY;;;;;;;;;;;;kCAG1B,8OAAC;wBAAI,WAAU;kCACZ,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,YAAY;;;;;;;;;;;;0BAKzC,8OAAC;gBAAI,WAAU;0BACZ,SAAS,UAAU,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC/B,8OAAC;wBAEC,WAAU;;0CAEV,8OAAC;gCAAK,WAAU;;oCACb,MAAM,SAAS;oCAAC;oCAAI,MAAM,OAAO;;;;;;;0CAEpC,8OAAC;gCAAK,WAAU;0CACb,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,QAAQ;;;;;;;uBAP3B;;;;;;;;;;0BAcX,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;sCAAgB;;;;;;sCAChC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO,GAAG,gBAAgB,CAAC,CAAC;wCAAC;;;;;;;;;;;8CAG1C,8OAAC;oCAAK,WAAU;;wCACb,gBAAgB,OAAO,CAAC;wCAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1C", "debugId": null}}, {"offset": {"line": 6516, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/components/DateComparison.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useMemo, useEffect } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { TrendingUp, TrendingDown, Minus } from 'lucide-react';\r\nimport { useAppStore } from '@/stores/useAppStore';\r\nimport { formatDate, getAdjacentDate } from '@/utils/timeUtils';\r\nimport { calculateDailyStats } from '@/utils/statsUtils';\r\nimport { DailyStats } from '@/types';\r\nimport { CustomDatePicker } from './CustomDatePicker';\r\n\r\ninterface DateComparisonProps {\r\n  currentDate: string;\r\n}\r\n\r\nexport function DateComparison({ currentDate }: DateComparisonProps) {\r\n  const {\r\n    getDailyStatsSync,\r\n    getDailyStatsAsync,\r\n    getTimeBlocksForDate,\r\n    activities\r\n  } = useAppStore();\r\n\r\n  const [compareDate, setCompareDate] = useState(() =>\r\n    getAdjacentDate(currentDate, -1) // 默认对比昨天\r\n  );\r\n  const [isExpanded, setIsExpanded] = useState(false);\r\n\r\n  // 预加载数据 - 在useEffect中进行，避免渲染中的副作用\r\n  useEffect(() => {\r\n    getDailyStatsAsync(currentDate);\r\n    getDailyStatsAsync(compareDate);\r\n  }, [currentDate, compareDate, getDailyStatsAsync]);\r\n\r\n  // 安全获取统计数据 - 使用useMemo缓存计算结果\r\n  const currentStats = useMemo(() => {\r\n    // 首先尝试从缓存获取\r\n    const cached = getDailyStatsSync(currentDate);\r\n    if (cached) return cached;\r\n\r\n    // 如果没有缓存，进行本地计算但不更新全局状态\r\n    const timeBlocks = getTimeBlocksForDate(currentDate);\r\n    return calculateDailyStats(timeBlocks, activities);\r\n  }, [currentDate, getDailyStatsSync, getTimeBlocksForDate, activities]);\r\n\r\n  const compareStats = useMemo(() => {\r\n    // 首先尝试从缓存获取\r\n    const cached = getDailyStatsSync(compareDate);\r\n    if (cached) return cached;\r\n\r\n    // 如果没有缓存，进行本地计算但不更新全局状态\r\n    const timeBlocks = getTimeBlocksForDate(compareDate);\r\n    return calculateDailyStats(timeBlocks, activities);\r\n  }, [compareDate, getDailyStatsSync, getTimeBlocksForDate, activities]);\r\n\r\n  // 计算对比指标\r\n  const comparison = useMemo(() => {\r\n    const filledMinutesDiff = currentStats.filledMinutes - compareStats.filledMinutes;\r\n    const filledPercentageDiff = currentStats.filledPercentage - compareStats.filledPercentage;\r\n    const activitiesDiff = currentStats.activities.length - compareStats.activities.length;\r\n\r\n    return {\r\n      filledMinutes: {\r\n        value: filledMinutesDiff,\r\n        percentage: compareStats.filledMinutes > 0 \r\n          ? (filledMinutesDiff / compareStats.filledMinutes) * 100 \r\n          : 0,\r\n        trend: filledMinutesDiff > 0 ? 'up' : filledMinutesDiff < 0 ? 'down' : 'same'\r\n      },\r\n      filledPercentage: {\r\n        value: filledPercentageDiff,\r\n        trend: filledPercentageDiff > 0 ? 'up' : filledPercentageDiff < 0 ? 'down' : 'same'\r\n      },\r\n      activities: {\r\n        value: activitiesDiff,\r\n        trend: activitiesDiff > 0 ? 'up' : activitiesDiff < 0 ? 'down' : 'same'\r\n      }\r\n    };\r\n  }, [currentStats, compareStats]);\r\n\r\n  // 获取趋势图标\r\n  const getTrendIcon = (trend: 'up' | 'down' | 'same') => {\r\n    switch (trend) {\r\n      case 'up':\r\n        return <TrendingUp size={16} className=\"text-green-500\" />;\r\n      case 'down':\r\n        return <TrendingDown size={16} className=\"text-red-500\" />;\r\n      default:\r\n        return <Minus size={16} className=\"text-gray-500\" />;\r\n    }\r\n  };\r\n\r\n  // 获取趋势颜色\r\n  const getTrendColor = (trend: 'up' | 'down' | 'same') => {\r\n    switch (trend) {\r\n      case 'up':\r\n        return 'var(--success-600)';\r\n      case 'down':\r\n        return 'var(--error-600)';\r\n      default:\r\n        return 'var(--neutral-500)';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <motion.div\r\n      className=\"card\"\r\n      style={{\r\n        background: 'white',\r\n        borderRadius: 'var(--radius-xl)',\r\n        boxShadow: 'var(--shadow-lg)',\r\n        border: '1px solid var(--neutral-200)',\r\n        padding: 'var(--spacing-6)'\r\n      }}\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}\r\n      whileHover={{\r\n        boxShadow: 'var(--shadow-xl)',\r\n        borderColor: 'var(--neutral-300)'\r\n      }}\r\n    >\r\n      {/* 标题和展开按钮 */}\r\n      <div className=\"flex items-center justify-between\" style={{ marginBottom: 'var(--spacing-4)' }}>\r\n        <h3 style={{\r\n          fontSize: 'var(--font-size-lg)',\r\n          fontWeight: 'var(--font-weight-semibold)',\r\n          color: 'var(--neutral-900)',\r\n          display: 'flex',\r\n          alignItems: 'center'\r\n        }}>\r\n          📊 日期对比分析\r\n        </h3>\r\n        <motion.button\r\n          onClick={() => setIsExpanded(!isExpanded)}\r\n          style={{\r\n            color: 'var(--primary-500)',\r\n            fontSize: 'var(--font-size-sm)',\r\n            fontWeight: 'var(--font-weight-medium)',\r\n            background: 'none',\r\n            border: 'none',\r\n            cursor: 'pointer'\r\n          }}\r\n          whileHover={{\r\n            scale: 1.05,\r\n            color: 'var(--primary-600)'\r\n          }}\r\n          whileTap={{ scale: 0.95 }}\r\n        >\r\n          {isExpanded ? '收起' : '展开详情'}\r\n        </motion.button>\r\n      </div>\r\n\r\n      {/* 美观的日期选择器 */}\r\n      <div className=\"flex items-center\" style={{\r\n        gap: 'var(--spacing-4)',\r\n        marginBottom: 'var(--spacing-6)',\r\n        flexWrap: 'wrap'\r\n      }}>\r\n        <div className=\"flex items-center\" style={{ gap: 'var(--spacing-2)' }}>\r\n          <span style={{\r\n            fontSize: 'var(--font-size-sm)',\r\n            color: 'var(--neutral-600)',\r\n            fontWeight: 'var(--font-weight-medium)'\r\n          }}>\r\n            对比日期:\r\n          </span>\r\n        </div>\r\n        <CustomDatePicker\r\n          value={compareDate}\r\n          onChange={setCompareDate}\r\n          className=\"flex-shrink-0\"\r\n        />\r\n        <span style={{\r\n          fontSize: 'var(--font-size-sm)',\r\n          color: 'var(--neutral-500)',\r\n          fontWeight: 'var(--font-weight-medium)'\r\n        }}>\r\n          vs {formatDate(currentDate)}\r\n        </span>\r\n      </div>\r\n\r\n      {/* 核心对比指标 */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-3\" style={{\r\n        gap: 'var(--spacing-4)',\r\n        marginBottom: 'var(--spacing-6)'\r\n      }}>\r\n        {/* 记录时间对比 */}\r\n        <motion.div\r\n          style={{\r\n            background: 'var(--neutral-50)',\r\n            borderRadius: 'var(--radius-lg)',\r\n            padding: 'var(--spacing-4)',\r\n            border: '1px solid var(--neutral-200)'\r\n          }}\r\n          whileHover={{\r\n            background: 'var(--neutral-100)',\r\n            borderColor: 'var(--neutral-300)'\r\n          }}\r\n          transition={{ duration: 0.15 }}\r\n        >\r\n          <div className=\"flex items-center justify-between\" style={{ marginBottom: 'var(--spacing-2)' }}>\r\n            <span style={{\r\n              fontSize: 'var(--font-size-sm)',\r\n              fontWeight: 'var(--font-weight-medium)',\r\n              color: 'var(--neutral-700)'\r\n            }}>\r\n              记录时间\r\n            </span>\r\n            {getTrendIcon(comparison.filledMinutes.trend)}\r\n          </div>\r\n          <div style={{\r\n            fontSize: 'var(--font-size-lg)',\r\n            fontWeight: 'var(--font-weight-semibold)',\r\n            color: 'var(--neutral-900)'\r\n          }}>\r\n            {Math.abs(comparison.filledMinutes.value)} 分钟\r\n          </div>\r\n          <div style={{\r\n            fontSize: 'var(--font-size-sm)',\r\n            color: getTrendColor(comparison.filledMinutes.trend)\r\n          }}>\r\n            {comparison.filledMinutes.trend === 'up' ? '+' : comparison.filledMinutes.trend === 'down' ? '-' : ''}\r\n            {Math.abs(comparison.filledMinutes.percentage).toFixed(1)}%\r\n          </div>\r\n        </motion.div>\r\n\r\n        {/* 时间利用率对比 */}\r\n        <motion.div\r\n          style={{\r\n            background: 'var(--neutral-50)',\r\n            borderRadius: 'var(--radius-lg)',\r\n            padding: 'var(--spacing-4)',\r\n            border: '1px solid var(--neutral-200)'\r\n          }}\r\n          whileHover={{\r\n            background: 'var(--neutral-100)',\r\n            borderColor: 'var(--neutral-300)'\r\n          }}\r\n          transition={{ duration: 0.15 }}\r\n        >\r\n          <div className=\"flex items-center justify-between\" style={{ marginBottom: 'var(--spacing-2)' }}>\r\n            <span style={{\r\n              fontSize: 'var(--font-size-sm)',\r\n              fontWeight: 'var(--font-weight-medium)',\r\n              color: 'var(--neutral-700)'\r\n            }}>\r\n              时间利用率\r\n            </span>\r\n            {getTrendIcon(comparison.filledPercentage.trend)}\r\n          </div>\r\n          <div style={{\r\n            fontSize: 'var(--font-size-lg)',\r\n            fontWeight: 'var(--font-weight-semibold)',\r\n            color: 'var(--neutral-900)'\r\n          }}>\r\n            {Math.abs(comparison.filledPercentage.value).toFixed(1)}%\r\n          </div>\r\n          <div style={{\r\n            fontSize: 'var(--font-size-sm)',\r\n            color: getTrendColor(comparison.filledPercentage.trend)\r\n          }}>\r\n            {comparison.filledPercentage.trend === 'up' ? '提升' : comparison.filledPercentage.trend === 'down' ? '下降' : '持平'}\r\n          </div>\r\n        </motion.div>\r\n\r\n        {/* 活动类型对比 */}\r\n        <motion.div\r\n          style={{\r\n            background: 'var(--neutral-50)',\r\n            borderRadius: 'var(--radius-lg)',\r\n            padding: 'var(--spacing-4)',\r\n            border: '1px solid var(--neutral-200)'\r\n          }}\r\n          whileHover={{\r\n            background: 'var(--neutral-100)',\r\n            borderColor: 'var(--neutral-300)'\r\n          }}\r\n          transition={{ duration: 0.15 }}\r\n        >\r\n          <div className=\"flex items-center justify-between\" style={{ marginBottom: 'var(--spacing-2)' }}>\r\n            <span style={{\r\n              fontSize: 'var(--font-size-sm)',\r\n              fontWeight: 'var(--font-weight-medium)',\r\n              color: 'var(--neutral-700)'\r\n            }}>\r\n              活动类型\r\n            </span>\r\n            {getTrendIcon(comparison.activities.trend)}\r\n          </div>\r\n          <div style={{\r\n            fontSize: 'var(--font-size-lg)',\r\n            fontWeight: 'var(--font-weight-semibold)',\r\n            color: 'var(--neutral-900)'\r\n          }}>\r\n            {Math.abs(comparison.activities.value)} 种\r\n          </div>\r\n          <div style={{\r\n            fontSize: 'var(--font-size-sm)',\r\n            color: getTrendColor(comparison.activities.trend)\r\n          }}>\r\n            {comparison.activities.trend === 'up' ? '增加' : comparison.activities.trend === 'down' ? '减少' : '相同'}\r\n          </div>\r\n        </motion.div>\r\n      </div>\r\n\r\n      {/* 展开的详细对比 */}\r\n      {isExpanded && (\r\n        <motion.div\r\n          initial={{ opacity: 0, height: 0 }}\r\n          animate={{ opacity: 1, height: 'auto' }}\r\n          exit={{ opacity: 0, height: 0 }}\r\n          transition={{ duration: 0.3 }}\r\n          className=\"border-t border-gray-200 pt-6\"\r\n        >\r\n          {/* 活动对比表格 */}\r\n          <div className=\"mb-6\">\r\n            <h4 className=\"text-md font-medium text-gray-900 mb-3\">活动详细对比</h4>\r\n            <div className=\"overflow-x-auto\">\r\n              <table className=\"w-full text-sm\">\r\n                <thead>\r\n                  <tr className=\"border-b border-gray-200\">\r\n                    <th className=\"text-left py-2 text-gray-600\">活动</th>\r\n                    <th className=\"text-right py-2 text-gray-600\">{formatDate(compareDate)}</th>\r\n                    <th className=\"text-right py-2 text-gray-600\">{formatDate(currentDate)}</th>\r\n                    <th className=\"text-right py-2 text-gray-600\">变化</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  {getActivityComparison(currentStats, compareStats).map((item, index) => (\r\n                    <tr key={index} className=\"border-b border-gray-100\">\r\n                      <td className=\"py-2 flex items-center space-x-2\">\r\n                        <div \r\n                          className=\"w-3 h-3 rounded-full\" \r\n                          style={{ backgroundColor: item.color }}\r\n                        />\r\n                        <span>{item.name}</span>\r\n                      </td>\r\n                      <td className=\"text-right py-2 text-gray-600\">\r\n                        {item.compareMinutes} 分钟\r\n                      </td>\r\n                      <td className=\"text-right py-2 text-gray-900 font-medium\">\r\n                        {item.currentMinutes} 分钟\r\n                      </td>\r\n                      <td className={`text-right py-2 ${getTrendColor(item.trend)}`}>\r\n                        {item.trend === 'up' ? '+' : item.trend === 'down' ? '-' : ''}\r\n                        {Math.abs(item.diff)} 分钟\r\n                      </td>\r\n                    </tr>\r\n                  ))}\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n          </div>\r\n\r\n          {/* 洞察总结 */}\r\n          <div className=\"bg-blue-50 rounded-lg p-4\">\r\n            <h4 className=\"text-md font-medium text-blue-900 mb-2\">📝 对比洞察</h4>\r\n            <p className=\"text-sm text-blue-800\">\r\n              {generateComparisonInsight(comparison, formatDate(compareDate), formatDate(currentDate))}\r\n            </p>\r\n          </div>\r\n        </motion.div>\r\n      )}\r\n    </motion.div>\r\n  );\r\n}\r\n\r\n// 获取活动对比数据\r\nfunction getActivityComparison(currentStats: DailyStats, compareStats: DailyStats) {\r\n  const allActivities = new Map<string, {\r\n    name: string;\r\n    color: string;\r\n    currentMinutes: number;\r\n    compareMinutes: number;\r\n  }>();\r\n\r\n  // 添加当前日期的活动\r\n  currentStats.activities.forEach(activity => {\r\n    allActivities.set(activity.activityId, {\r\n      name: activity.activityName,\r\n      color: activity.color,\r\n      currentMinutes: activity.totalMinutes,\r\n      compareMinutes: 0\r\n    });\r\n  });\r\n\r\n  // 添加对比日期的活动\r\n  compareStats.activities.forEach(activity => {\r\n    const existing = allActivities.get(activity.activityId);\r\n    if (existing) {\r\n      existing.compareMinutes = activity.totalMinutes;\r\n    } else {\r\n      allActivities.set(activity.activityId, {\r\n        name: activity.activityName,\r\n        color: activity.color,\r\n        currentMinutes: 0,\r\n        compareMinutes: activity.totalMinutes\r\n      });\r\n    }\r\n  });\r\n\r\n  return Array.from(allActivities.values()).map(activity => {\r\n    const diff = activity.currentMinutes - activity.compareMinutes;\r\n    return {\r\n      ...activity,\r\n      diff,\r\n      trend: diff > 0 ? 'up' as const : diff < 0 ? 'down' as const : 'same' as const\r\n    };\r\n  }).sort((a, b) => Math.abs(b.diff) - Math.abs(a.diff));\r\n}\r\n\r\n// 生成对比洞察\r\nfunction generateComparisonInsight(\r\n  comparison: any, \r\n  compareDate: string, \r\n  currentDate: string\r\n): string {\r\n  const insights: string[] = [];\r\n\r\n  if (comparison.filledMinutes.trend === 'up') {\r\n    insights.push(`相比${compareDate}，${currentDate}的时间记录增加了${comparison.filledMinutes.value}分钟`);\r\n  } else if (comparison.filledMinutes.trend === 'down') {\r\n    insights.push(`相比${compareDate}，${currentDate}的时间记录减少了${Math.abs(comparison.filledMinutes.value)}分钟`);\r\n  }\r\n\r\n  if (comparison.filledPercentage.trend === 'up') {\r\n    insights.push(`时间利用率提升了${comparison.filledPercentage.value.toFixed(1)}%`);\r\n  } else if (comparison.filledPercentage.trend === 'down') {\r\n    insights.push(`时间利用率下降了${Math.abs(comparison.filledPercentage.value).toFixed(1)}%`);\r\n  }\r\n\r\n  if (insights.length === 0) {\r\n    return `${currentDate}与${compareDate}的时间使用模式基本相似，保持了良好的一致性。`;\r\n  }\r\n\r\n  return insights.join('，') + '。';\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAEA;AATA;;;;;;;;;AAeO,SAAS,eAAe,EAAE,WAAW,EAAuB;IACjE,MAAM,EACJ,iBAAiB,EACjB,kBAAkB,EAClB,oBAAoB,EACpB,UAAU,EACX,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IAEd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAC7C,CAAA,GAAA,yHAAA,CAAA,kBAAe,AAAD,EAAE,aAAa,CAAC,GAAG,SAAS;;IAE5C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,mBAAmB;QACnB,mBAAmB;IACrB,GAAG;QAAC;QAAa;QAAa;KAAmB;IAEjD,6BAA6B;IAC7B,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC3B,YAAY;QACZ,MAAM,SAAS,kBAAkB;QACjC,IAAI,QAAQ,OAAO;QAEnB,wBAAwB;QACxB,MAAM,aAAa,qBAAqB;QACxC,OAAO,CAAA,GAAA,0HAAA,CAAA,sBAAmB,AAAD,EAAE,YAAY;IACzC,GAAG;QAAC;QAAa;QAAmB;QAAsB;KAAW;IAErE,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC3B,YAAY;QACZ,MAAM,SAAS,kBAAkB;QACjC,IAAI,QAAQ,OAAO;QAEnB,wBAAwB;QACxB,MAAM,aAAa,qBAAqB;QACxC,OAAO,CAAA,GAAA,0HAAA,CAAA,sBAAmB,AAAD,EAAE,YAAY;IACzC,GAAG;QAAC;QAAa;QAAmB;QAAsB;KAAW;IAErE,SAAS;IACT,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACzB,MAAM,oBAAoB,aAAa,aAAa,GAAG,aAAa,aAAa;QACjF,MAAM,uBAAuB,aAAa,gBAAgB,GAAG,aAAa,gBAAgB;QAC1F,MAAM,iBAAiB,aAAa,UAAU,CAAC,MAAM,GAAG,aAAa,UAAU,CAAC,MAAM;QAEtF,OAAO;YACL,eAAe;gBACb,OAAO;gBACP,YAAY,aAAa,aAAa,GAAG,IACrC,AAAC,oBAAoB,aAAa,aAAa,GAAI,MACnD;gBACJ,OAAO,oBAAoB,IAAI,OAAO,oBAAoB,IAAI,SAAS;YACzE;YACA,kBAAkB;gBAChB,OAAO;gBACP,OAAO,uBAAuB,IAAI,OAAO,uBAAuB,IAAI,SAAS;YAC/E;YACA,YAAY;gBACV,OAAO;gBACP,OAAO,iBAAiB,IAAI,OAAO,iBAAiB,IAAI,SAAS;YACnE;QACF;IACF,GAAG;QAAC;QAAc;KAAa;IAE/B,SAAS;IACT,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,kNAAA,CAAA,aAAU;oBAAC,MAAM;oBAAI,WAAU;;;;;;YACzC,KAAK;gBACH,qBAAO,8OAAC,sNAAA,CAAA,eAAY;oBAAC,MAAM;oBAAI,WAAU;;;;;;YAC3C;gBACE,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,MAAM;oBAAI,WAAU;;;;;;QACtC;IACF;IAEA,SAAS;IACT,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,OAAO;YACL,YAAY;YACZ,cAAc;YACd,WAAW;YACX,QAAQ;YACR,SAAS;QACX;QACA,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,MAAM;gBAAC;gBAAK;gBAAG;gBAAK;aAAE;QAAC;QACpD,YAAY;YACV,WAAW;YACX,aAAa;QACf;;0BAGA,8OAAC;gBAAI,WAAU;gBAAoC,OAAO;oBAAE,cAAc;gBAAmB;;kCAC3F,8OAAC;wBAAG,OAAO;4BACT,UAAU;4BACV,YAAY;4BACZ,OAAO;4BACP,SAAS;4BACT,YAAY;wBACd;kCAAG;;;;;;kCAGH,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,SAAS,IAAM,cAAc,CAAC;wBAC9B,OAAO;4BACL,OAAO;4BACP,UAAU;4BACV,YAAY;4BACZ,YAAY;4BACZ,QAAQ;4BACR,QAAQ;wBACV;wBACA,YAAY;4BACV,OAAO;4BACP,OAAO;wBACT;wBACA,UAAU;4BAAE,OAAO;wBAAK;kCAEvB,aAAa,OAAO;;;;;;;;;;;;0BAKzB,8OAAC;gBAAI,WAAU;gBAAoB,OAAO;oBACxC,KAAK;oBACL,cAAc;oBACd,UAAU;gBACZ;;kCACE,8OAAC;wBAAI,WAAU;wBAAoB,OAAO;4BAAE,KAAK;wBAAmB;kCAClE,cAAA,8OAAC;4BAAK,OAAO;gCACX,UAAU;gCACV,OAAO;gCACP,YAAY;4BACd;sCAAG;;;;;;;;;;;kCAIL,8OAAC,sIAAA,CAAA,mBAAgB;wBACf,OAAO;wBACP,UAAU;wBACV,WAAU;;;;;;kCAEZ,8OAAC;wBAAK,OAAO;4BACX,UAAU;4BACV,OAAO;4BACP,YAAY;wBACd;;4BAAG;4BACG,CAAA,GAAA,yHAAA,CAAA,aAAU,AAAD,EAAE;;;;;;;;;;;;;0BAKnB,8OAAC;gBAAI,WAAU;gBAAkC,OAAO;oBACtD,KAAK;oBACL,cAAc;gBAChB;;kCAEE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,OAAO;4BACL,YAAY;4BACZ,cAAc;4BACd,SAAS;4BACT,QAAQ;wBACV;wBACA,YAAY;4BACV,YAAY;4BACZ,aAAa;wBACf;wBACA,YAAY;4BAAE,UAAU;wBAAK;;0CAE7B,8OAAC;gCAAI,WAAU;gCAAoC,OAAO;oCAAE,cAAc;gCAAmB;;kDAC3F,8OAAC;wCAAK,OAAO;4CACX,UAAU;4CACV,YAAY;4CACZ,OAAO;wCACT;kDAAG;;;;;;oCAGF,aAAa,WAAW,aAAa,CAAC,KAAK;;;;;;;0CAE9C,8OAAC;gCAAI,OAAO;oCACV,UAAU;oCACV,YAAY;oCACZ,OAAO;gCACT;;oCACG,KAAK,GAAG,CAAC,WAAW,aAAa,CAAC,KAAK;oCAAE;;;;;;;0CAE5C,8OAAC;gCAAI,OAAO;oCACV,UAAU;oCACV,OAAO,cAAc,WAAW,aAAa,CAAC,KAAK;gCACrD;;oCACG,WAAW,aAAa,CAAC,KAAK,KAAK,OAAO,MAAM,WAAW,aAAa,CAAC,KAAK,KAAK,SAAS,MAAM;oCAClG,KAAK,GAAG,CAAC,WAAW,aAAa,CAAC,UAAU,EAAE,OAAO,CAAC;oCAAG;;;;;;;;;;;;;kCAK9D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,OAAO;4BACL,YAAY;4BACZ,cAAc;4BACd,SAAS;4BACT,QAAQ;wBACV;wBACA,YAAY;4BACV,YAAY;4BACZ,aAAa;wBACf;wBACA,YAAY;4BAAE,UAAU;wBAAK;;0CAE7B,8OAAC;gCAAI,WAAU;gCAAoC,OAAO;oCAAE,cAAc;gCAAmB;;kDAC3F,8OAAC;wCAAK,OAAO;4CACX,UAAU;4CACV,YAAY;4CACZ,OAAO;wCACT;kDAAG;;;;;;oCAGF,aAAa,WAAW,gBAAgB,CAAC,KAAK;;;;;;;0CAEjD,8OAAC;gCAAI,OAAO;oCACV,UAAU;oCACV,YAAY;oCACZ,OAAO;gCACT;;oCACG,KAAK,GAAG,CAAC,WAAW,gBAAgB,CAAC,KAAK,EAAE,OAAO,CAAC;oCAAG;;;;;;;0CAE1D,8OAAC;gCAAI,OAAO;oCACV,UAAU;oCACV,OAAO,cAAc,WAAW,gBAAgB,CAAC,KAAK;gCACxD;0CACG,WAAW,gBAAgB,CAAC,KAAK,KAAK,OAAO,OAAO,WAAW,gBAAgB,CAAC,KAAK,KAAK,SAAS,OAAO;;;;;;;;;;;;kCAK/G,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,OAAO;4BACL,YAAY;4BACZ,cAAc;4BACd,SAAS;4BACT,QAAQ;wBACV;wBACA,YAAY;4BACV,YAAY;4BACZ,aAAa;wBACf;wBACA,YAAY;4BAAE,UAAU;wBAAK;;0CAE7B,8OAAC;gCAAI,WAAU;gCAAoC,OAAO;oCAAE,cAAc;gCAAmB;;kDAC3F,8OAAC;wCAAK,OAAO;4CACX,UAAU;4CACV,YAAY;4CACZ,OAAO;wCACT;kDAAG;;;;;;oCAGF,aAAa,WAAW,UAAU,CAAC,KAAK;;;;;;;0CAE3C,8OAAC;gCAAI,OAAO;oCACV,UAAU;oCACV,YAAY;oCACZ,OAAO;gCACT;;oCACG,KAAK,GAAG,CAAC,WAAW,UAAU,CAAC,KAAK;oCAAE;;;;;;;0CAEzC,8OAAC;gCAAI,OAAO;oCACV,UAAU;oCACV,OAAO,cAAc,WAAW,UAAU,CAAC,KAAK;gCAClD;0CACG,WAAW,UAAU,CAAC,KAAK,KAAK,OAAO,OAAO,WAAW,UAAU,CAAC,KAAK,KAAK,SAAS,OAAO;;;;;;;;;;;;;;;;;;YAMpG,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,QAAQ;gBAAE;gBACjC,SAAS;oBAAE,SAAS;oBAAG,QAAQ;gBAAO;gBACtC,MAAM;oBAAE,SAAS;oBAAG,QAAQ;gBAAE;gBAC9B,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;;kCAGV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;sDACC,cAAA,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;kEAA+B;;;;;;kEAC7C,8OAAC;wDAAG,WAAU;kEAAiC,CAAA,GAAA,yHAAA,CAAA,aAAU,AAAD,EAAE;;;;;;kEAC1D,8OAAC;wDAAG,WAAU;kEAAiC,CAAA,GAAA,yHAAA,CAAA,aAAU,AAAD,EAAE;;;;;;kEAC1D,8OAAC;wDAAG,WAAU;kEAAgC;;;;;;;;;;;;;;;;;sDAGlD,8OAAC;sDACE,sBAAsB,cAAc,cAAc,GAAG,CAAC,CAAC,MAAM,sBAC5D,8OAAC;oDAAe,WAAU;;sEACxB,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEACC,WAAU;oEACV,OAAO;wEAAE,iBAAiB,KAAK,KAAK;oEAAC;;;;;;8EAEvC,8OAAC;8EAAM,KAAK,IAAI;;;;;;;;;;;;sEAElB,8OAAC;4DAAG,WAAU;;gEACX,KAAK,cAAc;gEAAC;;;;;;;sEAEvB,8OAAC;4DAAG,WAAU;;gEACX,KAAK,cAAc;gEAAC;;;;;;;sEAEvB,8OAAC;4DAAG,WAAW,CAAC,gBAAgB,EAAE,cAAc,KAAK,KAAK,GAAG;;gEAC1D,KAAK,KAAK,KAAK,OAAO,MAAM,KAAK,KAAK,KAAK,SAAS,MAAM;gEAC1D,KAAK,GAAG,CAAC,KAAK,IAAI;gEAAE;;;;;;;;mDAhBhB;;;;;;;;;;;;;;;;;;;;;;;;;;;kCA0BnB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAE,WAAU;0CACV,0BAA0B,YAAY,CAAA,GAAA,yHAAA,CAAA,aAAU,AAAD,EAAE,cAAc,CAAA,GAAA,yHAAA,CAAA,aAAU,AAAD,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAOzF;AAEA,WAAW;AACX,SAAS,sBAAsB,YAAwB,EAAE,YAAwB;IAC/E,MAAM,gBAAgB,IAAI;IAO1B,YAAY;IACZ,aAAa,UAAU,CAAC,OAAO,CAAC,CAAA;QAC9B,cAAc,GAAG,CAAC,SAAS,UAAU,EAAE;YACrC,MAAM,SAAS,YAAY;YAC3B,OAAO,SAAS,KAAK;YACrB,gBAAgB,SAAS,YAAY;YACrC,gBAAgB;QAClB;IACF;IAEA,YAAY;IACZ,aAAa,UAAU,CAAC,OAAO,CAAC,CAAA;QAC9B,MAAM,WAAW,cAAc,GAAG,CAAC,SAAS,UAAU;QACtD,IAAI,UAAU;YACZ,SAAS,cAAc,GAAG,SAAS,YAAY;QACjD,OAAO;YACL,cAAc,GAAG,CAAC,SAAS,UAAU,EAAE;gBACrC,MAAM,SAAS,YAAY;gBAC3B,OAAO,SAAS,KAAK;gBACrB,gBAAgB;gBAChB,gBAAgB,SAAS,YAAY;YACvC;QACF;IACF;IAEA,OAAO,MAAM,IAAI,CAAC,cAAc,MAAM,IAAI,GAAG,CAAC,CAAA;QAC5C,MAAM,OAAO,SAAS,cAAc,GAAG,SAAS,cAAc;QAC9D,OAAO;YACL,GAAG,QAAQ;YACX;YACA,OAAO,OAAO,IAAI,OAAgB,OAAO,IAAI,SAAkB;QACjE;IACF,GAAG,IAAI,CAAC,CAAC,GAAG,IAAM,KAAK,GAAG,CAAC,EAAE,IAAI,IAAI,KAAK,GAAG,CAAC,EAAE,IAAI;AACtD;AAEA,SAAS;AACT,SAAS,0BACP,UAAe,EACf,WAAmB,EACnB,WAAmB;IAEnB,MAAM,WAAqB,EAAE;IAE7B,IAAI,WAAW,aAAa,CAAC,KAAK,KAAK,MAAM;QAC3C,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,YAAY,CAAC,EAAE,YAAY,QAAQ,EAAE,WAAW,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC;IAC5F,OAAO,IAAI,WAAW,aAAa,CAAC,KAAK,KAAK,QAAQ;QACpD,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,YAAY,CAAC,EAAE,YAAY,QAAQ,EAAE,KAAK,GAAG,CAAC,WAAW,aAAa,CAAC,KAAK,EAAE,EAAE,CAAC;IACtG;IAEA,IAAI,WAAW,gBAAgB,CAAC,KAAK,KAAK,MAAM;QAC9C,SAAS,IAAI,CAAC,CAAC,QAAQ,EAAE,WAAW,gBAAgB,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAC1E,OAAO,IAAI,WAAW,gBAAgB,CAAC,KAAK,KAAK,QAAQ;QACvD,SAAS,IAAI,CAAC,CAAC,QAAQ,EAAE,KAAK,GAAG,CAAC,WAAW,gBAAgB,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACpF;IAEA,IAAI,SAAS,MAAM,KAAK,GAAG;QACzB,OAAO,GAAG,YAAY,CAAC,EAAE,YAAY,sBAAsB,CAAC;IAC9D;IAEA,OAAO,SAAS,IAAI,CAAC,OAAO;AAC9B", "debugId": null}}, {"offset": {"line": 7287, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/components/LoadingSkeleton.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\n\n/**\n * 基础骨架屏组件\n */\ninterface SkeletonProps {\n  width?: string | number;\n  height?: string | number;\n  className?: string;\n  rounded?: boolean;\n}\n\nexport function Skeleton({ \n  width = '100%', \n  height = '1rem', \n  className = '', \n  rounded = false \n}: SkeletonProps) {\n  return (\n    <motion.div\n      className={`bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 ${\n        rounded ? 'rounded-full' : 'rounded'\n      } ${className}`}\n      style={{ width, height }}\n      animate={{\n        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n      }}\n      transition={{\n        duration: 1.5,\n        repeat: Infinity,\n        ease: 'linear'\n      }}\n    />\n  );\n}\n\n/**\n * 统计卡片骨架屏\n */\nexport function DailyStatsCardSkeleton() {\n  return (\n    <div className=\"bg-white rounded-xl shadow-lg border border-gray-200 p-6\">\n      {/* 标题区域 */}\n      <div className=\"mb-6\">\n        <Skeleton width=\"120px\" height=\"24px\" className=\"mb-2\" />\n        <Skeleton width=\"200px\" height=\"16px\" />\n      </div>\n\n      {/* 统计数据网格 */}\n      <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6\">\n        {Array.from({ length: 4 }).map((_, index) => (\n          <div key={index} className=\"text-center\">\n            <Skeleton width=\"40px\" height=\"40px\" rounded className=\"mx-auto mb-2\" />\n            <Skeleton width=\"60px\" height=\"14px\" className=\"mx-auto mb-1\" />\n            <Skeleton width=\"80px\" height=\"18px\" className=\"mx-auto\" />\n          </div>\n        ))}\n      </div>\n\n      {/* 进度条区域 */}\n      <div>\n        <div className=\"flex justify-between items-center mb-2\">\n          <Skeleton width=\"100px\" height=\"14px\" />\n          <Skeleton width=\"80px\" height=\"14px\" />\n        </div>\n        <Skeleton width=\"100%\" height=\"12px\" rounded />\n      </div>\n    </div>\n  );\n}\n\n/**\n * 图表骨架屏\n */\nexport function ActivityChartSkeleton() {\n  return (\n    <div className=\"space-y-6\">\n      {/* 饼图骨架 */}\n      <div className=\"bg-white rounded-xl shadow-lg border border-gray-200 p-6\">\n        <Skeleton width=\"120px\" height=\"20px\" className=\"mb-4\" />\n        <div className=\"h-80 flex items-center justify-center\">\n          <Skeleton width=\"200px\" height=\"200px\" rounded />\n        </div>\n      </div>\n\n      {/* 条形图骨架 */}\n      <div className=\"bg-white rounded-xl shadow-lg border border-gray-200 p-6\">\n        <Skeleton width=\"140px\" height=\"20px\" className=\"mb-4\" />\n        <div className=\"h-80 space-y-4\">\n          {Array.from({ length: 5 }).map((_, index) => (\n            <div key={index} className=\"flex items-center space-x-4\">\n              <Skeleton width=\"80px\" height=\"16px\" />\n              <Skeleton \n                width={`${Math.random() * 60 + 20}%`} \n                height=\"24px\" \n                rounded \n              />\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n\n/**\n * 活动列表骨架屏\n */\nexport function ActivityListSkeleton() {\n  return (\n    <div className=\"space-y-6\">\n      {/* 活动排行榜骨架 */}\n      <div className=\"bg-white rounded-xl shadow-lg border border-gray-200 p-6\">\n        <Skeleton width=\"100px\" height=\"20px\" className=\"mb-4\" />\n        <div className=\"space-y-3\">\n          {Array.from({ length: 4 }).map((_, index) => (\n            <div key={index} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n              <div className=\"flex items-center space-x-3\">\n                <Skeleton width=\"24px\" height=\"24px\" rounded />\n                <div>\n                  <Skeleton width=\"80px\" height=\"16px\" className=\"mb-1\" />\n                  <Skeleton width=\"60px\" height=\"12px\" />\n                </div>\n              </div>\n              <Skeleton width=\"50px\" height=\"16px\" />\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* 时间段详情骨架 */}\n      <div className=\"bg-white rounded-xl shadow-lg border border-gray-200 p-6\">\n        <Skeleton width=\"100px\" height=\"20px\" className=\"mb-4\" />\n        <div className=\"space-y-2\">\n          {Array.from({ length: 6 }).map((_, index) => (\n            <div key={index} className=\"flex items-center justify-between py-2\">\n              <div className=\"flex items-center space-x-3\">\n                <Skeleton width=\"16px\" height=\"16px\" rounded />\n                <Skeleton width=\"120px\" height=\"14px\" />\n              </div>\n              <Skeleton width=\"80px\" height=\"14px\" />\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n\n/**\n * 复盘视图完整骨架屏\n */\nexport function ReviewViewSkeleton() {\n  return (\n    <motion.div\n      className=\"w-full max-w-6xl mx-auto px-4 py-6\"\n      initial={{ opacity: 0 }}\n      animate={{ opacity: 1 }}\n      transition={{ duration: 0.3 }}\n    >\n      {/* 页面标题骨架 */}\n      <div className=\"mb-8\">\n        <Skeleton width=\"120px\" height=\"32px\" className=\"mb-2\" />\n        <Skeleton width=\"200px\" height=\"20px\" />\n      </div>\n\n      {/* 统计概览卡片骨架 */}\n      <div className=\"mb-8\">\n        <DailyStatsCardSkeleton />\n      </div>\n\n      {/* 主要内容区域骨架 */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n        {/* 左侧：数据可视化骨架 */}\n        <div className=\"space-y-6\">\n          <ActivityChartSkeleton />\n        </div>\n\n        {/* 右侧：活动列表骨架 */}\n        <div className=\"space-y-6\">\n          <ActivityListSkeleton />\n        </div>\n      </div>\n    </motion.div>\n  );\n}\n\n/**\n * 加载状态指示器\n */\ninterface LoadingIndicatorProps {\n  message?: string;\n  progress?: number;\n}\n\nexport function LoadingIndicator({ message = '加载中...', progress }: LoadingIndicatorProps) {\n  return (\n    <div className=\"flex flex-col items-center justify-center py-8\">\n      <motion.div\n        className=\"w-8 h-8 border-3 border-blue-200 border-t-blue-500 rounded-full\"\n        animate={{ rotate: 360 }}\n        transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}\n      />\n      <p className=\"mt-4 text-gray-600 text-sm\">{message}</p>\n      {progress !== undefined && (\n        <div className=\"w-48 bg-gray-200 rounded-full h-2 mt-2\">\n          <motion.div\n            className=\"bg-blue-500 h-2 rounded-full\"\n            initial={{ width: 0 }}\n            animate={{ width: `${progress}%` }}\n            transition={{ duration: 0.3 }}\n          />\n        </div>\n      )}\n    </div>\n  );\n}\n\n/**\n * 数据加载错误组件\n */\ninterface LoadingErrorProps {\n  message?: string;\n  onRetry?: () => void;\n}\n\nexport function LoadingError({\n  message = '数据加载失败',\n  onRetry\n}: LoadingErrorProps) {\n  return (\n    <div className=\"flex flex-col items-center justify-center py-8\">\n      <div className=\"text-4xl mb-4\">😵</div>\n      <p className=\"text-gray-600 text-sm mb-4\">{message}</p>\n      {onRetry && (\n        <motion.button\n          onClick={onRetry}\n          className=\"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors\"\n          whileHover={{ scale: 1.05 }}\n          whileTap={{ scale: 0.95 }}\n        >\n          重试\n        </motion.button>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAGA;AAHA;;;AAeO,SAAS,SAAS,EACvB,QAAQ,MAAM,EACd,SAAS,MAAM,EACf,YAAY,EAAE,EACd,UAAU,KAAK,EACD;IACd,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,wDAAwD,EAClE,UAAU,iBAAiB,UAC5B,CAAC,EAAE,WAAW;QACf,OAAO;YAAE;YAAO;QAAO;QACvB,SAAS;YACP,oBAAoB;gBAAC;gBAAU;gBAAY;aAAS;QACtD;QACA,YAAY;YACV,UAAU;YACV,QAAQ;YACR,MAAM;QACR;;;;;;AAGN;AAKO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAS,OAAM;wBAAQ,QAAO;wBAAO,WAAU;;;;;;kCAChD,8OAAC;wBAAS,OAAM;wBAAQ,QAAO;;;;;;;;;;;;0BAIjC,8OAAC;gBAAI,WAAU;0BACZ,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC;wBAAgB,WAAU;;0CACzB,8OAAC;gCAAS,OAAM;gCAAO,QAAO;gCAAO,OAAO;gCAAC,WAAU;;;;;;0CACvD,8OAAC;gCAAS,OAAM;gCAAO,QAAO;gCAAO,WAAU;;;;;;0CAC/C,8OAAC;gCAAS,OAAM;gCAAO,QAAO;gCAAO,WAAU;;;;;;;uBAHvC;;;;;;;;;;0BASd,8OAAC;;kCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAS,OAAM;gCAAQ,QAAO;;;;;;0CAC/B,8OAAC;gCAAS,OAAM;gCAAO,QAAO;;;;;;;;;;;;kCAEhC,8OAAC;wBAAS,OAAM;wBAAO,QAAO;wBAAO,OAAO;;;;;;;;;;;;;;;;;;AAIpD;AAKO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAS,OAAM;wBAAQ,QAAO;wBAAO,WAAU;;;;;;kCAChD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAS,OAAM;4BAAQ,QAAO;4BAAQ,OAAO;;;;;;;;;;;;;;;;;0BAKlD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAS,OAAM;wBAAQ,QAAO;wBAAO,WAAU;;;;;;kCAChD,8OAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC;gCAAgB,WAAU;;kDACzB,8OAAC;wCAAS,OAAM;wCAAO,QAAO;;;;;;kDAC9B,8OAAC;wCACC,OAAO,GAAG,KAAK,MAAM,KAAK,KAAK,GAAG,CAAC,CAAC;wCACpC,QAAO;wCACP,OAAO;;;;;;;+BALD;;;;;;;;;;;;;;;;;;;;;;AAatB;AAKO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAS,OAAM;wBAAQ,QAAO;wBAAO,WAAU;;;;;;kCAChD,8OAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC;gCAAgB,WAAU;;kDACzB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAS,OAAM;gDAAO,QAAO;gDAAO,OAAO;;;;;;0DAC5C,8OAAC;;kEACC,8OAAC;wDAAS,OAAM;wDAAO,QAAO;wDAAO,WAAU;;;;;;kEAC/C,8OAAC;wDAAS,OAAM;wDAAO,QAAO;;;;;;;;;;;;;;;;;;kDAGlC,8OAAC;wCAAS,OAAM;wCAAO,QAAO;;;;;;;+BARtB;;;;;;;;;;;;;;;;0BAehB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAS,OAAM;wBAAQ,QAAO;wBAAO,WAAU;;;;;;kCAChD,8OAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC;gCAAgB,WAAU;;kDACzB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAS,OAAM;gDAAO,QAAO;gDAAO,OAAO;;;;;;0DAC5C,8OAAC;gDAAS,OAAM;gDAAQ,QAAO;;;;;;;;;;;;kDAEjC,8OAAC;wCAAS,OAAM;wCAAO,QAAO;;;;;;;+BALtB;;;;;;;;;;;;;;;;;;;;;;AAYtB;AAKO,SAAS;IACd,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,YAAY;YAAE,UAAU;QAAI;;0BAG5B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAS,OAAM;wBAAQ,QAAO;wBAAO,WAAU;;;;;;kCAChD,8OAAC;wBAAS,OAAM;wBAAQ,QAAO;;;;;;;;;;;;0BAIjC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;;;;;;;;;;0BAIH,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;;;;;;;;;;kCAIH,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;;;;;;;;;;;;;;;;;;;;;;AAKX;AAUO,SAAS,iBAAiB,EAAE,UAAU,QAAQ,EAAE,QAAQ,EAAyB;IACtF,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,QAAQ;gBAAI;gBACvB,YAAY;oBAAE,UAAU;oBAAG,QAAQ;oBAAU,MAAM;gBAAS;;;;;;0BAE9D,8OAAC;gBAAE,WAAU;0BAA8B;;;;;;YAC1C,aAAa,2BACZ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,OAAO;oBAAE;oBACpB,SAAS;wBAAE,OAAO,GAAG,SAAS,CAAC,CAAC;oBAAC;oBACjC,YAAY;wBAAE,UAAU;oBAAI;;;;;;;;;;;;;;;;;AAMxC;AAUO,SAAS,aAAa,EAC3B,UAAU,QAAQ,EAClB,OAAO,EACW;IAClB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BAAgB;;;;;;0BAC/B,8OAAC;gBAAE,WAAU;0BAA8B;;;;;;YAC1C,yBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS;gBACT,WAAU;gBACV,YAAY;oBAAE,OAAO;gBAAK;gBAC1B,UAAU;oBAAE,OAAO;gBAAK;0BACzB;;;;;;;;;;;;AAMT", "debugId": null}}, {"offset": {"line": 7911, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/components/ReviewView.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useMemo } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { useAppStore } from '@/stores/useAppStore';\r\nimport { DailyStatsCard } from './DailyStatsCard';\r\nimport { ActivityChart } from './ActivityChart';\r\nimport { ActivityList } from './ActivityList';\r\nimport { DateComparison } from './DateComparison';\r\nimport { formatDate } from '@/utils/timeUtils';\r\nimport { calculateDailyStats } from '@/utils/statsUtils';\r\nimport { DailyStats } from '@/types';\r\nimport {\r\n  ReviewViewSkeleton,\r\n  LoadingIndicator,\r\n  LoadingError\r\n} from './LoadingSkeleton';\r\n\r\nexport function ReviewView() {\r\n  const {\r\n    currentDate,\r\n    getDailyStatsSync,\r\n    getDailyStatsAsync,\r\n    getTimeBlocksForDate,\r\n    activities,\r\n    isViewPreloaded\r\n  } = useAppStore();\r\n\r\n  // 检查数据是否已预加载\r\n  const viewKey = `review-${currentDate}`;\r\n  const isDataPreloaded = isViewPreloaded(viewKey);\r\n\r\n  // 优化的初始状态获取 - 优先使用预加载的数据\r\n  const initialStats = useMemo(() => {\r\n    // 尝试从缓存获取\r\n    const cached = getDailyStatsSync(currentDate);\r\n    if (cached) return cached;\r\n\r\n    // 如果没有缓存，进行本地计算\r\n    const timeBlocks = getTimeBlocksForDate(currentDate);\r\n    return calculateDailyStats(timeBlocks, activities);\r\n  }, [currentDate, getDailyStatsSync, getTimeBlocksForDate, activities]);\r\n\r\n  const [dailyStats, setDailyStats] = useState<DailyStats>(initialStats);\r\n  const [isLoading, setIsLoading] = useState(!isDataPreloaded);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  // 监听日期变化，智能加载数据\r\n  useEffect(() => {\r\n    const loadData = async () => {\r\n      // 如果数据已预加载，直接使用缓存数据\r\n      if (isDataPreloaded) {\r\n        const cached = getDailyStatsSync(currentDate);\r\n        if (cached) {\r\n          setDailyStats(cached);\r\n          setIsLoading(false);\r\n          return;\r\n        }\r\n      }\r\n\r\n      setIsLoading(true);\r\n      setError(null);\r\n\r\n      try {\r\n        const stats = await getDailyStatsAsync(currentDate);\r\n        setDailyStats(stats);\r\n      } catch (err) {\r\n        setError(err instanceof Error ? err.message : '数据加载失败');\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    loadData();\r\n  }, [currentDate, getDailyStatsAsync, isDataPreloaded, getDailyStatsSync]);\r\n\r\n  // 重试函数 - 使用安全的数据获取方式\r\n  const handleRetry = () => {\r\n    // 尝试从缓存获取\r\n    const cached = getDailyStatsSync(currentDate);\r\n    if (cached) {\r\n      setDailyStats(cached);\r\n    } else {\r\n      // 如果没有缓存，进行本地计算\r\n      const timeBlocks = getTimeBlocksForDate(currentDate);\r\n      const stats = calculateDailyStats(timeBlocks, activities);\r\n      setDailyStats(stats);\r\n    }\r\n    setError(null);\r\n  };\r\n\r\n  // 如果正在加载，显示骨架屏\r\n  if (isLoading) {\r\n    return <ReviewViewSkeleton />;\r\n  }\r\n\r\n  // 如果有错误，显示错误状态\r\n  if (error) {\r\n    return (\r\n      <div className=\"w-full max-w-6xl mx-auto px-4 py-6\">\r\n        <LoadingError message={error} onRetry={handleRetry} />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <motion.div\r\n      className=\"w-full max-w-6xl mx-auto\"\r\n      style={{\r\n        padding: 'var(--spacing-4) var(--spacing-6)'\r\n      }}\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}\r\n    >\r\n      {/* 页面标题 */}\r\n      <motion.div\r\n        style={{ marginBottom: 'var(--spacing-8)' }}\r\n        initial={{ opacity: 0, y: -10 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ delay: 0.1, duration: 0.3 }}\r\n      >\r\n        <h1 style={{\r\n          fontSize: 'var(--font-size-3xl)',\r\n          fontWeight: 'var(--font-weight-bold)',\r\n          color: 'var(--neutral-900)',\r\n          marginBottom: 'var(--spacing-2)',\r\n          lineHeight: 'var(--line-height-tight)'\r\n        }}>\r\n          📈 每日复盘\r\n        </h1>\r\n        <p style={{\r\n          fontSize: 'var(--font-size-lg)',\r\n          color: 'var(--neutral-600)',\r\n          lineHeight: 'var(--line-height-normal)'\r\n        }}>\r\n          {formatDate(currentDate)} 的时间分析\r\n        </p>\r\n      </motion.div>\r\n\r\n      {/* 统计概览卡片 */}\r\n      <motion.div\r\n        style={{ marginBottom: 'var(--spacing-8)' }}\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ delay: 0.2, duration: 0.3 }}\r\n      >\r\n        <DailyStatsCard stats={dailyStats} />\r\n      </motion.div>\r\n\r\n      {/* 日期对比分析 */}\r\n      <motion.div\r\n        style={{ marginBottom: 'var(--spacing-8)' }}\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ delay: 0.3, duration: 0.3 }}\r\n      >\r\n        <DateComparison currentDate={currentDate} />\r\n      </motion.div>\r\n\r\n      {/* 主要内容区域 */}\r\n      <motion.div\r\n        className=\"grid grid-cols-1 lg:grid-cols-2\"\r\n        style={{ gap: 'var(--spacing-8)' }}\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ delay: 0.4, duration: 0.3 }}\r\n      >\r\n        {/* 左侧：数据可视化 */}\r\n        <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-6)' }}>\r\n          <ActivityChart stats={dailyStats} />\r\n        </div>\r\n\r\n        {/* 右侧：活动列表 */}\r\n        <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-6)' }}>\r\n          <ActivityList stats={dailyStats} />\r\n        </div>\r\n      </motion.div>\r\n\r\n      {/* 底部洞察区域 */}\r\n      {dailyStats.activities.length > 0 && (\r\n        <motion.div\r\n          className=\"card\"\r\n          style={{\r\n            marginTop: 'var(--spacing-12)',\r\n            padding: 'var(--spacing-6)',\r\n            background: 'linear-gradient(135deg, var(--primary-50), var(--info-50))',\r\n            borderRadius: 'var(--radius-xl)',\r\n            border: '1px solid var(--primary-100)',\r\n            boxShadow: 'var(--shadow-lg)'\r\n          }}\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.5, duration: 0.3, ease: [0.4, 0, 0.2, 1] }}\r\n          whileHover={{\r\n            boxShadow: 'var(--shadow-xl)',\r\n            borderColor: 'var(--primary-200)'\r\n          }}\r\n        >\r\n          <h3 style={{\r\n            fontSize: 'var(--font-size-lg)',\r\n            fontWeight: 'var(--font-weight-semibold)',\r\n            color: 'var(--neutral-900)',\r\n            marginBottom: 'var(--spacing-3)'\r\n          }}>\r\n            💡 今日洞察\r\n          </h3>\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2\" style={{ gap: 'var(--spacing-4)' }}>\r\n            <InsightCard\r\n              title=\"最长活动\"\r\n              value={dailyStats.activities[0]?.activityName || '无'}\r\n              description={`持续 ${Math.floor(dailyStats.activities[0]?.totalMinutes / 60 || 0)} 小时 ${(dailyStats.activities[0]?.totalMinutes % 60) || 0} 分钟`}\r\n              icon=\"🎯\"\r\n            />\r\n            <InsightCard\r\n              title=\"时间利用率\"\r\n              value={`${dailyStats.filledPercentage.toFixed(1)}%`}\r\n              description={getUtilizationDescription(dailyStats.filledPercentage)}\r\n              icon=\"📊\"\r\n            />\r\n          </div>\r\n        </motion.div>\r\n      )}\r\n\r\n      {/* 空状态 */}\r\n      {dailyStats.activities.length === 0 && (\r\n        <motion.div\r\n          className=\"text-center\"\r\n          style={{ padding: 'var(--spacing-16) 0' }}\r\n          initial={{ opacity: 0, scale: 0.9 }}\r\n          animate={{ opacity: 1, scale: 1 }}\r\n          transition={{ delay: 0.2, duration: 0.4, ease: [0.4, 0, 0.2, 1] }}\r\n        >\r\n          <motion.div\r\n            style={{\r\n              fontSize: 'var(--font-size-6xl)',\r\n              marginBottom: 'var(--spacing-4)'\r\n            }}\r\n            initial={{ scale: 0 }}\r\n            animate={{ scale: 1 }}\r\n            transition={{ delay: 0.4, duration: 0.3, ease: [0.68, -0.55, 0.265, 1.55] }}\r\n          >\r\n            📝\r\n          </motion.div>\r\n          <h3 style={{\r\n            fontSize: 'var(--font-size-xl)',\r\n            fontWeight: 'var(--font-weight-semibold)',\r\n            color: 'var(--neutral-900)',\r\n            marginBottom: 'var(--spacing-2)'\r\n          }}>\r\n            还没有记录任何活动\r\n          </h3>\r\n          <p style={{\r\n            color: 'var(--neutral-600)',\r\n            marginBottom: 'var(--spacing-6)',\r\n            fontSize: 'var(--font-size-base)'\r\n          }}>\r\n            回到网格视图开始记录您的时间吧\r\n          </p>\r\n        </motion.div>\r\n      )}\r\n    </motion.div>\r\n  );\r\n}\r\n\r\n// 洞察卡片组件\r\ninterface InsightCardProps {\r\n  title: string;\r\n  value: string;\r\n  description: string;\r\n  icon: string;\r\n}\r\n\r\nfunction InsightCard({ title, value, description, icon }: InsightCardProps) {\r\n  return (\r\n    <div className=\"bg-white p-4 rounded-lg border border-gray-200\">\r\n      <div className=\"flex items-center mb-2\">\r\n        <span className=\"text-2xl mr-2\">{icon}</span>\r\n        <h4 className=\"font-medium text-gray-900\">{title}</h4>\r\n      </div>\r\n      <div className=\"text-2xl font-bold text-gray-900 mb-1\">{value}</div>\r\n      <div className=\"text-sm text-gray-600\">{description}</div>\r\n    </div>\r\n  );\r\n}\r\n\r\n// 获取利用率描述\r\nfunction getUtilizationDescription(percentage: number): string {\r\n  if (percentage >= 80) {\r\n    return '时间利用率很高，保持下去！';\r\n  } else if (percentage >= 60) {\r\n    return '时间利用率不错，还有提升空间';\r\n  } else if (percentage >= 40) {\r\n    return '建议记录更多的时间活动';\r\n  } else {\r\n    return '开始记录您的时间活动吧';\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAZA;;;;;;;;;;;;AAkBO,SAAS;IACd,MAAM,EACJ,WAAW,EACX,iBAAiB,EACjB,kBAAkB,EAClB,oBAAoB,EACpB,UAAU,EACV,eAAe,EAChB,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IAEd,aAAa;IACb,MAAM,UAAU,CAAC,OAAO,EAAE,aAAa;IACvC,MAAM,kBAAkB,gBAAgB;IAExC,yBAAyB;IACzB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC3B,UAAU;QACV,MAAM,SAAS,kBAAkB;QACjC,IAAI,QAAQ,OAAO;QAEnB,gBAAgB;QAChB,MAAM,aAAa,qBAAqB;QACxC,OAAO,CAAA,GAAA,0HAAA,CAAA,sBAAmB,AAAD,EAAE,YAAY;IACzC,GAAG;QAAC;QAAa;QAAmB;QAAsB;KAAW;IAErE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAC5C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,gBAAgB;IAChB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW;YACf,oBAAoB;YACpB,IAAI,iBAAiB;gBACnB,MAAM,SAAS,kBAAkB;gBACjC,IAAI,QAAQ;oBACV,cAAc;oBACd,aAAa;oBACb;gBACF;YACF;YAEA,aAAa;YACb,SAAS;YAET,IAAI;gBACF,MAAM,QAAQ,MAAM,mBAAmB;gBACvC,cAAc;YAChB,EAAE,OAAO,KAAK;gBACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAChD,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG;QAAC;QAAa;QAAoB;QAAiB;KAAkB;IAExE,qBAAqB;IACrB,MAAM,cAAc;QAClB,UAAU;QACV,MAAM,SAAS,kBAAkB;QACjC,IAAI,QAAQ;YACV,cAAc;QAChB,OAAO;YACL,gBAAgB;YAChB,MAAM,aAAa,qBAAqB;YACxC,MAAM,QAAQ,CAAA,GAAA,0HAAA,CAAA,sBAAmB,AAAD,EAAE,YAAY;YAC9C,cAAc;QAChB;QACA,SAAS;IACX;IAEA,eAAe;IACf,IAAI,WAAW;QACb,qBAAO,8OAAC,qIAAA,CAAA,qBAAkB;;;;;IAC5B;IAEA,eAAe;IACf,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,qIAAA,CAAA,eAAY;gBAAC,SAAS;gBAAO,SAAS;;;;;;;;;;;IAG7C;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,OAAO;YACL,SAAS;QACX;QACA,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,MAAM;gBAAC;gBAAK;gBAAG;gBAAK;aAAE;QAAC;;0BAGpD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,OAAO;oBAAE,cAAc;gBAAmB;gBAC1C,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;oBAAK,UAAU;gBAAI;;kCAExC,8OAAC;wBAAG,OAAO;4BACT,UAAU;4BACV,YAAY;4BACZ,OAAO;4BACP,cAAc;4BACd,YAAY;wBACd;kCAAG;;;;;;kCAGH,8OAAC;wBAAE,OAAO;4BACR,UAAU;4BACV,OAAO;4BACP,YAAY;wBACd;;4BACG,CAAA,GAAA,yHAAA,CAAA,aAAU,AAAD,EAAE;4BAAa;;;;;;;;;;;;;0BAK7B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,OAAO;oBAAE,cAAc;gBAAmB;gBAC1C,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;oBAAK,UAAU;gBAAI;0BAExC,cAAA,8OAAC,oIAAA,CAAA,iBAAc;oBAAC,OAAO;;;;;;;;;;;0BAIzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,OAAO;oBAAE,cAAc;gBAAmB;gBAC1C,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;oBAAK,UAAU;gBAAI;0BAExC,cAAA,8OAAC,oIAAA,CAAA,iBAAc;oBAAC,aAAa;;;;;;;;;;;0BAI/B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBAAE,KAAK;gBAAmB;gBACjC,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;oBAAK,UAAU;gBAAI;;kCAGxC,8OAAC;wBAAI,OAAO;4BAAE,SAAS;4BAAQ,eAAe;4BAAU,KAAK;wBAAmB;kCAC9E,cAAA,8OAAC,mIAAA,CAAA,gBAAa;4BAAC,OAAO;;;;;;;;;;;kCAIxB,8OAAC;wBAAI,OAAO;4BAAE,SAAS;4BAAQ,eAAe;4BAAU,KAAK;wBAAmB;kCAC9E,cAAA,8OAAC,kIAAA,CAAA,eAAY;4BAAC,OAAO;;;;;;;;;;;;;;;;;YAKxB,WAAW,UAAU,CAAC,MAAM,GAAG,mBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBACL,WAAW;oBACX,SAAS;oBACT,YAAY;oBACZ,cAAc;oBACd,QAAQ;oBACR,WAAW;gBACb;gBACA,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;oBAAK,UAAU;oBAAK,MAAM;wBAAC;wBAAK;wBAAG;wBAAK;qBAAE;gBAAC;gBAChE,YAAY;oBACV,WAAW;oBACX,aAAa;gBACf;;kCAEA,8OAAC;wBAAG,OAAO;4BACT,UAAU;4BACV,YAAY;4BACZ,OAAO;4BACP,cAAc;wBAChB;kCAAG;;;;;;kCAGH,8OAAC;wBAAI,WAAU;wBAAkC,OAAO;4BAAE,KAAK;wBAAmB;;0CAChF,8OAAC;gCACC,OAAM;gCACN,OAAO,WAAW,UAAU,CAAC,EAAE,EAAE,gBAAgB;gCACjD,aAAa,CAAC,GAAG,EAAE,KAAK,KAAK,CAAC,WAAW,UAAU,CAAC,EAAE,EAAE,eAAe,MAAM,GAAG,IAAI,EAAE,AAAC,WAAW,UAAU,CAAC,EAAE,EAAE,eAAe,MAAO,EAAE,GAAG,CAAC;gCAC7I,MAAK;;;;;;0CAEP,8OAAC;gCACC,OAAM;gCACN,OAAO,GAAG,WAAW,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;gCACnD,aAAa,0BAA0B,WAAW,gBAAgB;gCAClE,MAAK;;;;;;;;;;;;;;;;;;YAOZ,WAAW,UAAU,CAAC,MAAM,KAAK,mBAChC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBAAE,SAAS;gBAAsB;gBACxC,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAI;gBAClC,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAE;gBAChC,YAAY;oBAAE,OAAO;oBAAK,UAAU;oBAAK,MAAM;wBAAC;wBAAK;wBAAG;wBAAK;qBAAE;gBAAC;;kCAEhE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,OAAO;4BACL,UAAU;4BACV,cAAc;wBAChB;wBACA,SAAS;4BAAE,OAAO;wBAAE;wBACpB,SAAS;4BAAE,OAAO;wBAAE;wBACpB,YAAY;4BAAE,OAAO;4BAAK,UAAU;4BAAK,MAAM;gCAAC;gCAAM,CAAC;gCAAM;gCAAO;6BAAK;wBAAC;kCAC3E;;;;;;kCAGD,8OAAC;wBAAG,OAAO;4BACT,UAAU;4BACV,YAAY;4BACZ,OAAO;4BACP,cAAc;wBAChB;kCAAG;;;;;;kCAGH,8OAAC;wBAAE,OAAO;4BACR,OAAO;4BACP,cAAc;4BACd,UAAU;wBACZ;kCAAG;;;;;;;;;;;;;;;;;;AAOb;AAUA,SAAS,YAAY,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,IAAI,EAAoB;IACxE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCAAiB;;;;;;kCACjC,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;;;;;;;0BAE7C,8OAAC;gBAAI,WAAU;0BAAyC;;;;;;0BACxD,8OAAC;gBAAI,WAAU;0BAAyB;;;;;;;;;;;;AAG9C;AAEA,UAAU;AACV,SAAS,0BAA0B,UAAkB;IACnD,IAAI,cAAc,IAAI;QACpB,OAAO;IACT,OAAO,IAAI,cAAc,IAAI;QAC3B,OAAO;IACT,OAAO,IAAI,cAAc,IAAI;QAC3B,OAAO;IACT,OAAO;QACL,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 8461, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/components/TimeUpdaterDebugPanel.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport { useAppStore } from '@/stores/useAppStore';\r\n\r\n/**\r\n * 智能时间更新器调试面板\r\n * 仅在开发环境中显示，用于监控时间触发器的性能\r\n */\r\nexport function TimeUpdaterDebugPanel() {\r\n  const { getTimeUpdaterStats } = useAppStore();\r\n  const [stats, setStats] = useState<any>(null);\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const [lastUpdateTime, setLastUpdateTime] = useState<string>('');\r\n\r\n  // 定期更新统计信息\r\n  useEffect(() => {\r\n    const updateStats = () => {\r\n      const currentStats = getTimeUpdaterStats();\r\n      setStats(currentStats);\r\n      setLastUpdateTime(new Date().toLocaleTimeString());\r\n    };\r\n\r\n    // 立即更新一次\r\n    updateStats();\r\n\r\n    // 每秒更新一次统计信息\r\n    const interval = setInterval(updateStats, 1000);\r\n\r\n    return () => clearInterval(interval);\r\n  }, [getTimeUpdaterStats]);\r\n\r\n  // 只在开发环境中显示\r\n  if (process.env.NODE_ENV !== 'development') {\r\n    return null;\r\n  }\r\n\r\n  if (!stats) {\r\n    return null;\r\n  }\r\n\r\n  const accuracyRate = stats.totalTriggers > 0 \r\n    ? ((stats.accurateTriggers / stats.totalTriggers) * 100).toFixed(1)\r\n    : '0';\r\n\r\n  return (\r\n    <>\r\n      {/* 切换按钮 */}\r\n      <motion.button\r\n        onClick={() => setIsVisible(!isVisible)}\r\n        className=\"fixed bottom-4 right-4 z-50 bg-blue-500 text-white p-2 rounded-full shadow-lg hover:bg-blue-600 transition-colors\"\r\n        whileHover={{ scale: 1.1 }}\r\n        whileTap={{ scale: 0.9 }}\r\n        title=\"时间更新器调试面板\"\r\n      >\r\n        🕐\r\n      </motion.button>\r\n\r\n      {/* 调试面板 */}\r\n      <AnimatePresence>\r\n        {isVisible && (\r\n          <motion.div\r\n            initial={{ opacity: 0, x: 300 }}\r\n            animate={{ opacity: 1, x: 0 }}\r\n            exit={{ opacity: 0, x: 300 }}\r\n            className=\"fixed bottom-16 right-4 z-40 bg-white border border-gray-300 rounded-lg shadow-xl p-4 w-80\"\r\n            style={{\r\n              fontSize: '12px',\r\n              fontFamily: 'monospace'\r\n            }}\r\n          >\r\n            <div className=\"flex justify-between items-center mb-3\">\r\n              <h3 className=\"font-bold text-sm text-gray-800\">时间更新器状态</h3>\r\n              <button\r\n                onClick={() => setIsVisible(false)}\r\n                className=\"text-gray-500 hover:text-gray-700 text-lg leading-none\"\r\n              >\r\n                ×\r\n              </button>\r\n            </div>\r\n\r\n            <div className=\"space-y-2 text-xs\">\r\n              <div className=\"grid grid-cols-2 gap-2\">\r\n                <div>\r\n                  <span className=\"text-gray-600\">总触发次数:</span>\r\n                  <div className=\"font-mono font-bold text-blue-600\">\r\n                    {stats.totalTriggers}\r\n                  </div>\r\n                </div>\r\n                <div>\r\n                  <span className=\"text-gray-600\">精确触发:</span>\r\n                  <div className=\"font-mono font-bold text-green-600\">\r\n                    {stats.accurateTriggers} ({accuracyRate}%)\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"grid grid-cols-2 gap-2\">\r\n                <div>\r\n                  <span className=\"text-gray-600\">平均偏差:</span>\r\n                  <div className=\"font-mono font-bold text-orange-600\">\r\n                    {stats.averageDeviation.toFixed(1)}ms\r\n                  </div>\r\n                </div>\r\n                <div>\r\n                  <span className=\"text-gray-600\">最大偏差:</span>\r\n                  <div className=\"font-mono font-bold text-red-600\">\r\n                    {stats.maxDeviation.toFixed(1)}ms\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div>\r\n                <span className=\"text-gray-600\">跳过触发:</span>\r\n                <div className=\"font-mono font-bold text-yellow-600\">\r\n                  {stats.skippedTriggers}\r\n                </div>\r\n              </div>\r\n\r\n              <div>\r\n                <span className=\"text-gray-600\">最后触发:</span>\r\n                <div className=\"font-mono text-gray-800\">\r\n                  {stats.lastTriggerTime > 0 \r\n                    ? new Date(stats.lastTriggerTime).toLocaleTimeString()\r\n                    : '未触发'\r\n                  }\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"pt-2 border-t border-gray-200\">\r\n                <span className=\"text-gray-600\">面板更新:</span>\r\n                <div className=\"font-mono text-gray-800\">\r\n                  {lastUpdateTime}\r\n                </div>\r\n              </div>\r\n\r\n              {/* 性能指标 */}\r\n              <div className=\"pt-2 border-t border-gray-200\">\r\n                <div className=\"text-xs text-gray-600 mb-1\">性能评级:</div>\r\n                <div className=\"flex items-center space-x-2\">\r\n                  <div \r\n                    className={`w-3 h-3 rounded-full ${\r\n                      parseFloat(accuracyRate) >= 95 ? 'bg-green-500' :\r\n                      parseFloat(accuracyRate) >= 80 ? 'bg-yellow-500' : 'bg-red-500'\r\n                    }`}\r\n                  />\r\n                  <span className=\"text-xs\">\r\n                    {parseFloat(accuracyRate) >= 95 ? '优秀' :\r\n                     parseFloat(accuracyRate) >= 80 ? '良好' : '需要优化'}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* 说明 */}\r\n            <div className=\"mt-3 pt-2 border-t border-gray-200 text-xs text-gray-500\">\r\n              <div>• 精确触发: 偏差 ≤ 100ms</div>\r\n              <div>• 智能触发器在分钟边界精确更新</div>\r\n              <div>• 页面隐藏时会跳过触发以节省资源</div>\r\n            </div>\r\n          </motion.div>\r\n        )}\r\n      </AnimatePresence>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAUO,SAAS;IACd,MAAM,EAAE,mBAAmB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IAC1C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACxC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE7D,WAAW;IACX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc;YAClB,MAAM,eAAe;YACrB,SAAS;YACT,kBAAkB,IAAI,OAAO,kBAAkB;QACjD;QAEA,SAAS;QACT;QAEA,aAAa;QACb,MAAM,WAAW,YAAY,aAAa;QAE1C,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAoB;IAExB,YAAY;IACZ;;IAIA,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IAEA,MAAM,eAAe,MAAM,aAAa,GAAG,IACvC,CAAC,AAAC,MAAM,gBAAgB,GAAG,MAAM,aAAa,GAAI,GAAG,EAAE,OAAO,CAAC,KAC/D;IAEJ,qBACE;;0BAEE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS,IAAM,aAAa,CAAC;gBAC7B,WAAU;gBACV,YAAY;oBAAE,OAAO;gBAAI;gBACzB,UAAU;oBAAE,OAAO;gBAAI;gBACvB,OAAM;0BACP;;;;;;0BAKD,8OAAC,yLAAA,CAAA,kBAAe;0BACb,2BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAI;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,MAAM;wBAAE,SAAS;wBAAG,GAAG;oBAAI;oBAC3B,WAAU;oBACV,OAAO;wBACL,UAAU;wBACV,YAAY;oBACd;;sCAEA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAkC;;;;;;8CAChD,8OAAC;oCACC,SAAS,IAAM,aAAa;oCAC5B,WAAU;8CACX;;;;;;;;;;;;sCAKH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAI,WAAU;8DACZ,MAAM,aAAa;;;;;;;;;;;;sDAGxB,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAI,WAAU;;wDACZ,MAAM,gBAAgB;wDAAC;wDAAG;wDAAa;;;;;;;;;;;;;;;;;;;8CAK9C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAI,WAAU;;wDACZ,MAAM,gBAAgB,CAAC,OAAO,CAAC;wDAAG;;;;;;;;;;;;;sDAGvC,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAI,WAAU;;wDACZ,MAAM,YAAY,CAAC,OAAO,CAAC;wDAAG;;;;;;;;;;;;;;;;;;;8CAKrC,8OAAC;;sDACC,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,8OAAC;4CAAI,WAAU;sDACZ,MAAM,eAAe;;;;;;;;;;;;8CAI1B,8OAAC;;sDACC,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,8OAAC;4CAAI,WAAU;sDACZ,MAAM,eAAe,GAAG,IACrB,IAAI,KAAK,MAAM,eAAe,EAAE,kBAAkB,KAClD;;;;;;;;;;;;8CAKR,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,8OAAC;4CAAI,WAAU;sDACZ;;;;;;;;;;;;8CAKL,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAA6B;;;;;;sDAC5C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAW,CAAC,qBAAqB,EAC/B,WAAW,iBAAiB,KAAK,iBACjC,WAAW,iBAAiB,KAAK,kBAAkB,cACnD;;;;;;8DAEJ,8OAAC;oDAAK,WAAU;8DACb,WAAW,iBAAiB,KAAK,OACjC,WAAW,iBAAiB,KAAK,OAAO;;;;;;;;;;;;;;;;;;;;;;;;sCAOjD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAI;;;;;;8CACL,8OAAC;8CAAI;;;;;;8CACL,8OAAC;8CAAI;;;;;;;;;;;;;;;;;;;;;;;;;AAOnB", "debugId": null}}, {"offset": {"line": 8861, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/components/HydrationBoundary.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useState } from 'react';\n\ninterface HydrationBoundaryProps {\n  children: React.ReactNode;\n  fallback?: React.ReactNode;\n}\n\n/**\n * HydrationBoundary组件\n * 用于防止SSR hydration mismatch错误\n * 确保子组件只在客户端完全hydration后才渲染\n */\nexport function HydrationBoundary({ children, fallback = null }: HydrationBoundaryProps) {\n  const [isHydrated, setIsHydrated] = useState(false);\n\n  useEffect(() => {\n    // 确保在客户端hydration完成后才渲染子组件\n    setIsHydrated(true);\n  }, []);\n\n  // 在hydration完成之前，渲染fallback或null\n  if (!isHydrated) {\n    return <>{fallback}</>;\n  }\n\n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAcO,SAAS,kBAAkB,EAAE,QAAQ,EAAE,WAAW,IAAI,EAA0B;IACrF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,2BAA2B;QAC3B,cAAc;IAChB,GAAG,EAAE;IAEL,iCAAiC;IACjC,IAAI,CAAC,YAAY;QACf,qBAAO;sBAAG;;IACZ;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 8889, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/components/ColorPicker.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useRef, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Check, Palette } from 'lucide-react';\nimport { DEFAULT_COLORS } from '@/types';\nimport { isLightColor } from '@/utils/colorUtils';\n\ninterface ColorPickerProps {\n  selectedColor: string;\n  onColorSelect: (color: string) => void;\n  onClose: () => void;\n}\n\n// 扩展的颜色调色板，包含更多颜色选择\nconst EXTENDED_COLORS = [\n  // 基础调色板（来自DEFAULT_COLORS）\n  ...DEFAULT_COLORS,\n  \n  // 扩展颜色 - 更多蓝色系\n  '#1E40AF', '#2563EB', '#60A5FA', '#93C5FD',\n  \n  // 扩展颜色 - 更多绿色系\n  '#059669', '#34D399', '#6EE7B7', '#A7F3D0',\n  \n  // 扩展颜色 - 更多红色系\n  '#DC2626', '#F87171', '#FCA5A5', '#FECACA',\n  \n  // 扩展颜色 - 更多紫色系\n  '#7C3AED', '#A855F7', '#C084FC', '#DDD6FE',\n  \n  // 扩展颜色 - 更多黄色系\n  '#D97706', '#FBBF24', '#FCD34D', '#FDE68A',\n  \n  // 扩展颜色 - 中性色系\n  '#374151', '#4B5563', '#9CA3AF', '#D1D5DB',\n  \n  // 扩展颜色 - 特殊色彩\n  '#BE185D', '#E11D48', '#F43F5E', '#FB7185', // 玫瑰色系\n  '#0891B2', '#0E7490', '#155E75', '#164E63', // 青色系\n];\n\n// 颜色分组\nconst COLOR_GROUPS = [\n  {\n    name: '推荐颜色',\n    colors: DEFAULT_COLORS\n  },\n  {\n    name: '蓝色系',\n    colors: ['#1E40AF', '#2563EB', '#3B82F6', '#60A5FA', '#93C5FD']\n  },\n  {\n    name: '绿色系',\n    colors: ['#059669', '#10B981', '#34D399', '#6EE7B7', '#A7F3D0']\n  },\n  {\n    name: '红色系',\n    colors: ['#DC2626', '#EF4444', '#F87171', '#FCA5A5', '#FECACA']\n  },\n  {\n    name: '紫色系',\n    colors: ['#7C3AED', '#8B5CF6', '#A855F7', '#C084FC', '#DDD6FE']\n  },\n  {\n    name: '黄色系',\n    colors: ['#D97706', '#F59E0B', '#FBBF24', '#FCD34D', '#FDE68A']\n  },\n  {\n    name: '中性色',\n    colors: ['#374151', '#4B5563', '#6B7280', '#9CA3AF', '#D1D5DB']\n  }\n];\n\nexport function ColorPicker({ selectedColor, onColorSelect, onClose }: ColorPickerProps) {\n  const pickerRef = useRef<HTMLDivElement>(null);\n\n  // 点击外部关闭\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (pickerRef.current && !pickerRef.current.contains(event.target as Node)) {\n        onClose();\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, [onClose]);\n\n  // 处理颜色选择\n  const handleColorClick = (color: string) => {\n    onColorSelect(color);\n  };\n\n  return (\n    <div ref={pickerRef} className=\"w-full max-h-80 overflow-y-auto\">\n        {/* 头部 */}\n        <div className=\"flex items-center justify-between mb-4\">\n          <div className=\"flex items-center space-x-2\">\n            <Palette className=\"w-4 h-4 text-gray-600\" />\n            <h3 className=\"text-sm font-medium text-gray-900\">选择颜色</h3>\n          </div>\n          <div className=\"text-xs text-gray-500\">\n            当前: {selectedColor}\n          </div>\n        </div>\n\n        {/* 颜色组 */}\n        <div className=\"space-y-4\">\n          {COLOR_GROUPS.map((group, groupIndex) => (\n            <div key={group.name} className=\"space-y-2\">\n              <h4 className=\"text-xs font-medium text-gray-700 uppercase tracking-wide\">\n                {group.name}\n              </h4>\n              <div className=\"grid grid-cols-5 gap-2\">\n                {group.colors.map((color, colorIndex) => (\n                  <ColorSwatch\n                    key={`${groupIndex}-${colorIndex}`}\n                    color={color}\n                    isSelected={selectedColor === color}\n                    onClick={() => handleColorClick(color)}\n                  />\n                ))}\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* 自定义颜色输入 */}\n        <div className=\"mt-4 pt-4 border-t border-gray-200\">\n          <label className=\"block text-xs font-medium text-gray-700 mb-2\">\n            自定义颜色\n          </label>\n          <div className=\"flex items-center space-x-2\">\n            <input\n              type=\"color\"\n              value={selectedColor}\n              onChange={(e) => handleColorClick(e.target.value)}\n              className=\"w-8 h-8 rounded border border-gray-300 cursor-pointer\"\n            />\n            <input\n              type=\"text\"\n              value={selectedColor}\n              onChange={(e) => {\n                const value = e.target.value;\n                // 简单的颜色格式验证\n                if (/^#[0-9A-Fa-f]{6}$/.test(value)) {\n                  handleColorClick(value);\n                }\n              }}\n              className=\"flex-1 px-2 py-1 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n              placeholder=\"#3B82F6\"\n              maxLength={7}\n            />\n          </div>\n        </div>\n    </div>\n  );\n}\n\n// 颜色色块组件\ninterface ColorSwatchProps {\n  color: string;\n  isSelected: boolean;\n  onClick: () => void;\n}\n\nfunction ColorSwatch({ color, isSelected, onClick }: ColorSwatchProps) {\n  return (\n    <motion.button\n      onClick={onClick}\n      className={`relative w-10 h-10 rounded-lg border-2 transition-all duration-200 ${\n        isSelected \n          ? 'border-gray-900 shadow-md' \n          : 'border-gray-200 hover:border-gray-300 hover:shadow-sm'\n      }`}\n      style={{ backgroundColor: color }}\n      whileHover={{ scale: 1.05 }}\n      whileTap={{ scale: 0.95 }}\n      title={color}\n    >\n      {isSelected && (\n        <motion.div\n          className=\"absolute inset-0 flex items-center justify-center\"\n          initial={{ scale: 0 }}\n          animate={{ scale: 1 }}\n          transition={{ type: \"spring\", stiffness: 400, damping: 25 }}\n        >\n          <Check \n            className=\"w-4 h-4 text-white drop-shadow-sm\" \n            style={{\n              // 根据背景颜色调整图标颜色\n              color: isLightColor(color) ? '#000000' : '#ffffff'\n            }}\n          />\n        </motion.div>\n      )}\n    </motion.button>\n  );\n}\n\n// isLightColor函数已移动到 @/utils/colorUtils 中统一管理\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AANA;;;;;;;AAcA,oBAAoB;AACpB,MAAM,kBAAkB;IACtB,0BAA0B;OACvB,qHAAA,CAAA,iBAAc;IAEjB,eAAe;IACf;IAAW;IAAW;IAAW;IAEjC,eAAe;IACf;IAAW;IAAW;IAAW;IAEjC,eAAe;IACf;IAAW;IAAW;IAAW;IAEjC,eAAe;IACf;IAAW;IAAW;IAAW;IAEjC,eAAe;IACf;IAAW;IAAW;IAAW;IAEjC,cAAc;IACd;IAAW;IAAW;IAAW;IAEjC,cAAc;IACd;IAAW;IAAW;IAAW;IACjC;IAAW;IAAW;IAAW;CAClC;AAED,OAAO;AACP,MAAM,eAAe;IACnB;QACE,MAAM;QACN,QAAQ,qHAAA,CAAA,iBAAc;IACxB;IACA;QACE,MAAM;QACN,QAAQ;YAAC;YAAW;YAAW;YAAW;YAAW;SAAU;IACjE;IACA;QACE,MAAM;QACN,QAAQ;YAAC;YAAW;YAAW;YAAW;YAAW;SAAU;IACjE;IACA;QACE,MAAM;QACN,QAAQ;YAAC;YAAW;YAAW;YAAW;YAAW;SAAU;IACjE;IACA;QACE,MAAM;QACN,QAAQ;YAAC;YAAW;YAAW;YAAW;YAAW;SAAU;IACjE;IACA;QACE,MAAM;QACN,QAAQ;YAAC;YAAW;YAAW;YAAW;YAAW;SAAU;IACjE;IACA;QACE,MAAM;QACN,QAAQ;YAAC;YAAW;YAAW;YAAW;YAAW;SAAU;IACjE;CACD;AAEM,SAAS,YAAY,EAAE,aAAa,EAAE,aAAa,EAAE,OAAO,EAAoB;IACrF,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAEzC,SAAS;IACT,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,UAAU,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC1E;YACF;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG;QAAC;KAAQ;IAEZ,SAAS;IACT,MAAM,mBAAmB,CAAC;QACxB,cAAc;IAChB;IAEA,qBACE,8OAAC;QAAI,KAAK;QAAW,WAAU;;0BAE3B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,wMAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;;;;;;;kCAEpD,8OAAC;wBAAI,WAAU;;4BAAwB;4BAChC;;;;;;;;;;;;;0BAKT,8OAAC;gBAAI,WAAU;0BACZ,aAAa,GAAG,CAAC,CAAC,OAAO,2BACxB,8OAAC;wBAAqB,WAAU;;0CAC9B,8OAAC;gCAAG,WAAU;0CACX,MAAM,IAAI;;;;;;0CAEb,8OAAC;gCAAI,WAAU;0CACZ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,2BACxB,8OAAC;wCAEC,OAAO;wCACP,YAAY,kBAAkB;wCAC9B,SAAS,IAAM,iBAAiB;uCAH3B,GAAG,WAAW,CAAC,EAAE,YAAY;;;;;;;;;;;uBAPhC,MAAM,IAAI;;;;;;;;;;0BAmBxB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gCAChD,WAAU;;;;;;0CAEZ,8OAAC;gCACC,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC;oCACT,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;oCAC5B,YAAY;oCACZ,IAAI,oBAAoB,IAAI,CAAC,QAAQ;wCACnC,iBAAiB;oCACnB;gCACF;gCACA,WAAU;gCACV,aAAY;gCACZ,WAAW;;;;;;;;;;;;;;;;;;;;;;;;AAMzB;AASA,SAAS,YAAY,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAoB;IACnE,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,SAAS;QACT,WAAW,CAAC,mEAAmE,EAC7E,aACI,8BACA,yDACJ;QACF,OAAO;YAAE,iBAAiB;QAAM;QAChC,YAAY;YAAE,OAAO;QAAK;QAC1B,UAAU;YAAE,OAAO;QAAK;QACxB,OAAO;kBAEN,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,OAAO;YAAE;YACpB,SAAS;gBAAE,OAAO;YAAE;YACpB,YAAY;gBAAE,MAAM;gBAAU,WAAW;gBAAK,SAAS;YAAG;sBAE1D,cAAA,8OAAC,oMAAA,CAAA,QAAK;gBACJ,WAAU;gBACV,OAAO;oBACL,eAAe;oBACf,OAAO,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD,EAAE,SAAS,YAAY;gBAC3C;;;;;;;;;;;;;;;;AAMZ,EAEA,8CAA8C", "debugId": null}}, {"offset": {"line": 9239, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/components/IconSelector.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useRef, useEffect, useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Search, Check } from 'lucide-react';\nimport * as LucideIcons from 'lucide-react';\n\ninterface IconSelectorProps {\n  selectedIcon: string;\n  onIconSelect: (icon: string) => void;\n  onClose: () => void;\n}\n\n// 常用图标分组\nconst ICON_GROUPS = [\n  {\n    name: '工作相关',\n    icons: [\n      'Laptop', 'Monitor', 'Code', 'FileText', 'Briefcase', \n      'Users', 'MessageSquare', 'Phone', 'Mail', 'Calendar'\n    ]\n  },\n  {\n    name: '学习教育',\n    icons: [\n      'BookOpen', 'GraduationCap', 'PenTool', 'Edit3', 'FileEdit',\n      'Library', 'Brain', 'Lightbulb', 'Target', 'Award'\n    ]\n  },\n  {\n    name: '运动健康',\n    icons: [\n      'Dumbbell', 'Activity', 'Heart', 'Bike', 'Footprints',\n      'Apple', 'Zap', 'Sun', 'Moon', 'Droplets'\n    ]\n  },\n  {\n    name: '生活日常',\n    icons: [\n      'Coffee', 'Utensils', 'Home', 'Car', 'ShoppingCart',\n      'Music', 'Gamepad2', 'Tv', 'Camera', 'Gift'\n    ]\n  },\n  {\n    name: '休闲娱乐',\n    icons: [\n      'Film', 'Headphones', 'Guitar', 'Palette', 'Brush',\n      'Book', 'Puzzle', 'Dice1', 'PartyPopper', 'Smile'\n    ]\n  },\n  {\n    name: '基础图形',\n    icons: [\n      'Circle', 'Square', 'Triangle', 'Star', 'Heart',\n      'Diamond', 'Hexagon', 'Octagon', 'Plus', 'Minus'\n    ]\n  }\n];\n\n// 获取所有可用图标\nconst ALL_ICONS = ICON_GROUPS.flatMap(group => group.icons);\n\nexport function IconSelector({ selectedIcon, onIconSelect, onClose }: IconSelectorProps) {\n  const selectorRef = useRef<HTMLDivElement>(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filteredGroups, setFilteredGroups] = useState(ICON_GROUPS);\n\n  // 点击外部关闭\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (selectorRef.current && !selectorRef.current.contains(event.target as Node)) {\n        onClose();\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, [onClose]);\n\n  // 搜索过滤\n  useEffect(() => {\n    if (!searchTerm.trim()) {\n      setFilteredGroups(ICON_GROUPS);\n      return;\n    }\n\n    const filtered = ICON_GROUPS.map(group => ({\n      ...group,\n      icons: group.icons.filter(icon => \n        icon.toLowerCase().includes(searchTerm.toLowerCase())\n      )\n    })).filter(group => group.icons.length > 0);\n\n    setFilteredGroups(filtered);\n  }, [searchTerm]);\n\n  /**\n   * 图标网格列优先索引转换函数\n   *\n   * 目标：将原始按行排序的图标数组转换为按列优先显示的顺序，\n   * 使用户的视觉选择顺序（从左到右、从上到下）与代码索引逻辑保持一致。\n   *\n   * 与项目时间网格系统保持一致的转换逻辑：列优先排序\n   *\n   * @param icons 原始图标数组（按行排序）\n   * @returns 转换后的图标数组（按列优先排序）\n   *\n   * 示例：\n   * 原始数组: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J']\n   * 当前显示（按行）: A B C D\n   *                  E F G H\n   *                  I J\n   *\n   * 转换后显示（按列）: A E I B\n   *                   F J C G\n   *                   D H\n   *\n   * 用户选择顺序：A→E→I→B→F→J→C→G→D→H（按列优先）\n   */\n  const convertToColumnFirstOrder = (icons: string[]) => {\n    const cols = 4; // 4列网格布局（与CSS grid-cols-4保持一致）\n    const rows = Math.ceil(icons.length / cols); // 计算需要的行数\n    const reorderedIcons: string[] = [];\n\n    // 按列优先顺序重新排列图标\n    // 外层循环：遍历每一列\n    // 内层循环：遍历每一行\n    for (let col = 0; col < cols; col++) {\n      for (let row = 0; row < rows; row++) {\n        const originalIndex = row * cols + col; // 计算原始按行排序的索引位置\n        if (originalIndex < icons.length) {\n          reorderedIcons.push(icons[originalIndex]);\n        }\n      }\n    }\n\n    return reorderedIcons;\n  };\n\n  // 处理图标选择\n  const handleIconClick = (icon: string) => {\n    onIconSelect(icon);\n  };\n\n  // 渲染图标组件\n  const renderIcon = (iconName: string, size: number = 20) => {\n    const IconComponent = (LucideIcons as any)[iconName];\n    if (!IconComponent) {\n      return <div className=\"w-5 h-5 bg-gray-200 rounded\" />;\n    }\n    return <IconComponent size={size} />;\n  };\n\n  return (\n    <div\n      ref={selectorRef}\n      className=\"w-full max-h-[400px] overflow-hidden flex flex-col\"\n    >\n        {/* 头部和搜索 */}\n        <div className=\"space-y-3 mb-4\">\n          <div className=\"flex items-center justify-between\">\n            <h3 className=\"text-sm font-medium text-gray-900\">选择图标</h3>\n            <div className=\"text-xs text-gray-500\">\n              当前: {selectedIcon}\n            </div>\n          </div>\n          \n          {/* 搜索框 */}\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" />\n            <input\n              type=\"text\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"w-full pl-10 pr-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              placeholder=\"搜索图标...\"\n            />\n          </div>\n        </div>\n\n        {/* 图标列表 */}\n        <div className=\"flex-1 overflow-y-auto space-y-6\">\n          {filteredGroups.length > 0 ? (\n            filteredGroups.map((group, groupIndex) => (\n              <div key={group.name} className=\"space-y-3\">\n                <h4 className=\"text-xs font-semibold text-gray-800 uppercase tracking-wide border-b border-gray-100 pb-1\">\n                  {group.name}\n                </h4>\n                <div className=\"grid grid-cols-4 gap-3\">\n                  {/* 应用列优先索引转换，使图标按列排序显示 */}\n                  {convertToColumnFirstOrder(group.icons).map((iconName, displayIndex) => (\n                    <IconButton\n                      key={`${group.name}-${iconName}-${displayIndex}`}\n                      iconName={iconName}\n                      isSelected={selectedIcon === iconName}\n                      onClick={() => handleIconClick(iconName)}\n                      renderIcon={renderIcon}\n                    />\n                  ))}\n                </div>\n              </div>\n            ))\n          ) : (\n            <div className=\"text-center py-8 text-gray-400\">\n              <Search className=\"w-8 h-8 mx-auto mb-2 opacity-50\" />\n              <p className=\"text-sm\">未找到匹配的图标</p>\n              <p className=\"text-xs mt-1\">尝试使用其他关键词搜索</p>\n            </div>\n          )}\n        </div>\n\n        {/* 当前选中的图标预览 */}\n        <div className=\"mt-4 pt-4 border-t border-gray-200\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-2\">\n              <span className=\"text-xs text-gray-600\">当前选中:</span>\n              <div className=\"flex items-center space-x-1\">\n                {renderIcon(selectedIcon, 16)}\n                <span className=\"text-xs font-medium text-gray-900\">{selectedIcon}</span>\n              </div>\n            </div>\n            <button\n              onClick={onClose}\n              className=\"text-xs text-blue-600 hover:text-blue-700 font-medium\"\n            >\n              确认选择\n            </button>\n          </div>\n        </div>\n    </div>\n  );\n}\n\n// 图标按钮组件\ninterface IconButtonProps {\n  iconName: string;\n  isSelected: boolean;\n  onClick: () => void;\n  renderIcon: (iconName: string, size?: number) => React.ReactNode;\n}\n\nfunction IconButton({ iconName, isSelected, onClick, renderIcon }: IconButtonProps) {\n  return (\n    <motion.button\n      onClick={onClick}\n      className={`relative w-12 h-12 rounded-xl border-2 flex items-center justify-center transition-all duration-200 ${\n        isSelected\n          ? 'border-blue-500 bg-blue-50 text-blue-600 shadow-md'\n          : 'border-gray-200 hover:border-blue-300 hover:bg-blue-50 hover:shadow-sm text-gray-600'\n      }`}\n      whileHover={{ scale: 1.05 }}\n      whileTap={{ scale: 0.95 }}\n      title={iconName}\n    >\n      {renderIcon(iconName, 22)}\n\n      {isSelected && (\n        <motion.div\n          className=\"absolute -top-1.5 -right-1.5 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center shadow-lg\"\n          initial={{ scale: 0 }}\n          animate={{ scale: 1 }}\n          transition={{ type: \"spring\", stiffness: 400, damping: 25 }}\n        >\n          <Check className=\"w-3 h-3 text-white\" />\n        </motion.div>\n      )}\n    </motion.button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AALA;;;;;;AAaA,SAAS;AACT,MAAM,cAAc;IAClB;QACE,MAAM;QACN,OAAO;YACL;YAAU;YAAW;YAAQ;YAAY;YACzC;YAAS;YAAiB;YAAS;YAAQ;SAC5C;IACH;IACA;QACE,MAAM;QACN,OAAO;YACL;YAAY;YAAiB;YAAW;YAAS;YACjD;YAAW;YAAS;YAAa;YAAU;SAC5C;IACH;IACA;QACE,MAAM;QACN,OAAO;YACL;YAAY;YAAY;YAAS;YAAQ;YACzC;YAAS;YAAO;YAAO;YAAQ;SAChC;IACH;IACA;QACE,MAAM;QACN,OAAO;YACL;YAAU;YAAY;YAAQ;YAAO;YACrC;YAAS;YAAY;YAAM;YAAU;SACtC;IACH;IACA;QACE,MAAM;QACN,OAAO;YACL;YAAQ;YAAc;YAAU;YAAW;YAC3C;YAAQ;YAAU;YAAS;YAAe;SAC3C;IACH;IACA;QACE,MAAM;QACN,OAAO;YACL;YAAU;YAAU;YAAY;YAAQ;YACxC;YAAW;YAAW;YAAW;YAAQ;SAC1C;IACH;CACD;AAED,WAAW;AACX,MAAM,YAAY,YAAY,OAAO,CAAC,CAAA,QAAS,MAAM,KAAK;AAEnD,SAAS,aAAa,EAAE,YAAY,EAAE,YAAY,EAAE,OAAO,EAAqB;IACrF,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,SAAS;IACT,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E;YACF;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG;QAAC;KAAQ;IAEZ,OAAO;IACP,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,IAAI,IAAI;YACtB,kBAAkB;YAClB;QACF;QAEA,MAAM,WAAW,YAAY,GAAG,CAAC,CAAA,QAAS,CAAC;gBACzC,GAAG,KAAK;gBACR,OAAO,MAAM,KAAK,CAAC,MAAM,CAAC,CAAA,OACxB,KAAK,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;YAEtD,CAAC,GAAG,MAAM,CAAC,CAAA,QAAS,MAAM,KAAK,CAAC,MAAM,GAAG;QAEzC,kBAAkB;IACpB,GAAG;QAAC;KAAW;IAEf;;;;;;;;;;;;;;;;;;;;;;GAsBC,GACD,MAAM,4BAA4B,CAAC;QACjC,MAAM,OAAO,GAAG,+BAA+B;QAC/C,MAAM,OAAO,KAAK,IAAI,CAAC,MAAM,MAAM,GAAG,OAAO,UAAU;QACvD,MAAM,iBAA2B,EAAE;QAEnC,eAAe;QACf,aAAa;QACb,aAAa;QACb,IAAK,IAAI,MAAM,GAAG,MAAM,MAAM,MAAO;YACnC,IAAK,IAAI,MAAM,GAAG,MAAM,MAAM,MAAO;gBACnC,MAAM,gBAAgB,MAAM,OAAO,KAAK,gBAAgB;gBACxD,IAAI,gBAAgB,MAAM,MAAM,EAAE;oBAChC,eAAe,IAAI,CAAC,KAAK,CAAC,cAAc;gBAC1C;YACF;QACF;QAEA,OAAO;IACT;IAEA,SAAS;IACT,MAAM,kBAAkB,CAAC;QACvB,aAAa;IACf;IAEA,SAAS;IACT,MAAM,aAAa,CAAC,UAAkB,OAAe,EAAE;QACrD,MAAM,gBAAgB,AAAC,iKAAmB,CAAC,SAAS;QACpD,IAAI,CAAC,eAAe;YAClB,qBAAO,8OAAC;gBAAI,WAAU;;;;;;QACxB;QACA,qBAAO,8OAAC;YAAc,MAAM;;;;;;IAC9B;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;;0BAGR,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,8OAAC;gCAAI,WAAU;;oCAAwB;oCAChC;;;;;;;;;;;;;kCAKT,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCACC,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;gCACV,aAAY;;;;;;;;;;;;;;;;;;0BAMlB,8OAAC;gBAAI,WAAU;0BACZ,eAAe,MAAM,GAAG,IACvB,eAAe,GAAG,CAAC,CAAC,OAAO,2BACzB,8OAAC;wBAAqB,WAAU;;0CAC9B,8OAAC;gCAAG,WAAU;0CACX,MAAM,IAAI;;;;;;0CAEb,8OAAC;gCAAI,WAAU;0CAEZ,0BAA0B,MAAM,KAAK,EAAE,GAAG,CAAC,CAAC,UAAU,6BACrD,8OAAC;wCAEC,UAAU;wCACV,YAAY,iBAAiB;wCAC7B,SAAS,IAAM,gBAAgB;wCAC/B,YAAY;uCAJP,GAAG,MAAM,IAAI,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,cAAc;;;;;;;;;;;uBAR9C,MAAM,IAAI;;;;8CAmBtB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,8OAAC;4BAAE,WAAU;sCAAU;;;;;;sCACvB,8OAAC;4BAAE,WAAU;sCAAe;;;;;;;;;;;;;;;;;0BAMlC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;8CACxC,8OAAC;oCAAI,WAAU;;wCACZ,WAAW,cAAc;sDAC1B,8OAAC;4CAAK,WAAU;sDAAqC;;;;;;;;;;;;;;;;;;sCAGzD,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOb;AAUA,SAAS,WAAW,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAmB;IAChF,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,SAAS;QACT,WAAW,CAAC,oGAAoG,EAC9G,aACI,uDACA,wFACJ;QACF,YAAY;YAAE,OAAO;QAAK;QAC1B,UAAU;YAAE,OAAO;QAAK;QACxB,OAAO;;YAEN,WAAW,UAAU;YAErB,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,OAAO;gBAAE;gBACpB,SAAS;oBAAE,OAAO;gBAAE;gBACpB,YAAY;oBAAE,MAAM;oBAAU,WAAW;oBAAK,SAAS;gBAAG;0BAE1D,cAAA,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAK3B", "debugId": null}}, {"offset": {"line": 9700, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/components/ActivityForm.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Save, X, Palette, Image } from 'lucide-react';\nimport * as LucideIcons from 'lucide-react';\nimport { useAppStore } from '@/stores/useAppStore';\nimport { Activity } from '@/types';\nimport { ColorPicker } from './ColorPicker';\nimport { IconSelector } from './IconSelector';\n\ninterface ActivityFormProps {\n  activity?: Activity | null;\n  onSave: () => void;\n  onCancel: () => void;\n}\n\ninterface FormData {\n  name: string;\n  color: string;\n  icon: string;\n  description: string;\n}\n\ninterface FormErrors {\n  name?: string;\n  color?: string;\n  icon?: string;\n}\n\nexport function ActivityForm({ activity, onSave, onCancel }: ActivityFormProps) {\n  const { addActivity, updateActivity } = useAppStore();\n  const [formData, setFormData] = useState<FormData>({\n    name: '',\n    color: '#3B82F6',\n    icon: 'Circle',\n    description: ''\n  });\n  const [errors, setErrors] = useState<FormErrors>({});\n  const [showColorPicker, setShowColorPicker] = useState(false);\n  const [showIconSelector, setShowIconSelector] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  // 初始化表单数据\n  useEffect(() => {\n    if (activity) {\n      setFormData({\n        name: activity.name,\n        color: activity.color,\n        icon: activity.icon || 'Circle',\n        description: activity.description || ''\n      });\n    } else {\n      // 重置为默认值\n      setFormData({\n        name: '',\n        color: '#3B82F6',\n        icon: 'Circle',\n        description: ''\n      });\n    }\n    setErrors({});\n    setShowColorPicker(false);\n    setShowIconSelector(false);\n  }, [activity]);\n\n  // 键盘快捷键支持\n  useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      // Ctrl+S 或 Command+S 保存表单\n      if ((event.ctrlKey || event.metaKey) && event.key === 's') {\n        event.preventDefault();\n        const form = document.querySelector('form');\n        if (form) {\n          form.requestSubmit();\n        }\n        return;\n      }\n\n      // ESC键关闭颜色选择器和图标选择器\n      if (event.key === 'Escape') {\n        if (showColorPicker) {\n          setShowColorPicker(false);\n          return;\n        }\n        if (showIconSelector) {\n          setShowIconSelector(false);\n          return;\n        }\n      }\n    };\n\n    document.addEventListener('keydown', handleKeyDown);\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [showColorPicker, showIconSelector]);\n\n  // 表单验证\n  const validateForm = (): boolean => {\n    const newErrors: FormErrors = {};\n\n    if (!formData.name.trim()) {\n      newErrors.name = '活动名称不能为空';\n    } else if (formData.name.trim().length > 20) {\n      newErrors.name = '活动名称不能超过20个字符';\n    }\n\n    if (!formData.color) {\n      newErrors.color = '请选择活动颜色';\n    }\n\n    if (!formData.icon) {\n      newErrors.icon = '请选择活动图标';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  // 处理表单提交\n  const handleSubmit = async (event: React.FormEvent) => {\n    event.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    setIsSubmitting(true);\n\n    try {\n      const activityData = {\n        name: formData.name.trim(),\n        color: formData.color,\n        icon: formData.icon,\n        description: formData.description.trim() || undefined\n      };\n\n      if (activity) {\n        // 更新现有活动\n        updateActivity(activity.id, activityData);\n      } else {\n        // 创建新活动\n        addActivity(activityData);\n      }\n\n      // 延迟一下以显示提交状态\n      await new Promise(resolve => setTimeout(resolve, 300));\n      onSave();\n    } catch (error) {\n      console.error('保存活动失败:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  // 处理输入变化\n  const handleInputChange = (field: keyof FormData, value: string) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n    \n    // 清除相关错误\n    if (errors[field as keyof FormErrors]) {\n      setErrors(prev => ({ ...prev, [field]: undefined }));\n    }\n  };\n\n  // 处理颜色选择\n  const handleColorSelect = (color: string) => {\n    handleInputChange('color', color);\n    setShowColorPicker(false);\n  };\n\n  // 处理图标选择\n  const handleIconSelect = (icon: string) => {\n    handleInputChange('icon', icon);\n    setShowIconSelector(false);\n  };\n\n  // 渲染图标组件\n  const renderIcon = (iconName: string, size: number = 20) => {\n    const IconComponent = (LucideIcons as any)[iconName];\n    if (!IconComponent) {\n      return <div className=\"w-5 h-5 bg-gray-200 rounded flex items-center justify-center text-xs text-gray-500\">?</div>;\n    }\n    return <IconComponent size={size} />;\n  };\n\n  return (\n    <div className=\"p-8\">\n      <form onSubmit={handleSubmit} className=\"space-y-8\">\n        {/* 活动名称 */}\n        <div className=\"space-y-3\">\n          <label htmlFor=\"name\" className=\"block text-base font-medium text-gray-700\">\n            活动名称 <span className=\"text-red-500\">*</span>\n          </label>\n          <input\n            id=\"name\"\n            type=\"text\"\n            value={formData.name}\n            onChange={(e) => handleInputChange('name', e.target.value)}\n            className={`w-full px-4 py-3 text-base border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${\n              errors.name ? 'border-red-300' : 'border-gray-300'\n            }`}\n            placeholder=\"请输入活动名称\"\n            maxLength={20}\n          />\n          {errors.name && (\n            <p className=\"text-sm text-red-600\">{errors.name}</p>\n          )}\n        </div>\n\n        {/* 活动描述 */}\n        <div className=\"space-y-3\">\n          <label htmlFor=\"description\" className=\"block text-base font-medium text-gray-700\">\n            活动描述\n          </label>\n          <textarea\n            id=\"description\"\n            value={formData.description}\n            onChange={(e) => handleInputChange('description', e.target.value)}\n            className=\"w-full px-4 py-3 text-base border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none\"\n            placeholder=\"请输入活动描述（可选）\"\n            rows={4}\n            maxLength={100}\n          />\n          <div className=\"text-sm text-gray-500 text-right\">\n            {formData.description.length}/100\n          </div>\n        </div>\n\n        {/* 颜色选择 */}\n        <div className=\"space-y-4\">\n          <div className=\"flex items-center justify-between\">\n            <label className=\"block text-base font-medium text-gray-700\">\n              活动颜色 <span className=\"text-red-500\">*</span>\n            </label>\n            <button\n              type=\"button\"\n              onClick={() => setShowColorPicker(!showColorPicker)}\n              className=\"flex items-center space-x-2 px-3 py-2 text-sm text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors\"\n            >\n              <Palette className=\"w-4 h-4\" />\n              <span>{showColorPicker ? '收起' : '选择颜色'}</span>\n            </button>\n          </div>\n\n          {/* 当前选中的颜色显示 */}\n          <div className=\"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg\">\n            <div\n              className=\"w-8 h-8 rounded-full border-2 border-white shadow-sm\"\n              style={{ backgroundColor: formData.color }}\n            />\n            <div>\n              <div className=\"text-sm font-medium text-gray-900\">当前颜色</div>\n              <div className=\"text-xs text-gray-500\">{formData.color}</div>\n            </div>\n          </div>\n\n          {/* 内联颜色选择器 */}\n          {showColorPicker && (\n            <motion.div\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: 'auto' }}\n              exit={{ opacity: 0, height: 0 }}\n              transition={{ duration: 0.2 }}\n              className=\"border border-gray-200 rounded-lg p-4 bg-white\"\n            >\n              <ColorPicker\n                selectedColor={formData.color}\n                onColorSelect={handleColorSelect}\n                onClose={() => setShowColorPicker(false)}\n              />\n            </motion.div>\n          )}\n\n          {errors.color && (\n            <p className=\"text-sm text-red-600\">{errors.color}</p>\n          )}\n        </div>\n\n        {/* 图标选择 */}\n        <div className=\"space-y-4\">\n          <div className=\"flex items-center justify-between\">\n            <label className=\"block text-base font-medium text-gray-700\">\n              活动图标 <span className=\"text-red-500\">*</span>\n            </label>\n            <button\n              type=\"button\"\n              onClick={() => setShowIconSelector(!showIconSelector)}\n              className=\"flex items-center space-x-2 px-3 py-2 text-sm text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors\"\n            >\n              <Image className=\"w-4 h-4\" />\n              <span>{showIconSelector ? '收起' : '选择图标'}</span>\n            </button>\n          </div>\n\n          {/* 当前选中的图标显示 */}\n          <div className=\"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg\">\n            <div className=\"w-8 h-8 flex items-center justify-center text-gray-600 bg-white rounded-lg border border-gray-200\">\n              {renderIcon(formData.icon, 24)}\n            </div>\n            <div>\n              <div className=\"text-sm font-medium text-gray-900\">当前图标</div>\n              <div className=\"text-xs text-gray-500\">{formData.icon}</div>\n            </div>\n          </div>\n\n          {/* 内联图标选择器 */}\n          {showIconSelector && (\n            <motion.div\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: 'auto' }}\n              exit={{ opacity: 0, height: 0 }}\n              transition={{ duration: 0.2 }}\n              className=\"border border-gray-200 rounded-lg p-4 bg-white\"\n            >\n              <IconSelector\n                selectedIcon={formData.icon}\n                onIconSelect={handleIconSelect}\n                onClose={() => setShowIconSelector(false)}\n              />\n            </motion.div>\n          )}\n\n          {errors.icon && (\n            <p className=\"text-sm text-red-600\">{errors.icon}</p>\n          )}\n        </div>\n\n        {/* 表单按钮 */}\n        <div className=\"flex items-center justify-end space-x-4 pt-6 border-t border-gray-200\">\n          <div className=\"flex-1 text-sm text-gray-500\">\n            <kbd className=\"px-2 py-1 bg-gray-100 border border-gray-300 rounded-md text-gray-600 font-mono\">Ctrl+S</kbd>\n            <span className=\"ml-2\">保存</span>\n          </div>\n\n          <button\n            type=\"button\"\n            onClick={onCancel}\n            className=\"px-6 py-3 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors font-medium\"\n            disabled={isSubmitting}\n          >\n            取消\n          </button>\n\n          <motion.button\n            type=\"submit\"\n            disabled={isSubmitting}\n            className=\"flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium\"\n            whileHover={!isSubmitting ? { scale: 1.02 } : {}}\n            whileTap={!isSubmitting ? { scale: 0.98 } : {}}\n          >\n            <Save className=\"w-4 h-4\" />\n            <span>{isSubmitting ? '保存中...' : (activity ? '更新活动' : '创建活动')}</span>\n          </motion.button>\n        </div>\n      </form>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AATA;;;;;;;;;AA8BO,SAAS,aAAa,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAqB;IAC5E,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IAClD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,MAAM;QACN,OAAO;QACP,MAAM;QACN,aAAa;IACf;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,CAAC;IAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,UAAU;IACV,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ,YAAY;gBACV,MAAM,SAAS,IAAI;gBACnB,OAAO,SAAS,KAAK;gBACrB,MAAM,SAAS,IAAI,IAAI;gBACvB,aAAa,SAAS,WAAW,IAAI;YACvC;QACF,OAAO;YACL,SAAS;YACT,YAAY;gBACV,MAAM;gBACN,OAAO;gBACP,MAAM;gBACN,aAAa;YACf;QACF;QACA,UAAU,CAAC;QACX,mBAAmB;QACnB,oBAAoB;IACtB,GAAG;QAAC;KAAS;IAEb,UAAU;IACV,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,0BAA0B;YAC1B,IAAI,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,KAAK,MAAM,GAAG,KAAK,KAAK;gBACzD,MAAM,cAAc;gBACpB,MAAM,OAAO,SAAS,aAAa,CAAC;gBACpC,IAAI,MAAM;oBACR,KAAK,aAAa;gBACpB;gBACA;YACF;YAEA,oBAAoB;YACpB,IAAI,MAAM,GAAG,KAAK,UAAU;gBAC1B,IAAI,iBAAiB;oBACnB,mBAAmB;oBACnB;gBACF;gBACA,IAAI,kBAAkB;oBACpB,oBAAoB;oBACpB;gBACF;YACF;QACF;QAEA,SAAS,gBAAgB,CAAC,WAAW;QACrC,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;QAC1C;IACF,GAAG;QAAC;QAAiB;KAAiB;IAEtC,OAAO;IACP,MAAM,eAAe;QACnB,MAAM,YAAwB,CAAC;QAE/B,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,UAAU,IAAI,GAAG;QACnB,OAAO,IAAI,SAAS,IAAI,CAAC,IAAI,GAAG,MAAM,GAAG,IAAI;YAC3C,UAAU,IAAI,GAAG;QACnB;QAEA,IAAI,CAAC,SAAS,KAAK,EAAE;YACnB,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,CAAC,SAAS,IAAI,EAAE;YAClB,UAAU,IAAI,GAAG;QACnB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,SAAS;IACT,MAAM,eAAe,OAAO;QAC1B,MAAM,cAAc;QAEpB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,MAAM,eAAe;gBACnB,MAAM,SAAS,IAAI,CAAC,IAAI;gBACxB,OAAO,SAAS,KAAK;gBACrB,MAAM,SAAS,IAAI;gBACnB,aAAa,SAAS,WAAW,CAAC,IAAI,MAAM;YAC9C;YAEA,IAAI,UAAU;gBACZ,SAAS;gBACT,eAAe,SAAS,EAAE,EAAE;YAC9B,OAAO;gBACL,QAAQ;gBACR,YAAY;YACd;YAEA,cAAc;YACd,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,SAAS;IACT,MAAM,oBAAoB,CAAC,OAAuB;QAChD,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAEhD,SAAS;QACT,IAAI,MAAM,CAAC,MAA0B,EAAE;YACrC,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAU,CAAC;QACpD;IACF;IAEA,SAAS;IACT,MAAM,oBAAoB,CAAC;QACzB,kBAAkB,SAAS;QAC3B,mBAAmB;IACrB;IAEA,SAAS;IACT,MAAM,mBAAmB,CAAC;QACxB,kBAAkB,QAAQ;QAC1B,oBAAoB;IACtB;IAEA,SAAS;IACT,MAAM,aAAa,CAAC,UAAkB,OAAe,EAAE;QACrD,MAAM,gBAAgB,AAAC,iKAAmB,CAAC,SAAS;QACpD,IAAI,CAAC,eAAe;YAClB,qBAAO,8OAAC;gBAAI,WAAU;0BAAqF;;;;;;QAC7G;QACA,qBAAO,8OAAC;YAAc,MAAM;;;;;;IAC9B;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAK,UAAU;YAAc,WAAU;;8BAEtC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAM,SAAQ;4BAAO,WAAU;;gCAA4C;8CACrE,8OAAC;oCAAK,WAAU;8CAAe;;;;;;;;;;;;sCAEtC,8OAAC;4BACC,IAAG;4BACH,MAAK;4BACL,OAAO,SAAS,IAAI;4BACpB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;4BACzD,WAAW,CAAC,sHAAsH,EAChI,OAAO,IAAI,GAAG,mBAAmB,mBACjC;4BACF,aAAY;4BACZ,WAAW;;;;;;wBAEZ,OAAO,IAAI,kBACV,8OAAC;4BAAE,WAAU;sCAAwB,OAAO,IAAI;;;;;;;;;;;;8BAKpD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAM,SAAQ;4BAAc,WAAU;sCAA4C;;;;;;sCAGnF,8OAAC;4BACC,IAAG;4BACH,OAAO,SAAS,WAAW;4BAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;4BAChE,WAAU;4BACV,aAAY;4BACZ,MAAM;4BACN,WAAW;;;;;;sCAEb,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,WAAW,CAAC,MAAM;gCAAC;;;;;;;;;;;;;8BAKjC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;;wCAA4C;sDACtD,8OAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;8CAEtC,8OAAC;oCACC,MAAK;oCACL,SAAS,IAAM,mBAAmB,CAAC;oCACnC,WAAU;;sDAEV,8OAAC,wMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,8OAAC;sDAAM,kBAAkB,OAAO;;;;;;;;;;;;;;;;;;sCAKpC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,iBAAiB,SAAS,KAAK;oCAAC;;;;;;8CAE3C,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAoC;;;;;;sDACnD,8OAAC;4CAAI,WAAU;sDAAyB,SAAS,KAAK;;;;;;;;;;;;;;;;;;wBAKzD,iCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAE;4BACjC,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAO;4BACtC,MAAM;gCAAE,SAAS;gCAAG,QAAQ;4BAAE;4BAC9B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;sCAEV,cAAA,8OAAC,iIAAA,CAAA,cAAW;gCACV,eAAe,SAAS,KAAK;gCAC7B,eAAe;gCACf,SAAS,IAAM,mBAAmB;;;;;;;;;;;wBAKvC,OAAO,KAAK,kBACX,8OAAC;4BAAE,WAAU;sCAAwB,OAAO,KAAK;;;;;;;;;;;;8BAKrD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;;wCAA4C;sDACtD,8OAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;8CAEtC,8OAAC;oCACC,MAAK;oCACL,SAAS,IAAM,oBAAoB,CAAC;oCACpC,WAAU;;sDAEV,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;sDAAM,mBAAmB,OAAO;;;;;;;;;;;;;;;;;;sCAKrC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACZ,WAAW,SAAS,IAAI,EAAE;;;;;;8CAE7B,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAoC;;;;;;sDACnD,8OAAC;4CAAI,WAAU;sDAAyB,SAAS,IAAI;;;;;;;;;;;;;;;;;;wBAKxD,kCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAE;4BACjC,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAO;4BACtC,MAAM;gCAAE,SAAS;gCAAG,QAAQ;4BAAE;4BAC9B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;sCAEV,cAAA,8OAAC,kIAAA,CAAA,eAAY;gCACX,cAAc,SAAS,IAAI;gCAC3B,cAAc;gCACd,SAAS,IAAM,oBAAoB;;;;;;;;;;;wBAKxC,OAAO,IAAI,kBACV,8OAAC;4BAAE,WAAU;sCAAwB,OAAO,IAAI;;;;;;;;;;;;8BAKpD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAkF;;;;;;8CACjG,8OAAC;oCAAK,WAAU;8CAAO;;;;;;;;;;;;sCAGzB,8OAAC;4BACC,MAAK;4BACL,SAAS;4BACT,WAAU;4BACV,UAAU;sCACX;;;;;;sCAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,MAAK;4BACL,UAAU;4BACV,WAAU;4BACV,YAAY,CAAC,eAAe;gCAAE,OAAO;4BAAK,IAAI,CAAC;4BAC/C,UAAU,CAAC,eAAe;gCAAE,OAAO;4BAAK,IAAI,CAAC;;8CAE7C,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;8CAAM,eAAe,WAAY,WAAW,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlE", "debugId": null}}, {"offset": {"line": 10355, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/components/DeleteConfirmDialog.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { AlertTriangle, Trash2, Info } from 'lucide-react';\nimport { Activity } from '@/types';\nimport { useAppStore } from '@/stores/useAppStore';\n\ninterface DeleteConfirmDialogProps {\n  activity: Activity;\n  onConfirm: () => void;\n  onCancel: () => void;\n}\n\nexport function DeleteConfirmDialog({ activity, onConfirm, onCancel }: DeleteConfirmDialogProps) {\n  const { timeBlocks } = useAppStore();\n  const [isDeleting, setIsDeleting] = useState(false);\n\n  // 检查是否为预设活动\n  const presetActivityNames = [\n    '深度工作', '会议', '学习', '运动', '休息', '娱乐', '通勤', '用餐'\n  ];\n  const isPreset = presetActivityNames.includes(activity.name);\n\n  // 计算关联的时间块数量\n  const getRelatedTimeBlocksCount = () => {\n    let count = 0;\n    Object.values(timeBlocks).forEach(dayBlocks => {\n      dayBlocks.forEach(block => {\n        if (block.activityId === activity.id) {\n          count++;\n        }\n      });\n    });\n    return count;\n  };\n\n  const relatedBlocksCount = getRelatedTimeBlocksCount();\n\n  // 处理确认删除\n  const handleConfirm = async () => {\n    setIsDeleting(true);\n    \n    try {\n      // 延迟一下以显示删除状态\n      await new Promise(resolve => setTimeout(resolve, 500));\n      onConfirm();\n    } catch (error) {\n      console.error('删除活动失败:', error);\n    } finally {\n      setIsDeleting(false);\n    }\n  };\n\n  return (\n    <div className=\"p-8 space-y-6\">\n      {/* 标题 */}\n      <div className=\"flex items-center space-x-3\">\n        <div className={`flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center ${\n          isPreset ? 'bg-orange-100' : 'bg-red-100'\n        }`}>\n          <AlertTriangle className={`w-6 h-6 ${\n            isPreset ? 'text-orange-600' : 'text-red-600'\n          }`} />\n        </div>\n        <div>\n          <h3 className=\"text-xl font-semibold text-gray-900\">\n            {isPreset ? '删除预设活动' : '确认删除活动'}\n          </h3>\n          <p className=\"text-sm text-gray-500\">\n            {isPreset ? '预设活动删除后可能影响系统功能' : '此操作无法撤销'}\n          </p>\n        </div>\n      </div>\n\n      {/* 活动信息 */}\n      <div className=\"bg-gray-50 rounded-lg p-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-3\">\n            <div\n              className=\"w-5 h-5 rounded-full border-2 border-white shadow-sm\"\n              style={{ backgroundColor: activity.color }}\n            />\n            <div>\n              <h4 className=\"font-medium text-gray-900 flex items-center space-x-2\">\n                <span>{activity.name}</span>\n                {isPreset && (\n                  <span className=\"px-2 py-1 text-xs bg-orange-100 text-orange-700 rounded-full\">\n                    预设活动\n                  </span>\n                )}\n              </h4>\n              {activity.description && (\n                <p className=\"text-sm text-gray-500\">{activity.description}</p>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* 影响提示 */}\n      {relatedBlocksCount > 0 ? (\n        <div className=\"bg-amber-50 border border-amber-200 rounded-lg p-4\">\n          <div className=\"flex items-start space-x-3\">\n            <Info className=\"w-5 h-5 text-amber-600 flex-shrink-0 mt-0.5\" />\n            <div>\n              <h4 className=\"font-medium text-amber-800\">影响分析</h4>\n              <p className=\"text-sm text-amber-700 mt-1\">\n                此活动在 {relatedBlocksCount} 个时间段中被使用。删除后，这些时间段将变为空白。\n              </p>\n            </div>\n          </div>\n        </div>\n      ) : (\n        <div className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\n          <div className=\"flex items-start space-x-3\">\n            <Info className=\"w-5 h-5 text-green-600 flex-shrink-0 mt-0.5\" />\n            <div>\n              <h4 className=\"font-medium text-green-800\">影响分析</h4>\n              <p className=\"text-sm text-green-700 mt-1\">\n                此活动当前未被使用，删除不会影响任何时间记录。\n              </p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* 操作按钮 */}\n      <div className=\"flex items-center justify-end space-x-4 pt-4 border-t border-gray-200\">\n        <button\n            onClick={onCancel}\n            disabled={isDeleting}\n            className=\"px-6 py-3 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed font-medium\"\n          >\n            取消\n          </button>\n\n          <motion.button\n            onClick={handleConfirm}\n            disabled={isDeleting}\n            className={`flex items-center space-x-2 px-6 py-3 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium ${\n              isPreset\n                ? 'bg-orange-600 hover:bg-orange-700'\n                : 'bg-red-600 hover:bg-red-700'\n            }`}\n            whileHover={!isDeleting ? { scale: 1.02 } : {}}\n            whileTap={!isDeleting ? { scale: 0.98 } : {}}\n          >\n            {isDeleting ? (\n              <>\n                <motion.div\n                  className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full\"\n                  animate={{ rotate: 360 }}\n                  transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n                />\n                <span>删除中...</span>\n              </>\n            ) : (\n              <>\n                <Trash2 className=\"w-4 h-4\" />\n                <span>确认删除</span>\n              </>\n            )}\n          </motion.button>\n      </div>\n\n      {/* 额外提示 */}\n      <div className=\"text-sm text-gray-600 bg-gray-50 rounded-lg p-4\">\n        <p className=\"flex items-start space-x-2\">\n          <span>💡</span>\n          <span>\n            {isPreset\n              ? '预设活动删除后，您可以重新创建同名活动来恢复功能。'\n              : '删除活动后，您可以重新创建同名活动，但历史记录将无法恢复。'\n            }\n          </span>\n        </p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAcO,SAAS,oBAAoB,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAA4B;IAC7F,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IACjC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,YAAY;IACZ,MAAM,sBAAsB;QAC1B;QAAQ;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAC7C;IACD,MAAM,WAAW,oBAAoB,QAAQ,CAAC,SAAS,IAAI;IAE3D,aAAa;IACb,MAAM,4BAA4B;QAChC,IAAI,QAAQ;QACZ,OAAO,MAAM,CAAC,YAAY,OAAO,CAAC,CAAA;YAChC,UAAU,OAAO,CAAC,CAAA;gBAChB,IAAI,MAAM,UAAU,KAAK,SAAS,EAAE,EAAE;oBACpC;gBACF;YACF;QACF;QACA,OAAO;IACT;IAEA,MAAM,qBAAqB;IAE3B,SAAS;IACT,MAAM,gBAAgB;QACpB,cAAc;QAEd,IAAI;YACF,cAAc;YACd,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B,SAAU;YACR,cAAc;QAChB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAW,CAAC,sEAAsE,EACrF,WAAW,kBAAkB,cAC7B;kCACA,cAAA,8OAAC,wNAAA,CAAA,gBAAa;4BAAC,WAAW,CAAC,QAAQ,EACjC,WAAW,oBAAoB,gBAC/B;;;;;;;;;;;kCAEJ,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CACX,WAAW,WAAW;;;;;;0CAEzB,8OAAC;gCAAE,WAAU;0CACV,WAAW,oBAAoB;;;;;;;;;;;;;;;;;;0BAMtC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,iBAAiB,SAAS,KAAK;gCAAC;;;;;;0CAE3C,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAM,SAAS,IAAI;;;;;;4CACnB,0BACC,8OAAC;gDAAK,WAAU;0DAA+D;;;;;;;;;;;;oCAKlF,SAAS,WAAW,kBACnB,8OAAC;wCAAE,WAAU;kDAAyB,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQnE,qBAAqB,kBACpB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAE,WAAU;;wCAA8B;wCACnC;wCAAmB;;;;;;;;;;;;;;;;;;;;;;;qCAMjC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;;;;;;;;;;;;;;;;;;0BASnD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACG,SAAS;wBACT,UAAU;wBACV,WAAU;kCACX;;;;;;kCAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,SAAS;wBACT,UAAU;wBACV,WAAW,CAAC,0IAA0I,EACpJ,WACI,sCACA,+BACJ;wBACF,YAAY,CAAC,aAAa;4BAAE,OAAO;wBAAK,IAAI,CAAC;wBAC7C,UAAU,CAAC,aAAa;4BAAE,OAAO;wBAAK,IAAI,CAAC;kCAE1C,2BACC;;8CACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,QAAQ;oCAAI;oCACvB,YAAY;wCAAE,UAAU;wCAAG,QAAQ;wCAAU,MAAM;oCAAS;;;;;;8CAE9D,8OAAC;8CAAK;;;;;;;yDAGR;;8CACE,8OAAC,0MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;0BAOhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;;sCACX,8OAAC;sCAAK;;;;;;sCACN,8OAAC;sCACE,WACG,8BACA;;;;;;;;;;;;;;;;;;;;;;;AAOhB", "debugId": null}}, {"offset": {"line": 10750, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/components/ActivityManagementModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport { X, Plus, Edit, Trash2, Settings, Search } from 'lucide-react';\r\nimport { useAppStore } from '@/stores/useAppStore';\r\nimport { Activity } from '@/types';\r\nimport { ActivityForm } from './ActivityForm';\r\nimport { DeleteConfirmDialog } from './DeleteConfirmDialog';\r\n\r\ninterface ActivityManagementModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n}\r\n\r\nexport function ActivityManagementModal({ isOpen, onClose }: ActivityManagementModalProps) {\r\n  const { activities, deleteActivity } = useAppStore();\r\n  const [currentView, setCurrentView] = useState<'list' | 'form' | 'delete'>('list');\r\n  const [editingActivity, setEditingActivity] = useState<Activity | null>(null);\r\n  const [deletingActivity, setDeletingActivity] = useState<Activity | null>(null);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n\r\n  // 分组活动：预设活动 vs 自定义活动\r\n  const presetActivityIds = [\r\n    '深度工作', '会议', '学习', '运动', '休息', '娱乐', '通勤', '用餐'\r\n  ];\r\n\r\n  // 搜索过滤功能\r\n  const filteredActivities = activities.filter(activity =>\r\n    activity.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n    (activity.description && activity.description.toLowerCase().includes(searchTerm.toLowerCase()))\r\n  );\r\n\r\n  const presetActivities = filteredActivities.filter(activity =>\r\n    presetActivityIds.some(name => activity.name === name)\r\n  );\r\n\r\n  const customActivities = filteredActivities.filter(activity =>\r\n    !presetActivityIds.some(name => activity.name === name)\r\n  );\r\n\r\n  // 处理创建新活动\r\n  const handleCreateActivity = () => {\r\n    setEditingActivity(null);\r\n    setCurrentView('form');\r\n  };\r\n\r\n  // 处理编辑活动\r\n  const handleEditActivity = (activity: Activity) => {\r\n    setEditingActivity(activity);\r\n    setCurrentView('form');\r\n  };\r\n\r\n  // 处理删除活动\r\n  const handleDeleteActivity = (activity: Activity) => {\r\n    setDeletingActivity(activity);\r\n    setCurrentView('delete');\r\n  };\r\n\r\n  // 确认删除活动\r\n  const handleConfirmDelete = () => {\r\n    if (deletingActivity) {\r\n      deleteActivity(deletingActivity.id);\r\n      setDeletingActivity(null);\r\n      setCurrentView('list');\r\n    }\r\n  };\r\n\r\n  // 返回列表视图\r\n  const handleBackToList = () => {\r\n    setCurrentView('list');\r\n    setEditingActivity(null);\r\n    setDeletingActivity(null);\r\n  };\r\n\r\n  // 阻止事件冒泡\r\n  const handleModalClick = (event: React.MouseEvent) => {\r\n    event.stopPropagation();\r\n  };\r\n\r\n  // 键盘快捷键支持\r\n  useEffect(() => {\r\n    if (!isOpen) return;\r\n\r\n    const handleKeyDown = (event: KeyboardEvent) => {\r\n      // ESC键关闭模态框\r\n      if (event.key === 'Escape') {\r\n        if (currentView === 'list') {\r\n          onClose();\r\n        } else {\r\n          handleBackToList();\r\n        }\r\n        return;\r\n      }\r\n\r\n      // Ctrl+N 创建新活动\r\n      if ((event.ctrlKey || event.metaKey) && event.key === 'n' && currentView === 'list') {\r\n        event.preventDefault();\r\n        handleCreateActivity();\r\n        return;\r\n      }\r\n\r\n      // Ctrl+K 或 Command+K 聚焦搜索框\r\n      if ((event.ctrlKey || event.metaKey) && event.key === 'k' && currentView === 'list') {\r\n        event.preventDefault();\r\n        const searchInput = document.querySelector('input[placeholder=\"搜索活动...\"]') as HTMLInputElement;\r\n        if (searchInput) {\r\n          searchInput.focus();\r\n        }\r\n        return;\r\n      }\r\n    };\r\n\r\n    document.addEventListener('keydown', handleKeyDown);\r\n    return () => {\r\n      document.removeEventListener('keydown', handleKeyDown);\r\n    };\r\n  }, [isOpen, currentView, onClose]);\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      <motion.div\r\n        className=\"fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm\"\r\n        initial={{ opacity: 0 }}\r\n        animate={{ opacity: 1 }}\r\n        exit={{ opacity: 0 }}\r\n        onClick={onClose}\r\n      >\r\n        <motion.div\r\n          className=\"relative w-full max-w-5xl max-h-[90vh] bg-white rounded-xl shadow-2xl overflow-hidden flex flex-col\"\r\n          initial={{ scale: 0.9, opacity: 0, y: 20 }}\r\n          animate={{ scale: 1, opacity: 1, y: 0 }}\r\n          exit={{ scale: 0.9, opacity: 0, y: 20 }}\r\n          transition={{ type: \"spring\", stiffness: 300, damping: 25 }}\r\n          onClick={handleModalClick}\r\n        >\r\n          {/* 模态框头部 */}\r\n          <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\r\n            <div className=\"flex items-center space-x-3\">\r\n              <Settings className=\"w-6 h-6 text-gray-600\" />\r\n              <h2 className=\"text-xl font-semibold text-gray-900\">\r\n                {currentView === 'form' ? (editingActivity ? '编辑活动' : '创建活动') : '活动管理'}\r\n              </h2>\r\n            </div>\r\n            <div className=\"flex items-center space-x-2\">\r\n              {currentView === 'list' && (\r\n                <div className=\"relative\">\r\n                  <kbd className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-xs text-gray-400\">⌘K</kbd>\r\n                  <input\r\n                    type=\"text\"\r\n                    value={searchTerm}\r\n                    onChange={(e) => setSearchTerm(e.target.value)}\r\n                    placeholder=\"搜索活动...\"\r\n                    className=\"pl-10 pr-3 py-1.5 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-48\"\r\n                  />\r\n                  <Search className=\"absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" />\r\n                </div>\r\n              )}\r\n              <button\r\n                onClick={onClose}\r\n                className=\"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors\"\r\n              >\r\n                <X className=\"w-5 h-5\" />\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* 模态框内容 */}\r\n          <div className=\"flex-1 overflow-y-auto\">\r\n            {currentView === 'list' && (\r\n              <ActivityListView\r\n                presetActivities={presetActivities}\r\n                customActivities={customActivities}\r\n                onCreateActivity={handleCreateActivity}\r\n                onEditActivity={handleEditActivity}\r\n                onDeleteActivity={handleDeleteActivity}\r\n                searchTerm={searchTerm}\r\n              />\r\n            )}\r\n\r\n            {currentView === 'form' && (\r\n              <ActivityForm\r\n                activity={editingActivity}\r\n                onSave={handleBackToList}\r\n                onCancel={handleBackToList}\r\n              />\r\n            )}\r\n\r\n            {currentView === 'delete' && deletingActivity && (\r\n              <DeleteConfirmDialog\r\n                activity={deletingActivity}\r\n                onConfirm={handleConfirmDelete}\r\n                onCancel={handleBackToList}\r\n              />\r\n            )}\r\n          </div>\r\n        </motion.div>\r\n      </motion.div>\r\n    </AnimatePresence>\r\n  );\r\n}\r\n\r\n// 活动列表视图组件\r\ninterface ActivityListViewProps {\r\n  presetActivities: Activity[];\r\n  customActivities: Activity[];\r\n  onCreateActivity: () => void;\r\n  onEditActivity: (activity: Activity) => void;\r\n  onDeleteActivity: (activity: Activity) => void;\r\n  searchTerm: string; // 添加searchTerm属性\r\n}\r\n\r\nfunction ActivityListView({\r\n  presetActivities,\r\n  customActivities,\r\n  onCreateActivity,\r\n  onEditActivity,\r\n  onDeleteActivity,\r\n  searchTerm\r\n}: ActivityListViewProps) {\r\n  return (\r\n    <div className=\"p-6 space-y-6\">\r\n      {/* 创建新活动按钮 */}\r\n      <motion.button\r\n        onClick={onCreateActivity}\r\n        className=\"w-full flex items-center justify-center space-x-2 p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-400 hover:bg-blue-50 transition-colors group\"\r\n        whileHover={{ scale: 1.02 }}\r\n        whileTap={{ scale: 0.98 }}\r\n      >\r\n        <Plus className=\"w-5 h-5 text-gray-400 group-hover:text-blue-500\" />\r\n        <span className=\"text-gray-600 group-hover:text-blue-600 font-medium\">创建新活动</span>\r\n      </motion.button>\r\n\r\n      {/* 预设活动组 */}\r\n      <ActivityGroup\r\n        title=\"预设活动\"\r\n        description=\"系统内置的常用活动类型\"\r\n        activities={presetActivities}\r\n        onEditActivity={onEditActivity}\r\n        onDeleteActivity={onDeleteActivity}\r\n        allowDelete={true}\r\n        searchTerm={searchTerm}\r\n      />\r\n\r\n      {/* 自定义活动组 */}\r\n      <ActivityGroup\r\n        title=\"自定义活动\"\r\n        description=\"您创建的个性化活动类型\"\r\n        activities={customActivities}\r\n        onEditActivity={onEditActivity}\r\n        onDeleteActivity={onDeleteActivity}\r\n        allowDelete={true}\r\n        searchTerm={searchTerm}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\n// 活动组组件\r\ninterface ActivityGroupProps {\r\n  title: string;\r\n  description: string;\r\n  activities: Activity[];\r\n  onEditActivity: (activity: Activity) => void;\r\n  onDeleteActivity: (activity: Activity) => void;\r\n  allowDelete: boolean;\r\n  searchTerm: string; // 添加searchTerm属性\r\n}\r\n\r\nfunction ActivityGroup({\r\n  title,\r\n  description,\r\n  activities,\r\n  onEditActivity,\r\n  onDeleteActivity,\r\n  allowDelete,\r\n  searchTerm\r\n}: ActivityGroupProps) {\r\n  return (\r\n    <div className=\"space-y-3\">\r\n      <div>\r\n        <h3 className=\"text-lg font-medium text-gray-900\">{title}</h3>\r\n        <p className=\"text-sm text-gray-500\">{description}</p>\r\n      </div>\r\n      \r\n      <div className=\"grid grid-cols-1 gap-3\">\r\n        {activities.map((activity) => (\r\n          <ActivityItem\r\n            key={activity.id}\r\n            activity={activity}\r\n            onEdit={() => onEditActivity(activity)}\r\n            onDelete={() => onDeleteActivity(activity)}\r\n            allowDelete={allowDelete}\r\n          />\r\n        ))}\r\n        \r\n        {activities.length === 0 && (\r\n          <div className=\"text-center py-8 text-gray-400\">\r\n            {searchTerm ? (\r\n              <div>\r\n                <Search className=\"w-8 h-8 mx-auto mb-2 opacity-50\" />\r\n                <p>未找到匹配的活动</p>\r\n                <p className=\"text-xs mt-1\">尝试使用其他关键词搜索</p>\r\n              </div>\r\n            ) : (\r\n              <div>\r\n                <Plus className=\"w-8 h-8 mx-auto mb-2 opacity-50\" />\r\n                <p>暂无{title.includes('自定义') ? '自定义' : ''}活动</p>\r\n                {title.includes('自定义') && (\r\n                  <p className=\"text-xs mt-1\">点击上方\"创建新活动\"按钮开始添加</p>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\n// 活动项组件\r\ninterface ActivityItemProps {\r\n  activity: Activity;\r\n  onEdit: () => void;\r\n  onDelete: () => void;\r\n  allowDelete: boolean;\r\n}\r\n\r\nfunction ActivityItem({ activity, onEdit, onDelete, allowDelete }: ActivityItemProps) {\r\n  return (\r\n    <motion.div\r\n      className=\"flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors\"\r\n      whileHover={{ scale: 1.01 }}\r\n      transition={{ type: \"spring\", stiffness: 400, damping: 25 }}\r\n    >\r\n      <div className=\"flex items-center space-x-3\">\r\n        <div\r\n          className=\"w-4 h-4 rounded-full\"\r\n          style={{ backgroundColor: activity.color }}\r\n        />\r\n        <div>\r\n          <h4 className=\"font-medium text-gray-900\">{activity.name}</h4>\r\n          {activity.description && (\r\n            <p className=\"text-sm text-gray-500\">{activity.description}</p>\r\n          )}\r\n        </div>\r\n      </div>\r\n      \r\n      <div className=\"flex items-center space-x-2\">\r\n        <button\r\n          onClick={onEdit}\r\n          className=\"p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-100 rounded-lg transition-colors\"\r\n          title=\"编辑活动\"\r\n        >\r\n          <Edit className=\"w-4 h-4\" />\r\n        </button>\r\n        \r\n        {allowDelete && (\r\n          <button\r\n            onClick={onDelete}\r\n            className=\"p-2 text-gray-400 hover:text-red-600 hover:bg-red-100 rounded-lg transition-colors\"\r\n            title=\"删除活动\"\r\n          >\r\n            <Trash2 className=\"w-4 h-4\" />\r\n          </button>\r\n        )}\r\n      </div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AARA;;;;;;;;AAeO,SAAS,wBAAwB,EAAE,MAAM,EAAE,OAAO,EAAgC;IACvF,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8B;IAC3E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IACxE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBAAqB;IACrB,MAAM,oBAAoB;QACxB;QAAQ;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAC7C;IAED,SAAS;IACT,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAA,WAC3C,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC1D,SAAS,WAAW,IAAI,SAAS,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;IAG7F,MAAM,mBAAmB,mBAAmB,MAAM,CAAC,CAAA,WACjD,kBAAkB,IAAI,CAAC,CAAA,OAAQ,SAAS,IAAI,KAAK;IAGnD,MAAM,mBAAmB,mBAAmB,MAAM,CAAC,CAAA,WACjD,CAAC,kBAAkB,IAAI,CAAC,CAAA,OAAQ,SAAS,IAAI,KAAK;IAGpD,UAAU;IACV,MAAM,uBAAuB;QAC3B,mBAAmB;QACnB,eAAe;IACjB;IAEA,SAAS;IACT,MAAM,qBAAqB,CAAC;QAC1B,mBAAmB;QACnB,eAAe;IACjB;IAEA,SAAS;IACT,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB;QACpB,eAAe;IACjB;IAEA,SAAS;IACT,MAAM,sBAAsB;QAC1B,IAAI,kBAAkB;YACpB,eAAe,iBAAiB,EAAE;YAClC,oBAAoB;YACpB,eAAe;QACjB;IACF;IAEA,SAAS;IACT,MAAM,mBAAmB;QACvB,eAAe;QACf,mBAAmB;QACnB,oBAAoB;IACtB;IAEA,SAAS;IACT,MAAM,mBAAmB,CAAC;QACxB,MAAM,eAAe;IACvB;IAEA,UAAU;IACV,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,QAAQ;QAEb,MAAM,gBAAgB,CAAC;YACrB,YAAY;YACZ,IAAI,MAAM,GAAG,KAAK,UAAU;gBAC1B,IAAI,gBAAgB,QAAQ;oBAC1B;gBACF,OAAO;oBACL;gBACF;gBACA;YACF;YAEA,eAAe;YACf,IAAI,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,KAAK,MAAM,GAAG,KAAK,OAAO,gBAAgB,QAAQ;gBACnF,MAAM,cAAc;gBACpB;gBACA;YACF;YAEA,2BAA2B;YAC3B,IAAI,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,KAAK,MAAM,GAAG,KAAK,OAAO,gBAAgB,QAAQ;gBACnF,MAAM,cAAc;gBACpB,MAAM,cAAc,SAAS,aAAa,CAAC;gBAC3C,IAAI,aAAa;oBACf,YAAY,KAAK;gBACnB;gBACA;YACF;QACF;QAEA,SAAS,gBAAgB,CAAC,WAAW;QACrC,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;QAC1C;IACF,GAAG;QAAC;QAAQ;QAAa;KAAQ;IAEjC,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACd,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,SAAS;sBAET,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,OAAO;oBAAK,SAAS;oBAAG,GAAG;gBAAG;gBACzC,SAAS;oBAAE,OAAO;oBAAG,SAAS;oBAAG,GAAG;gBAAE;gBACtC,MAAM;oBAAE,OAAO;oBAAK,SAAS;oBAAG,GAAG;gBAAG;gBACtC,YAAY;oBAAE,MAAM;oBAAU,WAAW;oBAAK,SAAS;gBAAG;gBAC1D,SAAS;;kCAGT,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAG,WAAU;kDACX,gBAAgB,SAAU,kBAAkB,SAAS,SAAU;;;;;;;;;;;;0CAGpE,8OAAC;gCAAI,WAAU;;oCACZ,gBAAgB,wBACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA2E;;;;;;0DAC1F,8OAAC;gDACC,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,aAAY;gDACZ,WAAU;;;;;;0DAEZ,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;kDAGtB,8OAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAMnB,8OAAC;wBAAI,WAAU;;4BACZ,gBAAgB,wBACf,8OAAC;gCACC,kBAAkB;gCAClB,kBAAkB;gCAClB,kBAAkB;gCAClB,gBAAgB;gCAChB,kBAAkB;gCAClB,YAAY;;;;;;4BAIf,gBAAgB,wBACf,8OAAC,kIAAA,CAAA,eAAY;gCACX,UAAU;gCACV,QAAQ;gCACR,UAAU;;;;;;4BAIb,gBAAgB,YAAY,kCAC3B,8OAAC,yIAAA,CAAA,sBAAmB;gCAClB,UAAU;gCACV,WAAW;gCACX,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1B;AAYA,SAAS,iBAAiB,EACxB,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,cAAc,EACd,gBAAgB,EAChB,UAAU,EACY;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS;gBACT,WAAU;gBACV,YAAY;oBAAE,OAAO;gBAAK;gBAC1B,UAAU;oBAAE,OAAO;gBAAK;;kCAExB,8OAAC,kMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;kCAChB,8OAAC;wBAAK,WAAU;kCAAsD;;;;;;;;;;;;0BAIxE,8OAAC;gBACC,OAAM;gBACN,aAAY;gBACZ,YAAY;gBACZ,gBAAgB;gBAChB,kBAAkB;gBAClB,aAAa;gBACb,YAAY;;;;;;0BAId,8OAAC;gBACC,OAAM;gBACN,aAAY;gBACZ,YAAY;gBACZ,gBAAgB;gBAChB,kBAAkB;gBAClB,aAAa;gBACb,YAAY;;;;;;;;;;;;AAIpB;AAaA,SAAS,cAAc,EACrB,KAAK,EACL,WAAW,EACX,UAAU,EACV,cAAc,EACd,gBAAgB,EAChB,WAAW,EACX,UAAU,EACS;IACnB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAqC;;;;;;kCACnD,8OAAC;wBAAE,WAAU;kCAAyB;;;;;;;;;;;;0BAGxC,8OAAC;gBAAI,WAAU;;oBACZ,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;4BAEC,UAAU;4BACV,QAAQ,IAAM,eAAe;4BAC7B,UAAU,IAAM,iBAAiB;4BACjC,aAAa;2BAJR,SAAS,EAAE;;;;;oBAQnB,WAAW,MAAM,KAAK,mBACrB,8OAAC;wBAAI,WAAU;kCACZ,2BACC,8OAAC;;8CACC,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;8CAAE;;;;;;8CACH,8OAAC;oCAAE,WAAU;8CAAe;;;;;;;;;;;iDAG9B,8OAAC;;8CACC,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;;wCAAE;wCAAG,MAAM,QAAQ,CAAC,SAAS,QAAQ;wCAAG;;;;;;;gCACxC,MAAM,QAAQ,CAAC,wBACd,8OAAC;oCAAE,WAAU;8CAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9C;AAUA,SAAS,aAAa,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAqB;IAClF,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,YAAY;YAAE,OAAO;QAAK;QAC1B,YAAY;YAAE,MAAM;YAAU,WAAW;YAAK,SAAS;QAAG;;0BAE1D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,iBAAiB,SAAS,KAAK;wBAAC;;;;;;kCAE3C,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAA6B,SAAS,IAAI;;;;;;4BACvD,SAAS,WAAW,kBACnB,8OAAC;gCAAE,WAAU;0CAAyB,SAAS,WAAW;;;;;;;;;;;;;;;;;;0BAKhE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS;wBACT,WAAU;wBACV,OAAM;kCAEN,cAAA,8OAAC,2MAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;oBAGjB,6BACC,8OAAC;wBACC,SAAS;wBACT,WAAU;wBACV,OAAM;kCAEN,cAAA,8OAAC,0MAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAM9B", "debugId": null}}, {"offset": {"line": 11351, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/components/PortalModal.tsx"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\nimport { createPortal } from 'react-dom';\n\ninterface PortalModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  children: React.ReactNode;\n  className?: string;\n  closeOnBackdropClick?: boolean;\n  closeOnEscape?: boolean;\n  showCloseButton?: boolean;\n  title?: string;\n}\n\nconst PortalModal: React.FC<PortalModalProps> = ({\n  isOpen,\n  onClose,\n  children,\n  className = '',\n  closeOnBackdropClick = true,\n  closeOnEscape = true,\n  showCloseButton = true,\n  title\n}) => {\n  const modalRef = useRef<HTMLDivElement>(null);\n\n  // 处理ESC键关闭\n  useEffect(() => {\n    if (!isOpen || !closeOnEscape) return;\n\n    const handleEscape = (event: KeyboardEvent) => {\n      if (event.key === 'Escape') {\n        onClose();\n      }\n    };\n\n    document.addEventListener('keydown', handleEscape);\n    return () => document.removeEventListener('keydown', handleEscape);\n  }, [isOpen, closeOnEscape, onClose]);\n\n  // 处理焦点管理\n  useEffect(() => {\n    if (!isOpen) return;\n\n    const previousActiveElement = document.activeElement as HTMLElement;\n    \n    // 聚焦到模态框\n    if (modalRef.current) {\n      modalRef.current.focus();\n    }\n\n    // 清理时恢复焦点\n    return () => {\n      if (previousActiveElement) {\n        previousActiveElement.focus();\n      }\n    };\n  }, [isOpen]);\n\n  // 处理背景点击关闭\n  const handleBackdropClick = (event: React.MouseEvent<HTMLDivElement>) => {\n    if (closeOnBackdropClick && event.target === event.currentTarget) {\n      onClose();\n    }\n  };\n\n  // 阻止模态框内容区域的点击事件冒泡\n  const handleContentClick = (event: React.MouseEvent<HTMLDivElement>) => {\n    event.stopPropagation();\n  };\n\n  if (!isOpen) return null;\n\n  const modalContent = (\n    <div\n      className=\"fixed inset-0 z-50 flex items-center justify-center p-4 animate-in fade-in duration-200\"\n      style={{\n        backgroundColor: 'rgba(0, 0, 0, 0.5)',\n        backdropFilter: 'blur(4px)'\n      }}\n      onClick={handleBackdropClick}\n      role=\"dialog\"\n      aria-modal=\"true\"\n      aria-labelledby={title ? \"modal-title\" : undefined}\n    >\n      <div\n        ref={modalRef}\n        className={`\n          relative bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-hidden\n          animate-in zoom-in-95 duration-200 ease-out\n          ${className}\n        `}\n        onClick={handleContentClick}\n        tabIndex={-1}\n      >\n        {/* 模态框头部 */}\n        {(title || showCloseButton) && (\n          <div className=\"flex items-center justify-between p-4 border-b border-gray-200\">\n            {title && (\n              <h2 id=\"modal-title\" className=\"text-lg font-semibold text-gray-900\">\n                {title}\n              </h2>\n            )}\n            {showCloseButton && (\n              <button\n                onClick={onClose}\n                className=\"p-1 text-gray-400 hover:text-gray-600 transition-colors rounded-full hover:bg-gray-100\"\n                aria-label=\"关闭模态框\"\n              >\n                <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                </svg>\n              </button>\n            )}\n          </div>\n        )}\n\n        {/* 模态框内容 */}\n        <div className=\"overflow-y-auto max-h-[calc(90vh-8rem)]\">\n          {children}\n        </div>\n      </div>\n    </div>\n  );\n\n  // 使用Portal将模态框渲染到document.body\n  return createPortal(modalContent, document.body);\n};\n\nexport default PortalModal;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAaA,MAAM,cAA0C,CAAC,EAC/C,MAAM,EACN,OAAO,EACP,QAAQ,EACR,YAAY,EAAE,EACd,uBAAuB,IAAI,EAC3B,gBAAgB,IAAI,EACpB,kBAAkB,IAAI,EACtB,KAAK,EACN;IACC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAExC,WAAW;IACX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,UAAU,CAAC,eAAe;QAE/B,MAAM,eAAe,CAAC;YACpB,IAAI,MAAM,GAAG,KAAK,UAAU;gBAC1B;YACF;QACF;QAEA,SAAS,gBAAgB,CAAC,WAAW;QACrC,OAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;IACvD,GAAG;QAAC;QAAQ;QAAe;KAAQ;IAEnC,SAAS;IACT,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,QAAQ;QAEb,MAAM,wBAAwB,SAAS,aAAa;QAEpD,SAAS;QACT,IAAI,SAAS,OAAO,EAAE;YACpB,SAAS,OAAO,CAAC,KAAK;QACxB;QAEA,UAAU;QACV,OAAO;YACL,IAAI,uBAAuB;gBACzB,sBAAsB,KAAK;YAC7B;QACF;IACF,GAAG;QAAC;KAAO;IAEX,WAAW;IACX,MAAM,sBAAsB,CAAC;QAC3B,IAAI,wBAAwB,MAAM,MAAM,KAAK,MAAM,aAAa,EAAE;YAChE;QACF;IACF;IAEA,mBAAmB;IACnB,MAAM,qBAAqB,CAAC;QAC1B,MAAM,eAAe;IACvB;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,6BACJ,8OAAC;QACC,WAAU;QACV,OAAO;YACL,iBAAiB;YACjB,gBAAgB;QAClB;QACA,SAAS;QACT,MAAK;QACL,cAAW;QACX,mBAAiB,QAAQ,gBAAgB;kBAEzC,cAAA,8OAAC;YACC,KAAK;YACL,WAAW,CAAC;;;UAGV,EAAE,UAAU;QACd,CAAC;YACD,SAAS;YACT,UAAU,CAAC;;gBAGV,CAAC,SAAS,eAAe,mBACxB,8OAAC;oBAAI,WAAU;;wBACZ,uBACC,8OAAC;4BAAG,IAAG;4BAAc,WAAU;sCAC5B;;;;;;wBAGJ,iCACC,8OAAC;4BACC,SAAS;4BACT,WAAU;4BACV,cAAW;sCAEX,cAAA,8OAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjE,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;8BAQ/E,8OAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;IAMT,+BAA+B;IAC/B,qBAAO,CAAA,GAAA,4MAAA,CAAA,eAAY,AAAD,EAAE,cAAc,SAAS,IAAI;AACjD;uCAEe", "debugId": null}}, {"offset": {"line": 11499, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/components/ImportModal.tsx"], "sourcesContent": ["import React, { useState, useRef, useCallback } from 'react';\nimport PortalModal from './PortalModal';\n\ninterface ImportModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onImport: (file: File, strategy: ImportStrategy) => Promise<void>;\n}\n\nexport type ImportStrategy = 'merge' | 'overwrite' | 'append';\n\ninterface ImportState {\n  file: File | null;\n  strategy: ImportStrategy;\n  isImporting: boolean;\n  error: string | null;\n  success: boolean;\n  importResult?: {\n    activitiesAdded: number;\n    timeBlocksAdded: number;\n  };\n}\n\nconst ImportModal: React.FC<ImportModalProps> = ({\n  isOpen,\n  onClose,\n  onImport\n}) => {\n  const fileInputRef = useRef<HTMLInputElement>(null);\n  const [importState, setImportState] = useState<ImportState>({\n    file: null,\n    strategy: 'merge',\n    isImporting: false,\n    error: null,\n    success: false\n  });\n\n  // 重置状态\n  const resetState = useCallback(() => {\n    setImportState({\n      file: null,\n      strategy: 'merge',\n      isImporting: false,\n      error: null,\n      success: false\n    });\n    // 重置文件选择器\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  }, []);\n\n  // 处理模态框关闭\n  const handleClose = useCallback(() => {\n    if (importState.isImporting) return; // 导入中不允许关闭\n    resetState();\n    onClose();\n  }, [importState.isImporting, resetState, onClose]);\n\n  // 处理文件选择\n  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (file) {\n      setImportState(prev => ({\n        ...prev,\n        file,\n        error: null,\n        success: false\n      }));\n    }\n  }, []);\n\n  // 处理策略选择\n  const handleStrategyChange = useCallback((strategy: ImportStrategy) => {\n    setImportState(prev => ({ ...prev, strategy }));\n  }, []);\n\n  // 处理导入确认\n  const handleImportConfirm = useCallback(async () => {\n    if (!importState.file) return;\n\n    setImportState(prev => ({ ...prev, isImporting: true, error: null }));\n\n    try {\n      await onImport(importState.file, importState.strategy);\n      setImportState(prev => ({\n        ...prev,\n        isImporting: false,\n        success: true,\n        error: null\n      }));\n\n      // 导入成功后2.5秒自动关闭模态框\n      setTimeout(() => {\n        resetState();\n        onClose();\n      }, 2500);\n    } catch (error) {\n      setImportState(prev => ({\n        ...prev,\n        isImporting: false,\n        error: error instanceof Error ? error.message : '导入失败'\n      }));\n    }\n  }, [importState.file, importState.strategy, onImport, resetState, onClose]);\n\n  // 触发文件选择\n  const triggerFileSelect = useCallback(() => {\n    fileInputRef.current?.click();\n  }, []);\n\n  const strategyOptions = [\n    { value: 'merge' as const, label: '智能合并（推荐）', description: '合并新数据，保留现有数据' },\n    { value: 'overwrite' as const, label: '完全覆盖', description: '清空现有数据，使用导入数据' },\n    { value: 'append' as const, label: '仅添加新数据', description: '只添加不冲突的新数据' }\n  ];\n\n  return (\n    <PortalModal\n      isOpen={isOpen}\n      onClose={handleClose}\n      title=\"导入数据\"\n      className=\"max-w-lg\"\n      closeOnBackdropClick={!importState.isImporting}\n      closeOnEscape={!importState.isImporting}\n    >\n      <div className=\"p-6\">\n        {/* 文件选择区域 */}\n        <div className=\"mb-6\">\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            选择文件\n          </label>\n          <div className=\"border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-400 transition-colors\">\n            {importState.file ? (\n              <div className=\"flex items-center justify-center space-x-2\">\n                <svg className=\"w-5 h-5 text-green-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n                <span className=\"text-sm text-gray-700\">{importState.file.name}</span>\n                <button\n                  onClick={triggerFileSelect}\n                  className=\"text-blue-600 hover:text-blue-800 text-sm underline\"\n                  disabled={importState.isImporting}\n                >\n                  重新选择\n                </button>\n              </div>\n            ) : (\n              <div>\n                <svg className=\"w-8 h-8 text-gray-400 mx-auto mb-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\" />\n                </svg>\n                <button\n                  onClick={triggerFileSelect}\n                  className=\"text-blue-600 hover:text-blue-800 font-medium\"\n                  disabled={importState.isImporting}\n                >\n                  点击选择文件\n                </button>\n                <p className=\"text-xs text-gray-500 mt-1\">支持 JSON 格式</p>\n              </div>\n            )}\n          </div>\n          <input\n            ref={fileInputRef}\n            type=\"file\"\n            accept=\".json\"\n            onChange={handleFileSelect}\n            className=\"hidden\"\n          />\n        </div>\n\n        {/* 导入策略选择 */}\n        {importState.file && (\n          <div className=\"mb-6\">\n            <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n              导入策略\n            </label>\n            <div className=\"space-y-2\">\n              {strategyOptions.map((option) => (\n                <label key={option.value} className=\"flex items-start space-x-3 cursor-pointer\">\n                  <input\n                    type=\"radio\"\n                    name=\"strategy\"\n                    value={option.value}\n                    checked={importState.strategy === option.value}\n                    onChange={() => handleStrategyChange(option.value)}\n                    className=\"mt-1 text-blue-600 focus:ring-blue-500\"\n                    disabled={importState.isImporting}\n                  />\n                  <div>\n                    <div className=\"text-sm font-medium text-gray-900\">{option.label}</div>\n                    <div className=\"text-xs text-gray-500\">{option.description}</div>\n                  </div>\n                </label>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* 错误信息 */}\n        {importState.error && (\n          <div className=\"mb-4 p-3 bg-red-50 border border-red-200 rounded-md\">\n            <div className=\"flex\">\n              <svg className=\"w-5 h-5 text-red-400 mr-2 mt-0.5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n              <div>\n                <h3 className=\"text-sm font-medium text-red-800\">导入失败</h3>\n                <p className=\"text-sm text-red-700 mt-1\">{importState.error}</p>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* 成功信息 */}\n        {importState.success && (\n          <div className=\"mb-4 p-3 bg-green-50 border border-green-200 rounded-md\">\n            <div className=\"flex\">\n              <svg className=\"w-5 h-5 text-green-400 mr-2 mt-0.5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n              <div>\n                <h3 className=\"text-sm font-medium text-green-800\">导入成功</h3>\n                <p className=\"text-sm text-green-700 mt-1\">数据导入成功</p>\n                {importState.importResult && (\n                  <p className=\"text-xs text-green-600 mt-1\">\n                    活动: +{importState.importResult.activitiesAdded} 时间块: +{importState.importResult.timeBlocksAdded}\n                  </p>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* 操作按钮 */}\n        {!importState.success && (\n          <div className=\"flex justify-end space-x-3\">\n            <button\n              onClick={handleClose}\n              className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50\"\n              disabled={importState.isImporting}\n            >\n              取消\n            </button>\n            <button\n              onClick={handleImportConfirm}\n              disabled={!importState.file || importState.isImporting}\n              className=\"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {importState.isImporting ? (\n                <div className=\"flex items-center\">\n                  <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" fill=\"none\" viewBox=\"0 0 24 24\">\n                    <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                    <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                  </svg>\n                  导入中...\n                </div>\n              ) : (\n                '确认导入'\n              )}\n            </button>\n          </div>\n        )}\n\n        {/* 导入成功时显示自动关闭提示 */}\n        {importState.success && (\n          <div className=\"text-center\">\n            <p className=\"text-sm text-gray-600\">\n              导入成功！模态框将在几秒后自动关闭...\n            </p>\n          </div>\n        )}\n      </div>\n    </PortalModal>\n  );\n};\n\nexport default ImportModal;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAsBA,MAAM,cAA0C,CAAC,EAC/C,MAAM,EACN,OAAO,EACP,QAAQ,EACT;IACC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;QAC1D,MAAM;QACN,UAAU;QACV,aAAa;QACb,OAAO;QACP,SAAS;IACX;IAEA,OAAO;IACP,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,eAAe;YACb,MAAM;YACN,UAAU;YACV,aAAa;YACb,OAAO;YACP,SAAS;QACX;QACA,UAAU;QACV,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK,GAAG;QAC/B;IACF,GAAG,EAAE;IAEL,UAAU;IACV,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,IAAI,YAAY,WAAW,EAAE,QAAQ,WAAW;QAChD;QACA;IACF,GAAG;QAAC,YAAY,WAAW;QAAE;QAAY;KAAQ;IAEjD,SAAS;IACT,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,MAAM;YACR,eAAe,CAAA,OAAQ,CAAC;oBACtB,GAAG,IAAI;oBACP;oBACA,OAAO;oBACP,SAAS;gBACX,CAAC;QACH;IACF,GAAG,EAAE;IAEL,SAAS;IACT,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACxC,eAAe,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE;YAAS,CAAC;IAC/C,GAAG,EAAE;IAEL,SAAS;IACT,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,IAAI,CAAC,YAAY,IAAI,EAAE;QAEvB,eAAe,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,aAAa;gBAAM,OAAO;YAAK,CAAC;QAEnE,IAAI;YACF,MAAM,SAAS,YAAY,IAAI,EAAE,YAAY,QAAQ;YACrD,eAAe,CAAA,OAAQ,CAAC;oBACtB,GAAG,IAAI;oBACP,aAAa;oBACb,SAAS;oBACT,OAAO;gBACT,CAAC;YAED,mBAAmB;YACnB,WAAW;gBACT;gBACA;YACF,GAAG;QACL,EAAE,OAAO,OAAO;YACd,eAAe,CAAA,OAAQ,CAAC;oBACtB,GAAG,IAAI;oBACP,aAAa;oBACb,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,CAAC;QACH;IACF,GAAG;QAAC,YAAY,IAAI;QAAE,YAAY,QAAQ;QAAE;QAAU;QAAY;KAAQ;IAE1E,SAAS;IACT,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,aAAa,OAAO,EAAE;IACxB,GAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB;YAAE,OAAO;YAAkB,OAAO;YAAY,aAAa;QAAe;QAC1E;YAAE,OAAO;YAAsB,OAAO;YAAQ,aAAa;QAAgB;QAC3E;YAAE,OAAO;YAAmB,OAAO;YAAU,aAAa;QAAa;KACxE;IAED,qBACE,8OAAC,iIAAA,CAAA,UAAW;QACV,QAAQ;QACR,SAAS;QACT,OAAM;QACN,WAAU;QACV,sBAAsB,CAAC,YAAY,WAAW;QAC9C,eAAe,CAAC,YAAY,WAAW;kBAEvC,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAM,WAAU;sCAA+C;;;;;;sCAGhE,8OAAC;4BAAI,WAAU;sCACZ,YAAY,IAAI,iBACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;wCAAyB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAChF,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;kDAEvE,8OAAC;wCAAK,WAAU;kDAAyB,YAAY,IAAI,CAAC,IAAI;;;;;;kDAC9D,8OAAC;wCACC,SAAS;wCACT,WAAU;wCACV,UAAU,YAAY,WAAW;kDAClC;;;;;;;;;;;yFAKH,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;wCAAqC,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC5F,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;kDAEvE,8OAAC;wCACC,SAAS;wCACT,WAAU;wCACV,UAAU,YAAY,WAAW;kDAClC;;;;;;kDAGD,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;;;;;;sCAIhD,8OAAC;4BACC,KAAK;4BACL,MAAK;4BACL,QAAO;4BACP,UAAU;4BACV,WAAU;;;;;;;;;;;;gBAKb,YAAY,IAAI,kBACf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAM,WAAU;sCAA+C;;;;;;sCAGhE,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,uBACpB,8OAAC;oCAAyB,WAAU;;sDAClC,8OAAC;4CACC,MAAK;4CACL,MAAK;4CACL,OAAO,OAAO,KAAK;4CACnB,SAAS,YAAY,QAAQ,KAAK,OAAO,KAAK;4CAC9C,UAAU,IAAM,qBAAqB,OAAO,KAAK;4CACjD,WAAU;4CACV,UAAU,YAAY,WAAW;;;;;;sDAEnC,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DAAqC,OAAO,KAAK;;;;;;8DAChE,8OAAC;oDAAI,WAAU;8DAAyB,OAAO,WAAW;;;;;;;;;;;;;mCAZlD,OAAO,KAAK;;;;;;;;;;;;;;;;gBAqB/B,YAAY,KAAK,kBAChB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;gCAAmC,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CAC1F,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;0CAEvE,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,8OAAC;wCAAE,WAAU;kDAA6B,YAAY,KAAK;;;;;;;;;;;;;;;;;;;;;;;gBAOlE,YAAY,OAAO,kBAClB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;gCAAqC,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CAC5F,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;0CAEvE,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,8OAAC;wCAAE,WAAU;kDAA8B;;;;;;oCAC1C,YAAY,YAAY,kBACvB,8OAAC;wCAAE,WAAU;;4CAA8B;4CACnC,YAAY,YAAY,CAAC,eAAe;4CAAC;4CAAQ,YAAY,YAAY,CAAC,eAAe;;;;;;;;;;;;;;;;;;;;;;;;gBAS1G,CAAC,YAAY,OAAO,kBACnB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,WAAU;4BACV,UAAU,YAAY,WAAW;sCAClC;;;;;;sCAGD,8OAAC;4BACC,SAAS;4BACT,UAAU,CAAC,YAAY,IAAI,IAAI,YAAY,WAAW;4BACtD,WAAU;sCAET,YAAY,WAAW,iBACtB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;wCAA6C,MAAK;wCAAO,SAAQ;;0DAC9E,8OAAC;gDAAO,WAAU;gDAAa,IAAG;gDAAK,IAAG;gDAAK,GAAE;gDAAK,QAAO;gDAAe,aAAY;;;;;;0DACxF,8OAAC;gDAAK,WAAU;gDAAa,MAAK;gDAAe,GAAE;;;;;;;;;;;;oCAC/C;;;;;;2EAIR;;;;;;;;;;;;gBAOP,YAAY,OAAO,kBAClB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;;;;;AAQjD;uCAEe", "debugId": null}}, {"offset": {"line": 12058, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/components/DataManagementDropdown.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useRef, useEffect, useState, useCallback } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useAppStore } from '@/stores/useAppStore';\nimport { ImportStrategy } from '@/utils/dataImport';\nimport ImportModal, { ImportStrategy as ModalImportStrategy } from './ImportModal';\nimport {\n  Download,\n  Upload,\n  FileText,\n  Database,\n  ChevronDown,\n  Loader2,\n  CheckCircle,\n  AlertCircle\n} from 'lucide-react';\n\nexport function DataManagementDropdown() {\n  const {\n    dataManagement,\n    setDataManagementDropdown,\n    exportData,\n    importData\n  } = useAppStore();\n\n  const [showImportModal, setShowImportModal] = useState(false);\n\n  const dropdownRef = useRef<HTMLDivElement>(null);\n\n  // 点击外部关闭下拉菜单\n  useEffect(() => {\n    function handleClickOutside(event: MouseEvent) {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setDataManagementDropdown(false);\n      }\n    }\n\n    if (dataManagement.showDropdown) {\n      document.addEventListener('mousedown', handleClickOutside);\n    }\n\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, [dataManagement.showDropdown, setDataManagementDropdown]);\n\n  // 处理导出\n  const handleExport = async (format: 'json' | 'csv') => {\n    try {\n      await exportData(format);\n      setDataManagementDropdown(false);\n    } catch (error) {\n      console.error('导出失败:', error);\n      // 这里可以添加错误提示\n    }\n  };\n\n  // 处理导入数据按钮点击\n  const handleImportClick = useCallback(() => {\n    setShowImportModal(true);\n    setDataManagementDropdown(false);\n  }, [setDataManagementDropdown]);\n\n  // 处理导入\n  const handleImport = useCallback(async (file: File, strategy: ModalImportStrategy) => {\n    // 转换策略类型\n    const importStrategyMap: Record<ModalImportStrategy, ImportStrategy> = {\n      'merge': ImportStrategy.MERGE,\n      'overwrite': ImportStrategy.OVERWRITE,\n      'append': ImportStrategy.ADD_ONLY\n    };\n\n    try {\n      await importData(file, importStrategyMap[strategy]);\n      // 不再设置showResult，让ImportModal自己处理成功状态\n    } catch (error) {\n      console.error('导入失败:', error);\n      throw error; // 重新抛出错误，让ImportModal处理\n    }\n  }, [importData]);\n\n  // 处理导入模态框关闭\n  const handleImportModalClose = useCallback(() => {\n    setShowImportModal(false);\n  }, []);\n\n  // 下拉菜单项组件\n  const DropdownItem = ({ \n    icon: Icon, \n    label, \n    onClick, \n    disabled = false \n  }: {\n    icon: React.ComponentType<any>;\n    label: string;\n    onClick: () => void;\n    disabled?: boolean;\n  }) => (\n    <motion.button\n      onClick={onClick}\n      disabled={disabled}\n      className={`w-full flex items-center px-4 py-3 text-left hover:bg-neutral-50 transition-colors ${\n        disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'\n      }`}\n      whileHover={disabled ? {} : { backgroundColor: 'var(--neutral-50)' }}\n      whileTap={disabled ? {} : { scale: 0.98 }}\n    >\n      <Icon size={16} className=\"mr-3\" style={{ color: 'var(--neutral-600)' }} />\n      <span style={{ \n        fontSize: 'var(--font-size-sm)', \n        color: 'var(--neutral-700)',\n        fontWeight: 'var(--font-weight-medium)'\n      }}>\n        {label}\n      </span>\n    </motion.button>\n  );\n\n  return (\n    <div className=\"relative\" ref={dropdownRef}>\n      {/* 主按钮 */}\n      <motion.button\n        onClick={() => setDataManagementDropdown(!dataManagement.showDropdown)}\n        className={`btn ${dataManagement.showDropdown ? 'btn-primary' : 'btn-secondary'} flex items-center`}\n        whileHover={{ scale: 1.02 }}\n        whileTap={{ scale: 0.98 }}\n        transition={{ duration: 0.15 }}\n        disabled={dataManagement.isExporting || dataManagement.isImporting}\n      >\n        {dataManagement.isExporting || dataManagement.isImporting ? (\n          <Loader2 size={16} className=\"mr-2 animate-spin\" />\n        ) : (\n          <Database size={16} className=\"mr-2\" />\n        )}\n        数据管理\n        <ChevronDown \n          size={14} \n          className={`ml-2 transition-transform ${\n            dataManagement.showDropdown ? 'rotate-180' : ''\n          }`} \n        />\n      </motion.button>\n\n      {/* 下拉菜单 */}\n      <AnimatePresence>\n        {dataManagement.showDropdown && (\n          <motion.div\n            initial={{ opacity: 0, y: -10, scale: 0.95 }}\n            animate={{ opacity: 1, y: 0, scale: 1 }}\n            exit={{ opacity: 0, y: -10, scale: 0.95 }}\n            transition={{ duration: 0.15, ease: [0.4, 0, 0.2, 1] }}\n            className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border z-50\"\n            style={{\n              borderColor: 'var(--neutral-200)',\n              boxShadow: 'var(--shadow-lg)'\n            }}\n          >\n            <div className=\"py-2\">\n              <DropdownItem\n                icon={Download}\n                label=\"导出 JSON\"\n                onClick={() => handleExport('json')}\n                disabled={dataManagement.isExporting || dataManagement.isImporting}\n              />\n              <DropdownItem\n                icon={FileText}\n                label=\"导出 CSV\"\n                onClick={() => handleExport('csv')}\n                disabled={dataManagement.isExporting || dataManagement.isImporting}\n              />\n              <div className=\"border-t my-2\" style={{ borderColor: 'var(--neutral-200)' }} />\n              <DropdownItem\n                icon={Upload}\n                label=\"导入数据\"\n                onClick={handleImportClick}\n                disabled={dataManagement.isExporting || dataManagement.isImporting}\n              />\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n\n\n      {/* 新的Portal导入模态框 */}\n      <ImportModal\n        isOpen={showImportModal}\n        onClose={handleImportModalClose}\n        onImport={handleImport}\n      />\n\n\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AAkBO,SAAS;IACd,MAAM,EACJ,cAAc,EACd,yBAAyB,EACzB,UAAU,EACV,UAAU,EACX,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IAEd,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,aAAa;IACb,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,mBAAmB,KAAiB;YAC3C,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,0BAA0B;YAC5B;QACF;QAEA,IAAI,eAAe,YAAY,EAAE;YAC/B,SAAS,gBAAgB,CAAC,aAAa;QACzC;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG;QAAC,eAAe,YAAY;QAAE;KAA0B;IAE3D,OAAO;IACP,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,WAAW;YACjB,0BAA0B;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;QACvB,aAAa;QACf;IACF;IAEA,aAAa;IACb,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,mBAAmB;QACnB,0BAA0B;IAC5B,GAAG;QAAC;KAA0B;IAE9B,OAAO;IACP,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,MAAY;QAClD,SAAS;QACT,MAAM,oBAAiE;YACrE,SAAS,0HAAA,CAAA,iBAAc,CAAC,KAAK;YAC7B,aAAa,0HAAA,CAAA,iBAAc,CAAC,SAAS;YACrC,UAAU,0HAAA,CAAA,iBAAc,CAAC,QAAQ;QACnC;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,iBAAiB,CAAC,SAAS;QAClD,sCAAsC;QACxC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;YACvB,MAAM,OAAO,wBAAwB;QACvC;IACF,GAAG;QAAC;KAAW;IAEf,YAAY;IACZ,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACzC,mBAAmB;IACrB,GAAG,EAAE;IAEL,UAAU;IACV,MAAM,eAAe,CAAC,EACpB,MAAM,IAAI,EACV,KAAK,EACL,OAAO,EACP,WAAW,KAAK,EAMjB,iBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;YACZ,SAAS;YACT,UAAU;YACV,WAAW,CAAC,mFAAmF,EAC7F,WAAW,kCAAkC,kBAC7C;YACF,YAAY,WAAW,CAAC,IAAI;gBAAE,iBAAiB;YAAoB;YACnE,UAAU,WAAW,CAAC,IAAI;gBAAE,OAAO;YAAK;;8BAExC,8OAAC;oBAAK,MAAM;oBAAI,WAAU;oBAAO,OAAO;wBAAE,OAAO;oBAAqB;;;;;;8BACtE,8OAAC;oBAAK,OAAO;wBACX,UAAU;wBACV,OAAO;wBACP,YAAY;oBACd;8BACG;;;;;;;;;;;;IAKP,qBACE,8OAAC;QAAI,WAAU;QAAW,KAAK;;0BAE7B,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS,IAAM,0BAA0B,CAAC,eAAe,YAAY;gBACrE,WAAW,CAAC,IAAI,EAAE,eAAe,YAAY,GAAG,gBAAgB,gBAAgB,kBAAkB,CAAC;gBACnG,YAAY;oBAAE,OAAO;gBAAK;gBAC1B,UAAU;oBAAE,OAAO;gBAAK;gBACxB,YAAY;oBAAE,UAAU;gBAAK;gBAC7B,UAAU,eAAe,WAAW,IAAI,eAAe,WAAW;;oBAEjE,eAAe,WAAW,IAAI,eAAe,WAAW,iBACvD,8OAAC,iNAAA,CAAA,UAAO;wBAAC,MAAM;wBAAI,WAAU;;;;;6CAE7B,8OAAC,0MAAA,CAAA,WAAQ;wBAAC,MAAM;wBAAI,WAAU;;;;;;oBAC9B;kCAEF,8OAAC,oNAAA,CAAA,cAAW;wBACV,MAAM;wBACN,WAAW,CAAC,0BAA0B,EACpC,eAAe,YAAY,GAAG,eAAe,IAC7C;;;;;;;;;;;;0BAKN,8OAAC,yLAAA,CAAA,kBAAe;0BACb,eAAe,YAAY,kBAC1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;wBAAI,OAAO;oBAAK;oBAC3C,SAAS;wBAAE,SAAS;wBAAG,GAAG;wBAAG,OAAO;oBAAE;oBACtC,MAAM;wBAAE,SAAS;wBAAG,GAAG,CAAC;wBAAI,OAAO;oBAAK;oBACxC,YAAY;wBAAE,UAAU;wBAAM,MAAM;4BAAC;4BAAK;4BAAG;4BAAK;yBAAE;oBAAC;oBACrD,WAAU;oBACV,OAAO;wBACL,aAAa;wBACb,WAAW;oBACb;8BAEA,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAM,0MAAA,CAAA,WAAQ;gCACd,OAAM;gCACN,SAAS,IAAM,aAAa;gCAC5B,UAAU,eAAe,WAAW,IAAI,eAAe,WAAW;;;;;;0CAEpE,8OAAC;gCACC,MAAM,8MAAA,CAAA,WAAQ;gCACd,OAAM;gCACN,SAAS,IAAM,aAAa;gCAC5B,UAAU,eAAe,WAAW,IAAI,eAAe,WAAW;;;;;;0CAEpE,8OAAC;gCAAI,WAAU;gCAAgB,OAAO;oCAAE,aAAa;gCAAqB;;;;;;0CAC1E,8OAAC;gCACC,MAAM,sMAAA,CAAA,SAAM;gCACZ,OAAM;gCACN,SAAS;gCACT,UAAU,eAAe,WAAW,IAAI,eAAe,WAAW;;;;;;;;;;;;;;;;;;;;;;0BAU5E,8OAAC,iIAAA,CAAA,UAAW;gBACV,QAAQ;gBACR,SAAS;gBACT,UAAU;;;;;;;;;;;;AAMlB", "debugId": null}}, {"offset": {"line": 12341, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/utils/dataExport.ts"], "sourcesContent": ["/**\n * 数据导出工具函数\n * 支持JSON和CSV格式的数据导出\n */\n\nimport type { Activity, TimeBlock, DailyStats } from '@/types';\n\n// 导出数据的版本信息\nexport const EXPORT_VERSION = '1.0.0';\n\n// 导出数据的完整结构\nexport interface ExportData {\n  version: string;\n  exportDate: string;\n  metadata: {\n    totalDays: number;\n    totalActivities: number;\n    totalTimeBlocks: number;\n    dateRange: {\n      start: string;\n      end: string;\n    };\n  };\n  data: {\n    activities: Activity[];\n    timeBlocks: Record<string, TimeBlock[]>;\n    dailyStats: Record<string, DailyStats>;\n  };\n}\n\n// CSV导出的时间块数据结构\nexport interface CSVTimeBlockData {\n  date: string;\n  timeSlot: number;\n  startTime: string;\n  endTime: string;\n  activityId: string;\n  activityName: string;\n  activityColor: string;\n  activityDescription: string;\n  activityIcon: string;\n  duration: number; // 分钟数\n  status: string; // 状态：已分配活动/空闲时间\n}\n\n/**\n * 导出JSON格式数据\n */\nexport function exportToJSON(\n  activities: Activity[],\n  timeBlocks: Record<string, TimeBlock[]>,\n  dailyStats: Record<string, DailyStats>,\n  dateRange?: { start: string; end: string }\n): ExportData {\n  // 过滤日期范围内的数据\n  let filteredTimeBlocks = timeBlocks;\n  let filteredDailyStats = dailyStats;\n\n  if (dateRange) {\n    filteredTimeBlocks = {};\n    filteredDailyStats = {};\n\n    Object.keys(timeBlocks).forEach(date => {\n      if (date >= dateRange.start && date <= dateRange.end) {\n        filteredTimeBlocks[date] = timeBlocks[date];\n      }\n    });\n\n    Object.keys(dailyStats).forEach(date => {\n      if (date >= dateRange.start && date <= dateRange.end) {\n        filteredDailyStats[date] = dailyStats[date];\n      }\n    });\n  }\n\n  // 计算元数据\n  const dates = Object.keys(filteredTimeBlocks);\n  const totalTimeBlocks = Object.values(filteredTimeBlocks)\n    .reduce((total, blocks) => total + blocks.length, 0);\n\n  const exportData: ExportData = {\n    version: EXPORT_VERSION,\n    exportDate: new Date().toISOString(),\n    metadata: {\n      totalDays: dates.length,\n      totalActivities: activities.length,\n      totalTimeBlocks,\n      dateRange: {\n        start: dates.length > 0 ? dates.sort()[0] : '',\n        end: dates.length > 0 ? dates.sort()[dates.length - 1] : ''\n      }\n    },\n    data: {\n      activities,\n      timeBlocks: filteredTimeBlocks,\n      dailyStats: filteredDailyStats\n    }\n  };\n\n  return exportData;\n}\n\n/**\n * 导出CSV格式数据\n */\nexport function exportToCSV(\n  activities: Activity[],\n  timeBlocks: Record<string, TimeBlock[]>,\n  dateRange?: { start: string; end: string }\n): string {\n  // 创建活动映射表\n  const activityMap = new Map<string, Activity>();\n  activities.forEach(activity => {\n    activityMap.set(activity.id, activity);\n  });\n\n  // 收集CSV数据\n  const csvData: CSVTimeBlockData[] = [];\n\n  Object.entries(timeBlocks).forEach(([date, blocks]) => {\n    // 检查日期范围\n    if (dateRange && (date < dateRange.start || date > dateRange.end)) {\n      return;\n    }\n\n    blocks.forEach(block => {\n      const activity = block.activityId ? activityMap.get(block.activityId) : null;\n      csvData.push({\n        date: block.date,\n        timeSlot: block.timeSlot,\n        startTime: block.startTime,\n        endTime: block.endTime,\n        activityId: block.activityId || '',\n        activityName: activity?.name || '空闲时间',\n        activityColor: activity?.color || '#f3f4f6',\n        activityDescription: activity?.description || '未分配活动的时间段',\n        activityIcon: activity?.icon || '⏰',\n        duration: 30, // 每个时间块30分钟\n        status: activity ? '已分配活动' : '空闲时间'\n      });\n    });\n  });\n\n  // 按日期和时间槽排序\n  csvData.sort((a, b) => {\n    if (a.date !== b.date) {\n      return a.date.localeCompare(b.date);\n    }\n    return a.timeSlot - b.timeSlot;\n  });\n\n  // 生成CSV内容\n  const headers = [\n    '日期',\n    '时间槽',\n    '开始时间',\n    '结束时间',\n    '活动ID',\n    '活动名称',\n    '活动颜色',\n    '活动描述',\n    '活动图标',\n    '持续时间(分钟)',\n    '状态'\n  ];\n\n  const csvRows = [\n    headers.join(','),\n    ...csvData.map(row => [\n      row.date,\n      row.timeSlot.toString(),\n      row.startTime,\n      row.endTime,\n      row.activityId,\n      `\"${row.activityName}\"`, // 用引号包围，防止逗号问题\n      row.activityColor,\n      `\"${row.activityDescription}\"`, // 用引号包围，防止逗号问题\n      row.activityIcon,\n      row.duration.toString(),\n      `\"${row.status}\"`\n    ].join(','))\n  ];\n\n  return csvRows.join('\\n');\n}\n\n/**\n * 生成导出文件名\n */\nexport function generateExportFileName(\n  format: 'json' | 'csv',\n  dateRange?: { start: string; end: string }\n): string {\n  const now = new Date();\n  const timestamp = now.toISOString().split('T')[0]; // YYYY-MM-DD格式\n\n  let fileName = `chronospect_export_${timestamp}`;\n\n  if (dateRange) {\n    fileName += `_${dateRange.start}_to_${dateRange.end}`;\n  }\n\n  fileName += `.${format}`;\n  return fileName;\n}\n\n/**\n * 下载文件到本地 - 增强版本，确保在真实浏览器环境中正常工作\n */\nexport function downloadFile(content: string, fileName: string, mimeType: string): void {\n  console.log(`[下载调试] 开始下载文件: ${fileName}, MIME类型: ${mimeType}`);\n\n  try {\n    // 确保文件名是安全的，移除可能的特殊字符\n    const safeFileName = fileName.replace(/[<>:\"/\\\\|?*]/g, '_');\n    console.log(`[下载调试] 安全文件名: ${safeFileName}`);\n\n    // 创建Blob对象，添加BOM以确保UTF-8编码正确\n    const bom = mimeType.includes('csv') ? '\\uFEFF' : ''; // CSV文件添加BOM\n    const blob = new Blob([bom + content], {\n      type: mimeType + ';charset=utf-8'\n    });\n    console.log(`[下载调试] Blob创建成功, 大小: ${blob.size} bytes`);\n\n    // 检查是否支持现代下载API (IE/Edge)\n    if (window.navigator && (window.navigator as any).msSaveOrOpenBlob) {\n      console.log('[下载调试] 使用IE/Edge下载API');\n      (window.navigator as any).msSaveOrOpenBlob(blob, safeFileName);\n      return;\n    }\n\n    // 创建下载链接\n    const url = URL.createObjectURL(blob);\n    console.log(`[下载调试] Blob URL创建: ${url}`);\n\n    const link = document.createElement('a');\n\n    // 设置下载属性\n    link.href = url;\n    link.download = safeFileName;\n    link.style.display = 'none';\n    link.style.position = 'absolute';\n    link.style.left = '-9999px';\n    link.style.top = '-9999px';\n\n    // 设置额外属性确保兼容性\n    link.setAttribute('download', safeFileName);\n    link.setAttribute('rel', 'noopener');\n\n    console.log(`[下载调试] 链接元素创建完成, href: ${link.href}, download: ${link.download}`);\n\n    // 添加到DOM\n    document.body.appendChild(link);\n    console.log('[下载调试] 链接元素已添加到DOM');\n\n    // 确保DOM更新完成后再触发下载\n    requestAnimationFrame(() => {\n      try {\n        // 触发下载 - 使用多种方法确保兼容性\n        console.log('[下载调试] 开始触发下载');\n\n        // 方法1: 直接调用click()\n        if (typeof link.click === 'function') {\n          console.log('[下载调试] 使用link.click()方法');\n          link.click();\n        } else {\n          // 方法2: 创建并分发点击事件\n          console.log('[下载调试] 使用事件分发方法');\n          const clickEvent = new MouseEvent('click', {\n            view: window,\n            bubbles: true,\n            cancelable: true,\n            button: 0\n          });\n          link.dispatchEvent(clickEvent);\n        }\n\n        console.log('[下载调试] 下载触发完成');\n\n        // 延迟清理，确保下载开始\n        setTimeout(() => {\n          try {\n            if (document.body.contains(link)) {\n              document.body.removeChild(link);\n              console.log('[下载调试] 链接元素已从DOM移除');\n            }\n            URL.revokeObjectURL(url);\n            console.log('[下载调试] Blob URL已释放');\n          } catch (cleanupError) {\n            console.warn('[下载调试] 清理过程中出现错误:', cleanupError);\n          }\n        }, 1000); // 增加延迟时间确保下载开始\n\n      } catch (triggerError) {\n        console.error('[下载调试] 触发下载时出错:', triggerError);\n        // 如果触发失败，尝试备用方法\n        fallbackDownload(content, safeFileName, mimeType);\n      }\n    });\n\n  } catch (error) {\n    console.error('[下载调试] 主要下载方法失败:', error);\n    fallbackDownload(content, fileName, mimeType);\n  }\n}\n\n/**\n * 备用下载方法\n */\nfunction fallbackDownload(content: string, fileName: string, mimeType: string): void {\n  console.log('[下载调试] 使用备用下载方法');\n\n  try {\n    // 方法1: 使用data URL\n    const dataUrl = `data:${mimeType};charset=utf-8,${encodeURIComponent(content)}`;\n    const link = document.createElement('a');\n    link.href = dataUrl;\n    link.download = fileName;\n    link.style.display = 'none';\n\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n\n    console.log('[下载调试] 备用方法1 (data URL) 执行完成');\n  } catch (dataUrlError) {\n    console.error('[下载调试] 备用方法1失败:', dataUrlError);\n\n    try {\n      // 方法2: 打开新窗口\n      const dataUrl = `data:${mimeType};charset=utf-8,${encodeURIComponent(content)}`;\n      const newWindow = window.open(dataUrl, '_blank');\n\n      if (newWindow) {\n        console.log('[下载调试] 备用方法2 (新窗口) 执行完成');\n      } else {\n        console.error('[下载调试] 备用方法2失败: 无法打开新窗口');\n        alert('下载失败，请检查浏览器的弹窗拦截设置，或尝试手动保存文件');\n      }\n    } catch (windowError) {\n      console.error('[下载调试] 备用方法2失败:', windowError);\n      alert('下载失败，请联系技术支持');\n    }\n  }\n}\n\n/**\n * 导出JSON文件\n */\nexport function exportJSONFile(\n  activities: Activity[],\n  timeBlocks: Record<string, TimeBlock[]>,\n  dailyStats: Record<string, DailyStats>,\n  dateRange?: { start: string; end: string }\n): void {\n  const exportData = exportToJSON(activities, timeBlocks, dailyStats, dateRange);\n  const jsonContent = JSON.stringify(exportData, null, 2);\n  const fileName = generateExportFileName('json', dateRange);\n  \n  downloadFile(jsonContent, fileName, 'application/json');\n}\n\n/**\n * 导出CSV文件\n */\nexport function exportCSVFile(\n  activities: Activity[],\n  timeBlocks: Record<string, TimeBlock[]>,\n  dateRange?: { start: string; end: string }\n): void {\n  const csvContent = exportToCSV(activities, timeBlocks, dateRange);\n  const fileName = generateExportFileName('csv', dateRange);\n  \n  downloadFile(csvContent, fileName, 'text/csv');\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;AAKM,MAAM,iBAAiB;AAwCvB,SAAS,aACd,UAAsB,EACtB,UAAuC,EACvC,UAAsC,EACtC,SAA0C;IAE1C,aAAa;IACb,IAAI,qBAAqB;IACzB,IAAI,qBAAqB;IAEzB,IAAI,WAAW;QACb,qBAAqB,CAAC;QACtB,qBAAqB,CAAC;QAEtB,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,CAAA;YAC9B,IAAI,QAAQ,UAAU,KAAK,IAAI,QAAQ,UAAU,GAAG,EAAE;gBACpD,kBAAkB,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK;YAC7C;QACF;QAEA,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,CAAA;YAC9B,IAAI,QAAQ,UAAU,KAAK,IAAI,QAAQ,UAAU,GAAG,EAAE;gBACpD,kBAAkB,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK;YAC7C;QACF;IACF;IAEA,QAAQ;IACR,MAAM,QAAQ,OAAO,IAAI,CAAC;IAC1B,MAAM,kBAAkB,OAAO,MAAM,CAAC,oBACnC,MAAM,CAAC,CAAC,OAAO,SAAW,QAAQ,OAAO,MAAM,EAAE;IAEpD,MAAM,aAAyB;QAC7B,SAAS;QACT,YAAY,IAAI,OAAO,WAAW;QAClC,UAAU;YACR,WAAW,MAAM,MAAM;YACvB,iBAAiB,WAAW,MAAM;YAClC;YACA,WAAW;gBACT,OAAO,MAAM,MAAM,GAAG,IAAI,MAAM,IAAI,EAAE,CAAC,EAAE,GAAG;gBAC5C,KAAK,MAAM,MAAM,GAAG,IAAI,MAAM,IAAI,EAAE,CAAC,MAAM,MAAM,GAAG,EAAE,GAAG;YAC3D;QACF;QACA,MAAM;YACJ;YACA,YAAY;YACZ,YAAY;QACd;IACF;IAEA,OAAO;AACT;AAKO,SAAS,YACd,UAAsB,EACtB,UAAuC,EACvC,SAA0C;IAE1C,UAAU;IACV,MAAM,cAAc,IAAI;IACxB,WAAW,OAAO,CAAC,CAAA;QACjB,YAAY,GAAG,CAAC,SAAS,EAAE,EAAE;IAC/B;IAEA,UAAU;IACV,MAAM,UAA8B,EAAE;IAEtC,OAAO,OAAO,CAAC,YAAY,OAAO,CAAC,CAAC,CAAC,MAAM,OAAO;QAChD,SAAS;QACT,IAAI,aAAa,CAAC,OAAO,UAAU,KAAK,IAAI,OAAO,UAAU,GAAG,GAAG;YACjE;QACF;QAEA,OAAO,OAAO,CAAC,CAAA;YACb,MAAM,WAAW,MAAM,UAAU,GAAG,YAAY,GAAG,CAAC,MAAM,UAAU,IAAI;YACxE,QAAQ,IAAI,CAAC;gBACX,MAAM,MAAM,IAAI;gBAChB,UAAU,MAAM,QAAQ;gBACxB,WAAW,MAAM,SAAS;gBAC1B,SAAS,MAAM,OAAO;gBACtB,YAAY,MAAM,UAAU,IAAI;gBAChC,cAAc,UAAU,QAAQ;gBAChC,eAAe,UAAU,SAAS;gBAClC,qBAAqB,UAAU,eAAe;gBAC9C,cAAc,UAAU,QAAQ;gBAChC,UAAU;gBACV,QAAQ,WAAW,UAAU;YAC/B;QACF;IACF;IAEA,YAAY;IACZ,QAAQ,IAAI,CAAC,CAAC,GAAG;QACf,IAAI,EAAE,IAAI,KAAK,EAAE,IAAI,EAAE;YACrB,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;QACpC;QACA,OAAO,EAAE,QAAQ,GAAG,EAAE,QAAQ;IAChC;IAEA,UAAU;IACV,MAAM,UAAU;QACd;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,UAAU;QACd,QAAQ,IAAI,CAAC;WACV,QAAQ,GAAG,CAAC,CAAA,MAAO;gBACpB,IAAI,IAAI;gBACR,IAAI,QAAQ,CAAC,QAAQ;gBACrB,IAAI,SAAS;gBACb,IAAI,OAAO;gBACX,IAAI,UAAU;gBACd,CAAC,CAAC,EAAE,IAAI,YAAY,CAAC,CAAC,CAAC;gBACvB,IAAI,aAAa;gBACjB,CAAC,CAAC,EAAE,IAAI,mBAAmB,CAAC,CAAC,CAAC;gBAC9B,IAAI,YAAY;gBAChB,IAAI,QAAQ,CAAC,QAAQ;gBACrB,CAAC,CAAC,EAAE,IAAI,MAAM,CAAC,CAAC,CAAC;aAClB,CAAC,IAAI,CAAC;KACR;IAED,OAAO,QAAQ,IAAI,CAAC;AACtB;AAKO,SAAS,uBACd,MAAsB,EACtB,SAA0C;IAE1C,MAAM,MAAM,IAAI;IAChB,MAAM,YAAY,IAAI,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,eAAe;IAElE,IAAI,WAAW,CAAC,mBAAmB,EAAE,WAAW;IAEhD,IAAI,WAAW;QACb,YAAY,CAAC,CAAC,EAAE,UAAU,KAAK,CAAC,IAAI,EAAE,UAAU,GAAG,EAAE;IACvD;IAEA,YAAY,CAAC,CAAC,EAAE,QAAQ;IACxB,OAAO;AACT;AAKO,SAAS,aAAa,OAAe,EAAE,QAAgB,EAAE,QAAgB;IAC9E,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,SAAS,UAAU,EAAE,UAAU;IAE7D,IAAI;QACF,sBAAsB;QACtB,MAAM,eAAe,SAAS,OAAO,CAAC,iBAAiB;QACvD,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,cAAc;QAE3C,6BAA6B;QAC7B,MAAM,MAAM,SAAS,QAAQ,CAAC,SAAS,WAAW,IAAI,aAAa;QACnE,MAAM,OAAO,IAAI,KAAK;YAAC,MAAM;SAAQ,EAAE;YACrC,MAAM,WAAW;QACnB;QACA,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,KAAK,IAAI,CAAC,MAAM,CAAC;QAErD,0BAA0B;QAC1B,IAAI,OAAO,SAAS,IAAI,AAAC,OAAO,SAAS,CAAS,gBAAgB,EAAE;YAClE,QAAQ,GAAG,CAAC;YACX,OAAO,SAAS,CAAS,gBAAgB,CAAC,MAAM;YACjD;QACF;QAEA,SAAS;QACT,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,KAAK;QAEvC,MAAM,OAAO,SAAS,aAAa,CAAC;QAEpC,SAAS;QACT,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG;QAChB,KAAK,KAAK,CAAC,OAAO,GAAG;QACrB,KAAK,KAAK,CAAC,QAAQ,GAAG;QACtB,KAAK,KAAK,CAAC,IAAI,GAAG;QAClB,KAAK,KAAK,CAAC,GAAG,GAAG;QAEjB,cAAc;QACd,KAAK,YAAY,CAAC,YAAY;QAC9B,KAAK,YAAY,CAAC,OAAO;QAEzB,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,KAAK,IAAI,CAAC,YAAY,EAAE,KAAK,QAAQ,EAAE;QAE7E,SAAS;QACT,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,QAAQ,GAAG,CAAC;QAEZ,kBAAkB;QAClB,sBAAsB;YACpB,IAAI;gBACF,qBAAqB;gBACrB,QAAQ,GAAG,CAAC;gBAEZ,mBAAmB;gBACnB,IAAI,OAAO,KAAK,KAAK,KAAK,YAAY;oBACpC,QAAQ,GAAG,CAAC;oBACZ,KAAK,KAAK;gBACZ,OAAO;oBACL,iBAAiB;oBACjB,QAAQ,GAAG,CAAC;oBACZ,MAAM,aAAa,IAAI,WAAW,SAAS;wBACzC,MAAM;wBACN,SAAS;wBACT,YAAY;wBACZ,QAAQ;oBACV;oBACA,KAAK,aAAa,CAAC;gBACrB;gBAEA,QAAQ,GAAG,CAAC;gBAEZ,cAAc;gBACd,WAAW;oBACT,IAAI;wBACF,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,OAAO;4BAChC,SAAS,IAAI,CAAC,WAAW,CAAC;4BAC1B,QAAQ,GAAG,CAAC;wBACd;wBACA,IAAI,eAAe,CAAC;wBACpB,QAAQ,GAAG,CAAC;oBACd,EAAE,OAAO,cAAc;wBACrB,QAAQ,IAAI,CAAC,qBAAqB;oBACpC;gBACF,GAAG,OAAO,eAAe;YAE3B,EAAE,OAAO,cAAc;gBACrB,QAAQ,KAAK,CAAC,mBAAmB;gBACjC,gBAAgB;gBAChB,iBAAiB,SAAS,cAAc;YAC1C;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,iBAAiB,SAAS,UAAU;IACtC;AACF;AAEA;;CAEC,GACD,SAAS,iBAAiB,OAAe,EAAE,QAAgB,EAAE,QAAgB;IAC3E,QAAQ,GAAG,CAAC;IAEZ,IAAI;QACF,kBAAkB;QAClB,MAAM,UAAU,CAAC,KAAK,EAAE,SAAS,eAAe,EAAE,mBAAmB,UAAU;QAC/E,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG;QAChB,KAAK,KAAK,CAAC,OAAO,GAAG;QAErB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,KAAK,KAAK;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,cAAc;QACrB,QAAQ,KAAK,CAAC,mBAAmB;QAEjC,IAAI;YACF,aAAa;YACb,MAAM,UAAU,CAAC,KAAK,EAAE,SAAS,eAAe,EAAE,mBAAmB,UAAU;YAC/E,MAAM,YAAY,OAAO,IAAI,CAAC,SAAS;YAEvC,IAAI,WAAW;gBACb,QAAQ,GAAG,CAAC;YACd,OAAO;gBACL,QAAQ,KAAK,CAAC;gBACd,MAAM;YACR;QACF,EAAE,OAAO,aAAa;YACpB,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM;QACR;IACF;AACF;AAKO,SAAS,eACd,UAAsB,EACtB,UAAuC,EACvC,UAAsC,EACtC,SAA0C;IAE1C,MAAM,aAAa,aAAa,YAAY,YAAY,YAAY;IACpE,MAAM,cAAc,KAAK,SAAS,CAAC,YAAY,MAAM;IACrD,MAAM,WAAW,uBAAuB,QAAQ;IAEhD,aAAa,aAAa,UAAU;AACtC;AAKO,SAAS,cACd,UAAsB,EACtB,UAAuC,EACvC,SAA0C;IAE1C,MAAM,aAAa,YAAY,YAAY,YAAY;IACvD,MAAM,WAAW,uBAAuB,OAAO;IAE/C,aAAa,YAAY,UAAU;AACrC", "debugId": null}}, {"offset": {"line": 12605, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/components/DownloadTestPanel.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Download, FileText, Database, TestTube, CheckCircle, AlertCircle } from 'lucide-react';\nimport { downloadFile } from '@/utils/dataExport';\n\ninterface TestResult {\n  method: string;\n  success: boolean;\n  message: string;\n  timestamp: string;\n}\n\nexport function DownloadTestPanel() {\n  const [testResults, setTestResults] = useState<TestResult[]>([]);\n  const [isVisible, setIsVisible] = useState(false);\n\n  const addTestResult = (method: string, success: boolean, message: string) => {\n    const result: TestResult = {\n      method,\n      success,\n      message,\n      timestamp: new Date().toLocaleTimeString()\n    };\n    setTestResults(prev => [result, ...prev.slice(0, 9)]); // 保留最近10条记录\n  };\n\n  // 测试JSON下载\n  const testJSONDownload = () => {\n    try {\n      const testData = {\n        version: '1.0.0',\n        exportDate: new Date().toISOString(),\n        testData: {\n          message: 'This is a test JSON file from Chronospect',\n          timestamp: Date.now(),\n          items: ['item1', 'item2', 'item3']\n        }\n      };\n      \n      const jsonContent = JSON.stringify(testData, null, 2);\n      const fileName = `chronospect_test_${new Date().toISOString().split('T')[0]}.json`;\n      \n      downloadFile(jsonContent, fileName, 'application/json');\n      addTestResult('JSON下载', true, `测试文件: ${fileName}`);\n    } catch (error) {\n      addTestResult('JSON下载', false, `错误: ${(error as Error).message}`);\n    }\n  };\n\n  // 测试CSV下载\n  const testCSVDownload = () => {\n    try {\n      const csvContent = [\n        '日期,时间槽,开始时间,结束时间,活动名称,持续时间(分钟)',\n        '2025-07-28,1,00:00,00:30,测试活动1,30',\n        '2025-07-28,2,00:30,01:00,测试活动2,30',\n        '2025-07-28,3,01:00,01:30,测试活动3,30'\n      ].join('\\n');\n      \n      const fileName = `chronospect_test_${new Date().toISOString().split('T')[0]}.csv`;\n      \n      downloadFile(csvContent, fileName, 'text/csv');\n      addTestResult('CSV下载', true, `测试文件: ${fileName}`);\n    } catch (error) {\n      addTestResult('CSV下载', false, `错误: ${(error as Error).message}`);\n    }\n  };\n\n  // 测试小文件下载\n  const testSmallFileDownload = () => {\n    try {\n      const content = 'Hello, Chronospect! This is a test file.';\n      const fileName = `chronospect_small_test_${Date.now()}.txt`;\n      \n      downloadFile(content, fileName, 'text/plain');\n      addTestResult('小文件下载', true, `测试文件: ${fileName}`);\n    } catch (error) {\n      addTestResult('小文件下载', false, `错误: ${(error as Error).message}`);\n    }\n  };\n\n  // 清除测试结果\n  const clearResults = () => {\n    setTestResults([]);\n  };\n\n  if (!isVisible) {\n    return (\n      <motion.button\n        onClick={() => setIsVisible(true)}\n        className=\"fixed bottom-4 left-4 bg-blue-500 text-white p-3 rounded-full shadow-lg z-50\"\n        whileHover={{ scale: 1.1 }}\n        whileTap={{ scale: 0.9 }}\n        title=\"打开下载测试面板\"\n      >\n        <TestTube size={20} />\n      </motion.button>\n    );\n  }\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, x: -300 }}\n      animate={{ opacity: 1, x: 0 }}\n      className=\"fixed left-4 top-1/2 transform -translate-y-1/2 bg-white rounded-lg shadow-xl border p-4 w-80 max-h-96 overflow-y-auto z-50\"\n      style={{\n        borderColor: 'var(--neutral-200)',\n        boxShadow: 'var(--shadow-xl)'\n      }}\n    >\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 style={{\n          fontSize: 'var(--font-size-lg)',\n          fontWeight: 'var(--font-weight-semibold)',\n          color: 'var(--neutral-900)'\n        }}>\n          下载功能测试\n        </h3>\n        <button\n          onClick={() => setIsVisible(false)}\n          className=\"text-gray-500 hover:text-gray-700\"\n        >\n          ✕\n        </button>\n      </div>\n\n      <div className=\"space-y-2 mb-4\">\n        <motion.button\n          onClick={testJSONDownload}\n          className=\"w-full flex items-center justify-center px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors\"\n          whileHover={{ scale: 1.02 }}\n          whileTap={{ scale: 0.98 }}\n        >\n          <Database size={16} className=\"mr-2\" />\n          测试 JSON 下载\n        </motion.button>\n\n        <motion.button\n          onClick={testCSVDownload}\n          className=\"w-full flex items-center justify-center px-3 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors\"\n          whileHover={{ scale: 1.02 }}\n          whileTap={{ scale: 0.98 }}\n        >\n          <FileText size={16} className=\"mr-2\" />\n          测试 CSV 下载\n        </motion.button>\n\n        <motion.button\n          onClick={testSmallFileDownload}\n          className=\"w-full flex items-center justify-center px-3 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors\"\n          whileHover={{ scale: 1.02 }}\n          whileTap={{ scale: 0.98 }}\n        >\n          <Download size={16} className=\"mr-2\" />\n          测试小文件下载\n        </motion.button>\n\n        <motion.button\n          onClick={clearResults}\n          className=\"w-full px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors\"\n          whileHover={{ scale: 1.02 }}\n          whileTap={{ scale: 0.98 }}\n        >\n          清除结果\n        </motion.button>\n      </div>\n\n      <div className=\"border-t pt-3\" style={{ borderColor: 'var(--neutral-200)' }}>\n        <h4 style={{\n          fontSize: 'var(--font-size-sm)',\n          fontWeight: 'var(--font-weight-medium)',\n          color: 'var(--neutral-700)',\n          marginBottom: 'var(--spacing-2)'\n        }}>\n          测试结果:\n        </h4>\n\n        {testResults.length === 0 ? (\n          <p style={{\n            fontSize: 'var(--font-size-xs)',\n            color: 'var(--neutral-500)',\n            textAlign: 'center',\n            padding: 'var(--spacing-2)'\n          }}>\n            暂无测试结果\n          </p>\n        ) : (\n          <div className=\"space-y-2 max-h-40 overflow-y-auto\">\n            {testResults.map((result, index) => (\n              <div\n                key={index}\n                className={`p-2 rounded text-xs ${\n                  result.success \n                    ? 'bg-green-50 border border-green-200' \n                    : 'bg-red-50 border border-red-200'\n                }`}\n              >\n                <div className=\"flex items-center mb-1\">\n                  {result.success ? (\n                    <CheckCircle size={12} className=\"text-green-500 mr-1\" />\n                  ) : (\n                    <AlertCircle size={12} className=\"text-red-500 mr-1\" />\n                  )}\n                  <span className={`font-medium ${\n                    result.success ? 'text-green-700' : 'text-red-700'\n                  }`}>\n                    {result.method}\n                  </span>\n                  <span className=\"ml-auto text-gray-500\">\n                    {result.timestamp}\n                  </span>\n                </div>\n                <p className={result.success ? 'text-green-600' : 'text-red-600'}>\n                  {result.message}\n                </p>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n\n      <div className=\"mt-3 pt-3 border-t\" style={{ borderColor: 'var(--neutral-200)' }}>\n        <p style={{\n          fontSize: 'var(--font-size-xs)',\n          color: 'var(--neutral-500)',\n          lineHeight: '1.4'\n        }}>\n          💡 提示: 请检查浏览器的下载文件夹 (通常是 Downloads) 来确认文件是否成功下载。\n        </p>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AALA;;;;;;AAcO,SAAS;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,gBAAgB,CAAC,QAAgB,SAAkB;QACvD,MAAM,SAAqB;YACzB;YACA;YACA;YACA,WAAW,IAAI,OAAO,kBAAkB;QAC1C;QACA,eAAe,CAAA,OAAQ;gBAAC;mBAAW,KAAK,KAAK,CAAC,GAAG;aAAG,GAAG,YAAY;IACrE;IAEA,WAAW;IACX,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,WAAW;gBACf,SAAS;gBACT,YAAY,IAAI,OAAO,WAAW;gBAClC,UAAU;oBACR,SAAS;oBACT,WAAW,KAAK,GAAG;oBACnB,OAAO;wBAAC;wBAAS;wBAAS;qBAAQ;gBACpC;YACF;YAEA,MAAM,cAAc,KAAK,SAAS,CAAC,UAAU,MAAM;YACnD,MAAM,WAAW,CAAC,iBAAiB,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;YAElF,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD,EAAE,aAAa,UAAU;YACpC,cAAc,UAAU,MAAM,CAAC,MAAM,EAAE,UAAU;QACnD,EAAE,OAAO,OAAO;YACd,cAAc,UAAU,OAAO,CAAC,IAAI,EAAE,AAAC,MAAgB,OAAO,EAAE;QAClE;IACF;IAEA,UAAU;IACV,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,aAAa;gBACjB;gBACA;gBACA;gBACA;aACD,CAAC,IAAI,CAAC;YAEP,MAAM,WAAW,CAAC,iBAAiB,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;YAEjF,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD,EAAE,YAAY,UAAU;YACnC,cAAc,SAAS,MAAM,CAAC,MAAM,EAAE,UAAU;QAClD,EAAE,OAAO,OAAO;YACd,cAAc,SAAS,OAAO,CAAC,IAAI,EAAE,AAAC,MAAgB,OAAO,EAAE;QACjE;IACF;IAEA,UAAU;IACV,MAAM,wBAAwB;QAC5B,IAAI;YACF,MAAM,UAAU;YAChB,MAAM,WAAW,CAAC,uBAAuB,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC;YAE3D,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD,EAAE,SAAS,UAAU;YAChC,cAAc,SAAS,MAAM,CAAC,MAAM,EAAE,UAAU;QAClD,EAAE,OAAO,OAAO;YACd,cAAc,SAAS,OAAO,CAAC,IAAI,EAAE,AAAC,MAAgB,OAAO,EAAE;QACjE;IACF;IAEA,SAAS;IACT,MAAM,eAAe;QACnB,eAAe,EAAE;IACnB;IAEA,IAAI,CAAC,WAAW;QACd,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;YACZ,SAAS,IAAM,aAAa;YAC5B,WAAU;YACV,YAAY;gBAAE,OAAO;YAAI;YACzB,UAAU;gBAAE,OAAO;YAAI;YACvB,OAAM;sBAEN,cAAA,8OAAC,8MAAA,CAAA,WAAQ;gBAAC,MAAM;;;;;;;;;;;IAGtB;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG,CAAC;QAAI;QAC/B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,WAAU;QACV,OAAO;YACL,aAAa;YACb,WAAW;QACb;;0BAEA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,OAAO;4BACT,UAAU;4BACV,YAAY;4BACZ,OAAO;wBACT;kCAAG;;;;;;kCAGH,8OAAC;wBACC,SAAS,IAAM,aAAa;wBAC5B,WAAU;kCACX;;;;;;;;;;;;0BAKH,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,SAAS;wBACT,WAAU;wBACV,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;;0CAExB,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,MAAM;gCAAI,WAAU;;;;;;4BAAS;;;;;;;kCAIzC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,SAAS;wBACT,WAAU;wBACV,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;;0CAExB,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,MAAM;gCAAI,WAAU;;;;;;4BAAS;;;;;;;kCAIzC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,SAAS;wBACT,WAAU;wBACV,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;;0CAExB,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,MAAM;gCAAI,WAAU;;;;;;4BAAS;;;;;;;kCAIzC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,SAAS;wBACT,WAAU;wBACV,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;kCACzB;;;;;;;;;;;;0BAKH,8OAAC;gBAAI,WAAU;gBAAgB,OAAO;oBAAE,aAAa;gBAAqB;;kCACxE,8OAAC;wBAAG,OAAO;4BACT,UAAU;4BACV,YAAY;4BACZ,OAAO;4BACP,cAAc;wBAChB;kCAAG;;;;;;oBAIF,YAAY,MAAM,KAAK,kBACtB,8OAAC;wBAAE,OAAO;4BACR,UAAU;4BACV,OAAO;4BACP,WAAW;4BACX,SAAS;wBACX;kCAAG;;;;;6CAIH,8OAAC;wBAAI,WAAU;kCACZ,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,8OAAC;gCAEC,WAAW,CAAC,oBAAoB,EAC9B,OAAO,OAAO,GACV,wCACA,mCACJ;;kDAEF,8OAAC;wCAAI,WAAU;;4CACZ,OAAO,OAAO,iBACb,8OAAC,2NAAA,CAAA,cAAW;gDAAC,MAAM;gDAAI,WAAU;;;;;qEAEjC,8OAAC,oNAAA,CAAA,cAAW;gDAAC,MAAM;gDAAI,WAAU;;;;;;0DAEnC,8OAAC;gDAAK,WAAW,CAAC,YAAY,EAC5B,OAAO,OAAO,GAAG,mBAAmB,gBACpC;0DACC,OAAO,MAAM;;;;;;0DAEhB,8OAAC;gDAAK,WAAU;0DACb,OAAO,SAAS;;;;;;;;;;;;kDAGrB,8OAAC;wCAAE,WAAW,OAAO,OAAO,GAAG,mBAAmB;kDAC/C,OAAO,OAAO;;;;;;;+BAvBZ;;;;;;;;;;;;;;;;0BA+Bf,8OAAC;gBAAI,WAAU;gBAAqB,OAAO;oBAAE,aAAa;gBAAqB;0BAC7E,cAAA,8OAAC;oBAAE,OAAO;wBACR,UAAU;wBACV,OAAO;wBACP,YAAY;oBACd;8BAAG;;;;;;;;;;;;;;;;;AAMX", "debugId": null}}, {"offset": {"line": 12996, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/hooks/useViewPreloader.ts"], "sourcesContent": ["'use client';\n\nimport { useCallback, useRef, useTransition } from 'react';\nimport { useAppStore } from '@/stores/useAppStore';\n\nexport type ViewType = 'grid' | 'review';\n\ninterface ViewPreloaderOptions {\n  preloadDelay?: number; // hover预加载延迟（毫秒）\n  enableHoverPreload?: boolean; // 是否启用hover预加载\n}\n\ninterface ViewPreloaderReturn {\n  isPreloading: boolean;\n  isPending: boolean;\n  preloadView: (view: ViewType) => Promise<void>;\n  switchView: (view: ViewType) => void;\n  handleMouseEnter: (view: ViewType) => void;\n  handleMouseLeave: () => void;\n}\n\n/**\n * 智能视图预加载Hook\n * 提供hover预加载和无缝视图切换功能\n */\nexport function useViewPreloader(options: ViewPreloaderOptions = {}): ViewPreloaderReturn {\n  const {\n    preloadDelay = 300,\n    enableHoverPreload = true\n  } = options;\n\n  const {\n    currentDate,\n    getDailyStatsAsync,\n    setCurrentView,\n    ui,\n    setViewPreloading,\n    setViewPreloaded,\n    isViewPreloaded,\n    isViewPreloading\n  } = useAppStore();\n\n  const [isPending, startTransition] = useTransition();\n  const preloadTimeoutRef = useRef<NodeJS.Timeout | null>(null);\n  const preloadingRef = useRef<Set<string>>(new Set());\n\n  /**\n   * 预加载指定视图的数据\n   */\n  const preloadView = useCallback(async (view: ViewType): Promise<void> => {\n    const preloadKey = `${view}-${currentDate}`;\n\n    // 避免重复预加载\n    if (isViewPreloading(preloadKey) || isViewPreloaded(preloadKey)) {\n      return;\n    }\n\n    setViewPreloading(preloadKey, true);\n\n    try {\n      if (view === 'review') {\n        // 预加载复盘视图需要的数据\n        await getDailyStatsAsync(currentDate);\n\n        // 预加载相邻日期的数据（用于日期对比）\n        const yesterday = new Date(currentDate);\n        yesterday.setDate(yesterday.getDate() - 1);\n        const tomorrow = new Date(currentDate);\n        tomorrow.setDate(tomorrow.getDate() + 1);\n\n        await Promise.all([\n          getDailyStatsAsync(yesterday.toISOString().split('T')[0]),\n          getDailyStatsAsync(tomorrow.toISOString().split('T')[0])\n        ]);\n      }\n      // grid视图通常不需要额外预加载，因为数据已经在当前页面中\n\n      setViewPreloaded(preloadKey, true);\n    } catch (error) {\n      console.warn(`预加载视图 ${view} 失败:`, error);\n    } finally {\n      setViewPreloading(preloadKey, false);\n    }\n  }, [currentDate, getDailyStatsAsync, setViewPreloading, setViewPreloaded, isViewPreloading, isViewPreloaded]);\n\n  /**\n   * 切换视图（带预加载）\n   */\n  const switchView = useCallback((view: ViewType) => {\n    if (ui.currentView === view) return;\n\n    startTransition(async () => {\n      // 先预加载数据，再切换视图\n      await preloadView(view);\n      setCurrentView(view);\n    });\n  }, [ui.currentView, preloadView, setCurrentView]);\n\n  /**\n   * 处理鼠标进入事件（hover预加载）\n   */\n  const handleMouseEnter = useCallback((view: ViewType) => {\n    if (!enableHoverPreload || ui.currentView === view) return;\n\n    // 清除之前的定时器\n    if (preloadTimeoutRef.current) {\n      clearTimeout(preloadTimeoutRef.current);\n    }\n\n    // 延迟预加载，避免鼠标快速划过时的无效预加载\n    preloadTimeoutRef.current = setTimeout(() => {\n      preloadView(view);\n    }, preloadDelay);\n  }, [enableHoverPreload, ui.currentView, preloadView, preloadDelay]);\n\n  /**\n   * 处理鼠标离开事件\n   */\n  const handleMouseLeave = useCallback(() => {\n    if (preloadTimeoutRef.current) {\n      clearTimeout(preloadTimeoutRef.current);\n      preloadTimeoutRef.current = null;\n    }\n  }, []);\n\n  return {\n    isPreloading: isViewPreloading(`${ui.currentView}-${currentDate}`),\n    isPending,\n    preloadView,\n    switchView,\n    handleMouseEnter,\n    handleMouseLeave\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;AAyBO,SAAS,iBAAiB,UAAgC,CAAC,CAAC;IACjE,MAAM,EACJ,eAAe,GAAG,EAClB,qBAAqB,IAAI,EAC1B,GAAG;IAEJ,MAAM,EACJ,WAAW,EACX,kBAAkB,EAClB,cAAc,EACd,EAAE,EACF,iBAAiB,EACjB,gBAAgB,EAChB,eAAe,EACf,gBAAgB,EACjB,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IAEd,MAAM,CAAC,WAAW,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD;IACjD,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IACxD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAe,IAAI;IAE9C;;GAEC,GACD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACrC,MAAM,aAAa,GAAG,KAAK,CAAC,EAAE,aAAa;QAE3C,UAAU;QACV,IAAI,iBAAiB,eAAe,gBAAgB,aAAa;YAC/D;QACF;QAEA,kBAAkB,YAAY;QAE9B,IAAI;YACF,IAAI,SAAS,UAAU;gBACrB,eAAe;gBACf,MAAM,mBAAmB;gBAEzB,qBAAqB;gBACrB,MAAM,YAAY,IAAI,KAAK;gBAC3B,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK;gBACxC,MAAM,WAAW,IAAI,KAAK;gBAC1B,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK;gBAEtC,MAAM,QAAQ,GAAG,CAAC;oBAChB,mBAAmB,UAAU,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oBACxD,mBAAmB,SAAS,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;iBACxD;YACH;YACA,gCAAgC;YAEhC,iBAAiB,YAAY;QAC/B,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,EAAE;QACpC,SAAU;YACR,kBAAkB,YAAY;QAChC;IACF,GAAG;QAAC;QAAa;QAAoB;QAAmB;QAAkB;QAAkB;KAAgB;IAE5G;;GAEC,GACD,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,IAAI,GAAG,WAAW,KAAK,MAAM;QAE7B,gBAAgB;YACd,eAAe;YACf,MAAM,YAAY;YAClB,eAAe;QACjB;IACF,GAAG;QAAC,GAAG,WAAW;QAAE;QAAa;KAAe;IAEhD;;GAEC,GACD,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,IAAI,CAAC,sBAAsB,GAAG,WAAW,KAAK,MAAM;QAEpD,WAAW;QACX,IAAI,kBAAkB,OAAO,EAAE;YAC7B,aAAa,kBAAkB,OAAO;QACxC;QAEA,wBAAwB;QACxB,kBAAkB,OAAO,GAAG,WAAW;YACrC,YAAY;QACd,GAAG;IACL,GAAG;QAAC;QAAoB,GAAG,WAAW;QAAE;QAAa;KAAa;IAElE;;GAEC,GACD,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACnC,IAAI,kBAAkB,OAAO,EAAE;YAC7B,aAAa,kBAAkB,OAAO;YACtC,kBAAkB,OAAO,GAAG;QAC9B;IACF,GAAG,EAAE;IAEL,OAAO;QACL,cAAc,iBAAiB,GAAG,GAAG,WAAW,CAAC,CAAC,EAAE,aAAa;QACjE;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 13101, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/git_resp/Unfinished/time_blocks/chronospect/src/app/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useState } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { useAppStore } from '@/stores/useAppStore';\r\nimport { DateNavigator } from '@/components/DateNavigator';\r\nimport { TimeGrid } from '@/components/TimeGrid';\r\nimport { ReviewView } from '@/components/ReviewView';\r\nimport { TimeUpdaterDebugPanel } from '@/components/TimeUpdaterDebugPanel';\r\nimport { HydrationBoundary } from '@/components/HydrationBoundary';\r\nimport { ActivityManagementModal } from '@/components/ActivityManagementModal';\r\nimport { DataManagementDropdown } from '@/components/DataManagementDropdown';\r\nimport { DownloadTestPanel } from '@/components/DownloadTestPanel';\r\nimport { useViewPreloader } from '@/hooks/useViewPreloader';\r\n\r\nexport default function Home() {\r\n  const { initializeApp, ui } = useAppStore();\r\n  const [showActivityManagement, setShowActivityManagement] = useState(false);\r\n\r\n  // 使用智能预加载Hook\r\n  const {\r\n    isPending,\r\n    switchView,\r\n    handleMouseEnter,\r\n    handleMouseLeave\r\n  } = useViewPreloader();\r\n\r\n  // 初始化应用\r\n  useEffect(() => {\r\n    // 确保在客户端环境下初始化，并且在hydration完成后\r\n    if (typeof window !== 'undefined') {\r\n      // 使用requestAnimationFrame确保在hydration完成后初始化\r\n      requestAnimationFrame(() => {\r\n        setTimeout(() => {\r\n          initializeApp();\r\n        }, 50); // 减少延迟，但仍确保hydration完成\r\n      });\r\n    }\r\n  }, [initializeApp]);\r\n\r\n  return (\r\n    <div className=\"min-h-screen\" style={{\r\n      background: 'linear-gradient(135deg, var(--primary-50) 0%, white 50%, var(--info-50) 100%)'\r\n    }}>\r\n      {/* 头部 */}\r\n      <header className=\"bg-white/80 backdrop-blur-sm border-b sticky top-0 z-40\" style={{\r\n        borderColor: 'var(--neutral-200)',\r\n        boxShadow: 'var(--shadow-sm)'\r\n      }}>\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\" style={{ padding: 'var(--spacing-4) var(--spacing-6)' }}>\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <h1 style={{\r\n                fontSize: 'var(--font-size-2xl)',\r\n                fontWeight: 'var(--font-weight-bold)',\r\n                color: 'var(--neutral-900)',\r\n                lineHeight: 'var(--line-height-tight)'\r\n              }}>\r\n                Chronospect\r\n              </h1>\r\n              <p style={{\r\n                fontSize: 'var(--font-size-sm)',\r\n                color: 'var(--neutral-600)',\r\n                marginTop: 'var(--spacing-1)'\r\n              }}>\r\n                时间洞察 - 通过数据理解时间的真实去向\r\n              </p>\r\n            </div>\r\n\r\n            {/* 视图切换按钮和数据管理 */}\r\n            <div className=\"flex items-center\" style={{ gap: 'var(--spacing-2)' }}>\r\n              <motion.button\r\n                onClick={() => switchView('grid')}\r\n                onMouseEnter={() => handleMouseEnter('grid')}\r\n                onMouseLeave={handleMouseLeave}\r\n                className={`btn ${ui.currentView === 'grid' ? 'btn-primary' : 'btn-secondary'} ${\r\n                  isPending ? 'opacity-75 cursor-wait' : ''\r\n                }`}\r\n                whileHover={{ scale: 1.02 }}\r\n                whileTap={{ scale: 0.98 }}\r\n                transition={{ duration: 0.15 }}\r\n                disabled={isPending}\r\n              >\r\n                📊 网格视图\r\n              </motion.button>\r\n              <motion.button\r\n                onClick={() => switchView('review')}\r\n                onMouseEnter={() => handleMouseEnter('review')}\r\n                onMouseLeave={handleMouseLeave}\r\n                className={`btn ${ui.currentView === 'review' ? 'btn-primary' : 'btn-secondary'} ${\r\n                  isPending ? 'opacity-75 cursor-wait' : ''\r\n                }`}\r\n                whileHover={{ scale: 1.02 }}\r\n                whileTap={{ scale: 0.98 }}\r\n                transition={{ duration: 0.15 }}\r\n                disabled={isPending}\r\n              >\r\n                📈 复盘视图\r\n                {isPending && ui.currentView !== 'review' && (\r\n                  <motion.div\r\n                    className=\"inline-block ml-2 w-3 h-3 border border-current border-t-transparent rounded-full\"\r\n                    animate={{ rotate: 360 }}\r\n                    transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}\r\n                  />\r\n                )}\r\n              </motion.button>\r\n\r\n              {/* 数据管理下拉菜单 */}\r\n              <DataManagementDropdown />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </header>\r\n\r\n      {/* 主要内容 */}\r\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\" style={{ padding: 'var(--spacing-8) var(--spacing-6)' }}>\r\n        <HydrationBoundary fallback={\r\n          <div className=\"flex items-center justify-center py-12\">\r\n            <div className=\"text-center\">\r\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"></div>\r\n              <p className=\"text-gray-600\">加载中...</p>\r\n            </div>\r\n          </div>\r\n        }>\r\n          {/* 日期导航 */}\r\n          <DateNavigator onManageActivities={() => setShowActivityManagement(true)} />\r\n\r\n          {/* 根据当前视图显示不同内容 - 优化的无缝切换 */}\r\n          <div className=\"relative\">\r\n            {/* 网格视图 */}\r\n            <motion.div\r\n              className={ui.currentView === 'grid' ? 'block' : 'hidden'}\r\n              initial={false}\r\n              animate={{\r\n                opacity: ui.currentView === 'grid' ? 1 : 0,\r\n                y: ui.currentView === 'grid' ? 0 : 20\r\n              }}\r\n              transition={{\r\n                duration: 0.3,\r\n                ease: [0.4, 0, 0.2, 1]\r\n              }}\r\n            >\r\n              <TimeGrid />\r\n            </motion.div>\r\n\r\n            {/* 复盘视图 */}\r\n            <motion.div\r\n              className={ui.currentView === 'review' ? 'block' : 'hidden'}\r\n              initial={false}\r\n              animate={{\r\n                opacity: ui.currentView === 'review' ? 1 : 0,\r\n                y: ui.currentView === 'review' ? 0 : 20\r\n              }}\r\n              transition={{\r\n                duration: 0.3,\r\n                ease: [0.4, 0, 0.2, 1]\r\n              }}\r\n            >\r\n              <ReviewView />\r\n            </motion.div>\r\n          </div>\r\n        </HydrationBoundary>\r\n      </main>\r\n\r\n      {/* 活动管理模态框 */}\r\n      <ActivityManagementModal\r\n        isOpen={showActivityManagement}\r\n        onClose={() => setShowActivityManagement(false)}\r\n      />\r\n\r\n      {/* 开发环境调试面板 */}\r\n      <TimeUpdaterDebugPanel />\r\n\r\n      {/* 下载测试面板 - 仅在开发环境显示 */}\r\n      {process.env.NODE_ENV === 'development' && <DownloadTestPanel />}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAbA;;;;;;;;;;;;;;AAee,SAAS;IACtB,MAAM,EAAE,aAAa,EAAE,EAAE,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IACxC,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErE,cAAc;IACd,MAAM,EACJ,SAAS,EACT,UAAU,EACV,gBAAgB,EAChB,gBAAgB,EACjB,GAAG,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD;IAEnB,QAAQ;IACR,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,+BAA+B;QAC/B;;IAQF,GAAG;QAAC;KAAc;IAElB,qBACE,8OAAC;QAAI,WAAU;QAAe,OAAO;YACnC,YAAY;QACd;;0BAEE,8OAAC;gBAAO,WAAU;gBAA0D,OAAO;oBACjF,aAAa;oBACb,WAAW;gBACb;0BACE,cAAA,8OAAC;oBAAI,WAAU;oBAAyC,OAAO;wBAAE,SAAS;oBAAoC;8BAC5G,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,OAAO;4CACT,UAAU;4CACV,YAAY;4CACZ,OAAO;4CACP,YAAY;wCACd;kDAAG;;;;;;kDAGH,8OAAC;wCAAE,OAAO;4CACR,UAAU;4CACV,OAAO;4CACP,WAAW;wCACb;kDAAG;;;;;;;;;;;;0CAML,8OAAC;gCAAI,WAAU;gCAAoB,OAAO;oCAAE,KAAK;gCAAmB;;kDAClE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,SAAS,IAAM,WAAW;wCAC1B,cAAc,IAAM,iBAAiB;wCACrC,cAAc;wCACd,WAAW,CAAC,IAAI,EAAE,GAAG,WAAW,KAAK,SAAS,gBAAgB,gBAAgB,CAAC,EAC7E,YAAY,2BAA2B,IACvC;wCACF,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,YAAY;4CAAE,UAAU;wCAAK;wCAC7B,UAAU;kDACX;;;;;;kDAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,SAAS,IAAM,WAAW;wCAC1B,cAAc,IAAM,iBAAiB;wCACrC,cAAc;wCACd,WAAW,CAAC,IAAI,EAAE,GAAG,WAAW,KAAK,WAAW,gBAAgB,gBAAgB,CAAC,EAC/E,YAAY,2BAA2B,IACvC;wCACF,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,YAAY;4CAAE,UAAU;wCAAK;wCAC7B,UAAU;;4CACX;4CAEE,aAAa,GAAG,WAAW,KAAK,0BAC/B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,QAAQ;gDAAI;gDACvB,YAAY;oDAAE,UAAU;oDAAG,QAAQ;oDAAU,MAAM;gDAAS;;;;;;;;;;;;kDAMlE,8OAAC,4IAAA,CAAA,yBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO/B,8OAAC;gBAAK,WAAU;gBAAyC,OAAO;oBAAE,SAAS;gBAAoC;0BAC7G,cAAA,8OAAC,uIAAA,CAAA,oBAAiB;oBAAC,wBACjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;sCAKjC,8OAAC,mIAAA,CAAA,gBAAa;4BAAC,oBAAoB,IAAM,0BAA0B;;;;;;sCAGnE,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAW,GAAG,WAAW,KAAK,SAAS,UAAU;oCACjD,SAAS;oCACT,SAAS;wCACP,SAAS,GAAG,WAAW,KAAK,SAAS,IAAI;wCACzC,GAAG,GAAG,WAAW,KAAK,SAAS,IAAI;oCACrC;oCACA,YAAY;wCACV,UAAU;wCACV,MAAM;4CAAC;4CAAK;4CAAG;4CAAK;yCAAE;oCACxB;8CAEA,cAAA,8OAAC,8HAAA,CAAA,WAAQ;;;;;;;;;;8CAIX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAW,GAAG,WAAW,KAAK,WAAW,UAAU;oCACnD,SAAS;oCACT,SAAS;wCACP,SAAS,GAAG,WAAW,KAAK,WAAW,IAAI;wCAC3C,GAAG,GAAG,WAAW,KAAK,WAAW,IAAI;oCACvC;oCACA,YAAY;wCACV,UAAU;wCACV,MAAM;4CAAC;4CAAK;4CAAG;4CAAK;yCAAE;oCACxB;8CAEA,cAAA,8OAAC,gIAAA,CAAA,aAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOnB,8OAAC,6IAAA,CAAA,0BAAuB;gBACtB,QAAQ;gBACR,SAAS,IAAM,0BAA0B;;;;;;0BAI3C,8OAAC,2IAAA,CAAA,wBAAqB;;;;;YAGrB,oDAAyB,+BAAiB,8OAAC,uIAAA,CAAA,oBAAiB;;;;;;;;;;;AAGnE", "debugId": null}}]}